# TT Quant - Phoenix + Rust 算法交易平台

## 🎯 项目概述

TT Quant 是基于 Phoenix + Rust 技术栈构建的高性能算法交易平台，旨在重新实现 Nautilus Trader 的核心功能，并提供更好的用户体验和系统性能。

### 🚀 核心特性

- **高性能交易引擎**: Rust 实现的微秒级订单匹配引擎
- **实时 Web 界面**: Phoenix LiveView 提供的实时交易仪表板
- **多交易所支持**: 支持 Binance、OKX、Bybit 等主流交易所
- **智能风险管理**: 实时风险监控和预交易风险检查
- **策略回测框架**: 完整的策略开发和回测环境
- **分布式架构**: 基于 OTP 的高可用分布式系统

### 🏗️ 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Phoenix LiveView 层                      │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   交易仪表板     │ │   策略管理界面   │ │   风险监控面板   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Phoenix 业务层                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   交易管理       │ │   投资组合       │ │   配置管理       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  Rustler NIF 接口层                         │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   数据序列化     │ │   错误处理       │ │   性能监控       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Rust 核心引擎                            │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   订单匹配引擎   │ │   风险计算引擎   │ │   数据处理引擎   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 📊 当前开发状态

- **项目版本**: 0.1.0-dev
- **开发阶段**: 核心引擎开发阶段
- **完成进度**: 第二阶段 1.2 核心 Rust 引擎框架 (100% 完成)

#### ✅ 已完成功能

**基础架构 (1.1 阶段)**

- Phoenix 项目基础架构
- Rustler NIF 集成和配置
- 基础 Rust crate 结构 (`tt_quant_core`)
- 开发工具链配置 (代码格式化、质量检查)

**核心 Rust 引擎 (1.2 阶段) - 🎉 新完成**

- **基础数据类型**: Price、Quantity、Money、Currency 等核心类型
- **订单管理系统**: 完整的订单创建、验证、状态管理
- **金融工具定义**: Instrument 类型和交易规则验证
- **NIF 接口框架**: Elixir-Rust 高效通信接口
- **序列化系统**: 跨语言数据传输优化
- **错误处理机制**: 统一的错误分类和处理
- **多币种支持**: 支持 USD、EUR、GBP、JPY、**CNY(人民币)**、BTC、ETH、USDT、USDC
- **52 个单元测试**: 全面的功能测试覆盖

#### 🚧 正在进行

- 市场数据处理模块开发
- 风险管理系统设计

#### 📋 下一步计划

- 交易执行引擎开发
- Phoenix 业务层集成
- 实时 Web 界面开发

## 🚀 快速开始

### 环境要求

- **Elixir**: 1.15+
- **Rust**: 1.70+
- **PostgreSQL**: 15+
- **Redis**: 7+
- **Node.js**: 18+ (用于前端资源构建)

### 安装步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd tt_quant
```

2. **安装依赖**

```bash
# 安装 Elixir 依赖
mix deps.get

# 编译 Rust NIF
mix compile

# 安装前端依赖
cd assets && npm install && cd ..
```

3. **数据库设置**

```bash
# 创建数据库
mix ecto.create

# 运行迁移
mix ecto.migrate

# 种子数据 (可选)
mix run priv/repo/seeds.exs
```

4. **启动服务**

```bash
# 开发模式启动
mix phx.server

# 或者在 IEx 中启动
iex -S mix phx.server
```

访问 http://localhost:4000 查看应用。

### 验证安装

运行以下命令验证 Rustler NIF 集成是否正常工作：

```bash
# 启动 IEx
iex -S mix

# 测试基础 NIF 功能
iex> TtQuant.Core.add(1, 2)
3

# 测试价格创建 (支持人民币)
iex> TtQuant.Core.create_price(100.50, 2)
{:ok, <<price_binary>>}

# 测试货币创建
iex> TtQuant.Core.create_money(1000.0, "CNY")
{:ok, <<money_binary>>}
```

## 🌏 多币种支持

TT Quant 提供全面的多币种支持，特别针对中国市场优化：

### 法定货币

- **USD** - 美元 (精度: 2 位小数, ISO: 840)
- **EUR** - 欧元 (精度: 2 位小数, ISO: 978)
- **GBP** - 英镑 (精度: 2 位小数, ISO: 826)
- **JPY** - 日元 (精度: 0 位小数, ISO: 392)
- **CNY** - 人民币 (精度: 2 位小数, ISO: 156) 🇨🇳

### 加密货币

- **BTC** - 比特币 (精度: 8 位小数)
- **ETH** - 以太坊 (精度: 18 位小数)
- **USDT** - 泰达币 (精度: 6 位小数)
- **USDC** - USD Coin (精度: 6 位小数)

### 货币特性

- **类型安全**: 编译时货币类型检查
- **精确计算**: 基于固定精度算术，避免浮点误差
- **自动转换**: 支持汇率转换（需要汇率数据）
- **本地化**: 支持货币符号和格式化显示

## 🔧 开发指南

### 项目结构

```
tt_quant/
├── lib/
│   ├── tt_quant/              # 核心业务逻辑
│   │   └── core.ex            # Rustler NIF 接口
│   ├── tt_quant_web/          # Web 层
│   │   ├── controllers/       # API 控制器
│   │   ├── live/              # LiveView 组件
│   │   └── channels/          # WebSocket 频道
│   └── tt_quant.ex            # 应用入口
├── native/
│   └── tt_quant_core/         # Rust 核心引擎
│       ├── src/lib.rs         # Rust NIF 实现
│       └── Cargo.toml         # Rust 依赖配置
├── priv/
│   └── repo/migrations/       # 数据库迁移
├── test/                      # 测试文件
├── assets/                    # 前端资源
├── config/                    # 配置文件
├── Cargo.toml                 # Rust 工作区配置
└── mix.exs                    # Elixir 项目配置
```

### 开发工作流

1. **功能开发**

   - 先实现 Rust 核心逻辑
   - 然后添加 Phoenix 业务层
   - 最后实现 LiveView 界面

2. **测试策略**

   - 单元测试：Rust 和 Elixir 分别测试
   - 集成测试：Phoenix-Rust 通信测试
   - 性能测试：关键路径基准测试

3. **代码规范**
   - Rust: 使用 `cargo fmt` 和 `cargo clippy`
   - Elixir: 使用 `mix format` 和 `mix credo`

### 开发命令

项目提供了便捷的开发命令：

```bash
# 代码质量检查
mix code.check          # 检查 Elixir 代码格式和质量 (format --check-formatted + credo --strict)
mix rust.check          # 检查 Rust 代码格式和质量 (cargo fmt --check + cargo clippy)

# 代码自动修复
mix code.fix            # 修复 Elixir 代码格式和质量问题 (format + credo --strict)
mix rust.fix            # 修复 Rust 代码格式问题 (cargo fmt + cargo clippy --fix)

# 预提交检查
mix precommit           # 运行完整的预提交检查 (compile + deps + format + test)

# 项目设置和管理
mix setup               # 完整项目设置 (deps.get + ecto.setup + assets.setup + assets.build)
mix ecto.setup          # 数据库设置 (ecto.create + ecto.migrate + seeds)
mix ecto.reset          # 重置数据库 (ecto.drop + ecto.setup)

# 前端资源管理
mix assets.setup        # 安装前端工具 (tailwind + esbuild)
mix assets.build        # 构建前端资源 (tailwind + esbuild)
mix assets.deploy       # 生产环境资源构建 (minify + digest)
```

## 📚 文档

### 项目文档

- [架构设计文档](./tt_quant_docs/ARCHITECTURE_DESIGN.md) - 详细的系统架构设计
- [开发计划](./tt_quant_docs/DEVELOPMENT_PLAN.md) - 8 阶段详细开发计划
- [技术决策文档](./tt_quant_docs/TECHNICAL_DECISIONS.md) - 关键技术决策分析
- [Nautilus Trader 分析](./tt_quant_docs/NAUTILUS_TRADER_ANALYSIS.md) - 原系统分析

### 技术文档

- [Phoenix 官方文档](https://hexdocs.pm/phoenix/)
- [Rust 官方文档](https://doc.rust-lang.org/)
- [Rustler 文档](https://hexdocs.pm/rustler/)

## 🎯 性能目标

- **订单延迟**: < 100 微秒 (99th percentile)
- **市场数据处理**: > 100,000 ticks/秒
- **并发连接**: > 10,000 WebSocket 连接
- **系统可用性**: 99.9%

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码贡献规范

- 遵循项目代码风格
- 添加适当的测试
- 更新相关文档
- 确保 CI 检查通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持和帮助

### 社区支持

- GitHub Issues: 报告 Bug 和功能请求
- GitHub Discussions: 技术讨论和问答
- 项目 Wiki: 详细技术文档

---

**注意**: 本项目目前处于开发阶段，不建议用于生产环境。请在充分测试后再考虑实际使用。
