# Phoenix + Rust 新架构设计

## 🏗️ 整体架构图

```mermaid
graph TB
    subgraph "Phoenix Layer (Elixir/OTP)"
        UI[Web UI - LiveView]
        API[REST/GraphQL API]
        WS[WebSocket Gateway]
        BL[Business Logic]
        CONFIG[Configuration Management]
        AUTH[Authentication & Authorization]
        NOTIFY[Notification System]
        REPORT[Reporting Engine]
    end

    subgraph "Integration Layer (Rustler NIF)"
        NIF[Rustler NIFs]
        BRIDGE[Data Bridge]
        EVENTS[Event Dispatcher]
    end

    subgraph "Rust Core Layer"
        MATCH[Order Matching Engine]
        RISK[Risk Engine]
        DATA[Data Processing Engine]
        CACHE[High-Speed Cache]
        INDICATORS[Technical Indicators]
        PORTFOLIO[Portfolio Engine]
        ADAPTERS[Exchange Adapters]
        NETWORK[Network Clients]
    end

    subgraph "External Systems"
        EXCHANGES[Exchanges APIs]
        DB[(Database)]
        REDIS[(Redis Cache)]
        FILES[(File Storage)]
    end

    UI --> API
    UI --> WS
    API --> BL
    WS --> BL
    BL --> NIF
    NIF --> BRIDGE
    BRIDGE --> MATCH
    BRIDGE --> RISK
    BRIDGE --> DATA
    MATCH --> CACHE
    RISK --> PORTFOLIO
    DATA --> INDICATORS
    ADAPTERS --> NETWORK
    NETWORK --> EXCHANGES
    CONFIG --> DB
    CACHE --> REDIS
    REPORT --> FILES
```

## 🔧 模块详细设计

### 1. Phoenix Web 层

```elixir
# 项目结构
tt_quant/
├── lib/
│   ├── tt_quant/
│   │   ├── trading/           # 交易业务逻辑
│   │   ├── risk/             # 风险管理业务
│   │   ├── portfolio/        # 投资组合管理
│   │   ├── strategies/       # 策略管理
│   │   ├── adapters/         # 适配器管理
│   │   ├── config/           # 配置管理
│   │   └── notifications/    # 通知系统
│   ├── tt_quant_web/
│   │   ├── live/            # LiveView 组件
│   │   ├── controllers/     # API 控制器
│   │   ├── channels/        # WebSocket 频道
│   │   └── graphql/         # GraphQL 解析器
│   └── tt_quant_native/     # Rustler NIF
├── native/
│   └── trading_engine/      # Rust 核心引擎
└── assets/                  # 前端资源
```

### 2. 核心 LiveView 组件

```elixir
defmodule TtQuantWeb.TradingDashboardLive do
  use TtQuantWeb, :live_view
  
  def mount(_params, _session, socket) do
    # 订阅实时数据流
    TtQuant.PubSub.subscribe("market_data")
    TtQuant.PubSub.subscribe("order_updates")
    TtQuant.PubSub.subscribe("portfolio_updates")
    
    {:ok, assign(socket, 
      orders: [],
      positions: [],
      market_data: %{},
      pnl: %{}
    )}
  end
  
  def handle_info({:market_data, data}, socket) do
    # 实时更新市场数据
    {:noreply, assign(socket, :market_data, data)}
  end
  
  def handle_info({:order_update, order}, socket) do
    # 实时更新订单状态
    orders = update_order_list(socket.assigns.orders, order)
    {:noreply, assign(socket, :orders, orders)}
  end
end
```

### 3. Rust 核心引擎接口

```rust
// native/trading_engine/src/lib.rs
use rustler::{Atom, Binary, Encoder, Env, NifResult, Term};

#[rustler::nif]
fn submit_order(order_data: Binary) -> NifResult<Atom> {
    let order = parse_order(order_data)?;
    let result = TRADING_ENGINE.submit_order(order);
    
    match result {
        Ok(_) => Ok(atoms::ok()),
        Err(e) => Ok(atoms::error()),
    }
}

#[rustler::nif]
fn calculate_portfolio_risk(portfolio_data: Binary) -> NifResult<Binary> {
    let portfolio = parse_portfolio(portfolio_data)?;
    let risk_metrics = RISK_ENGINE.calculate_risk(&portfolio);
    Ok(serialize_risk_metrics(risk_metrics))
}

#[rustler::nif]
fn process_market_data(market_data: Binary) -> NifResult<Binary> {
    let data = parse_market_data(market_data)?;
    let processed = DATA_ENGINE.process(data);
    Ok(serialize_processed_data(processed))
}
```

### 4. 数据流管道设计

```elixir
defmodule TtQuant.DataPipeline do
  use GenStage
  
  def start_link(_) do
    GenStage.start_link(__MODULE__, :ok, name: __MODULE__)
  end
  
  def init(:ok) do
    {:producer, %{}}
  end
  
  def handle_demand(demand, state) do
    # 从 Rust 引擎获取数据
    events = TtQuantNative.get_market_events(demand)
    {:noreply, events, state}
  end
end

defmodule TtQuant.DataProcessor do
  use GenStage
  
  def start_link(_) do
    GenStage.start_link(__MODULE__, :ok, name: __MODULE__)
  end
  
  def init(:ok) do
    {:producer_consumer, %{}}
  end
  
  def handle_events(events, _from, state) do
    processed_events = Enum.map(events, fn event ->
      # 业务逻辑处理
      process_event(event)
    end)
    
    {:noreply, processed_events, state}
  end
end
```

## 🔄 通信机制设计

### 1. Phoenix 到 Rust 通信
```elixir
defmodule TtQuant.TradingEngine do
  @moduledoc "Trading engine interface"
  
  def submit_order(order) do
    order_binary = :erlang.term_to_binary(order)
    case TtQuantNative.submit_order(order_binary) do
      :ok -> {:ok, :submitted}
      :error -> {:error, :submission_failed}
    end
  end
  
  def get_portfolio_status() do
    case TtQuantNative.get_portfolio_status() do
      {:ok, binary} -> {:ok, :erlang.binary_to_term(binary)}
      {:error, reason} -> {:error, reason}
    end
  end
end
```

### 2. Rust 到 Phoenix 事件推送
```rust
// 在 Rust 中触发事件
pub fn notify_order_filled(order_id: &str, fill_data: &FillData) {
    let event = OrderFilledEvent {
        order_id: order_id.to_string(),
        fill_data: fill_data.clone(),
        timestamp: Utc::now(),
    };
    
    // 通过回调函数发送到 Phoenix
    PHOENIX_CALLBACK.send_event("order_filled", &event);
}
```

```elixir
# Phoenix 中接收事件
defmodule TtQuant.EventHandler do
  use GenServer
  
  def handle_cast({:rust_event, "order_filled", data}, state) do
    # 处理订单成交事件
    order_event = decode_order_event(data)
    
    # 广播到 LiveView
    TtQuantWeb.Endpoint.broadcast("orders", "order_filled", order_event)
    
    # 更新数据库
    TtQuant.Orders.update_order_status(order_event.order_id, :filled)
    
    {:noreply, state}
  end
end
```

## 📊 数据模型设计

### 1. Phoenix Schema 定义
```elixir
defmodule TtQuant.Trading.Order do
  use Ecto.Schema
  import Ecto.Changeset
  
  @primary_key {:id, :binary_id, autogenerate: true}
  schema "orders" do
    field :client_order_id, :string
    field :venue_order_id, :string
    field :instrument_id, :string
    field :side, Ecto.Enum, values: [:buy, :sell]
    field :order_type, Ecto.Enum, values: [:market, :limit, :stop, :stop_limit]
    field :quantity, :decimal
    field :price, :decimal
    field :status, Ecto.Enum, values: [:pending, :accepted, :filled, :cancelled]
    field :filled_quantity, :decimal, default: 0
    field :average_price, :decimal
    
    timestamps()
  end
end
```

### 2. Rust 数据结构
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Order {
    pub id: OrderId,
    pub client_order_id: String,
    pub instrument_id: InstrumentId,
    pub side: OrderSide,
    pub order_type: OrderType,
    pub quantity: Quantity,
    pub price: Option<Price>,
    pub status: OrderStatus,
    pub filled_quantity: Quantity,
    pub average_price: Option<Price>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct RiskMetrics {
    pub total_exposure: Money,
    pub available_margin: Money,
    pub position_value: Money,
    pub unrealized_pnl: Money,
    pub risk_score: f64,
}
```

## 🚀 部署架构

### Docker 容器化
```dockerfile
# Dockerfile
FROM elixir:1.15-alpine AS builder

# 安装 Rust
RUN apk add --no-cache rust cargo

# 构建应用
WORKDIR /app
COPY . .
RUN mix deps.get --only prod
RUN mix compile
RUN mix assets.deploy
RUN mix release

FROM alpine:3.18
RUN apk add --no-cache openssl ncurses-libs
WORKDIR /app
COPY --from=builder /app/_build/prod/rel/tt_quant ./
CMD ["./bin/tt_quant", "start"]
```

### Kubernetes 部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tt-quant
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tt-quant
  template:
    metadata:
      labels:
        app: tt-quant
    spec:
      containers:
      - name: tt-quant
        image: tt-quant:latest
        ports:
        - containerPort: 4000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: tt-quant-secrets
              key: database-url
```
