# TT Quant - Phoenix + Rust 算法交易平台

## 🎯 项目概述

TT Quant 是基于 Phoenix + Rust 技术栈重构的高性能算法交易平台，旨在重新实现 Nautilus Trader 的所有功能，并提供更好的用户体验和系统性能。

### 🚀 核心特性

- **高性能交易引擎**: Rust 实现的微秒级订单匹配引擎
- **实时 Web 界面**: Phoenix LiveView 提供的实时交易仪表板
- **多交易所支持**: 支持 Binance、OKX、Bybit 等主流交易所
- **智能风险管理**: 实时风险监控和预交易风险检查
- **策略回测框架**: 完整的策略开发和回测环境
- **分布式架构**: 基于 OTP 的高可用分布式系统

### 🏗️ 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Phoenix Web Layer                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │  LiveView   │ │  REST API   │ │    WebSocket API        │ │
│  │     UI      │ │   Service   │ │      Gateway            │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                    ┌─────────────────────┐
                    │   Rustler NIF       │
                    │  Integration Layer  │
                    └─────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Rust Core Engine                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   Order     │ │    Risk     │ │      Data Processing    │ │
│  │  Matching   │ │   Engine    │ │        Engine           │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ Portfolio   │ │ Technical   │ │    Exchange             │ │
│  │   Engine    │ │ Indicators  │ │     Adapters            │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📚 文档结构

本项目包含以下核心文档：

### 📋 [项目分析报告](./NAUTILUS_TRADER_ANALYSIS.md)
- Nautilus Trader 现有架构深度分析
- 核心功能模块详细清单
- Phoenix vs Rust 技术选型策略
- 性能关键路径识别

### 🏗️ [架构设计文档](./ARCHITECTURE_DESIGN.md)
- 整体架构设计图
- 模块详细设计
- 通信机制设计
- 数据模型设计
- 部署架构

### 📅 [开发计划](./DEVELOPMENT_PLAN.md)
- 8 阶段详细开发计划
- 关键里程碑定义
- 风险评估和缓解策略
- 团队配置建议

### 🎯 [技术决策文档](./TECHNICAL_DECISIONS.md)
- 关键技术决策点分析
- 决策选项评估
- 最终推荐方案
- 实现策略

## 🚀 快速开始

### 环境要求

- **Elixir**: 1.15+
- **Rust**: 1.70+
- **PostgreSQL**: 15+
- **Redis**: 7+
- **Node.js**: 18+ (用于前端资源构建)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd tt_quant_umbrella
```

2. **安装依赖**
```bash
# 安装 Elixir 依赖
mix deps.get

# 编译 Rust NIF
mix compile

# 安装前端依赖
cd assets && npm install && cd ..
```

3. **数据库设置**
```bash
# 创建数据库
mix ecto.create

# 运行迁移
mix ecto.migrate

# 种子数据 (可选)
mix run priv/repo/seeds.exs
```

4. **启动服务**
```bash
# 开发模式启动
mix phx.server

# 或者在 IEx 中启动
iex -S mix phx.server
```

访问 http://localhost:4000 查看应用。

## 🔧 开发指南

### 项目结构

```
tt_quant_umbrella/
├── lib/
│   ├── tt_quant/              # 核心业务逻辑
│   │   ├── trading/           # 交易相关
│   │   ├── risk/              # 风险管理
│   │   ├── portfolio/         # 投资组合
│   │   └── strategies/        # 策略管理
│   ├── tt_quant_web/          # Web 层
│   │   ├── live/              # LiveView 组件
│   │   ├── controllers/       # API 控制器
│   │   └── channels/          # WebSocket 频道
│   └── tt_quant_native/       # Rustler NIF 接口
├── native/
│   └── trading_engine/        # Rust 核心引擎
├── priv/
│   └── repo/migrations/       # 数据库迁移
├── test/                      # 测试文件
└── assets/                    # 前端资源
```

### 开发工作流

1. **功能开发**
   - 先实现 Rust 核心逻辑
   - 然后添加 Phoenix 业务层
   - 最后实现 LiveView 界面

2. **测试策略**
   - 单元测试：Rust 和 Elixir 分别测试
   - 集成测试：Phoenix-Rust 通信测试
   - 性能测试：关键路径基准测试

3. **代码规范**
   - Rust: 使用 `cargo fmt` 和 `cargo clippy`
   - Elixir: 使用 `mix format` 和 `mix credo`

## 📊 性能指标

### 目标性能

- **订单延迟**: < 100 微秒 (99th percentile)
- **市场数据处理**: > 100,000 ticks/秒
- **并发连接**: > 10,000 WebSocket 连接
- **系统可用性**: 99.9%

### 监控指标

- 订单处理延迟
- 市场数据吞吐量
- 内存使用情况
- CPU 使用率
- 网络 I/O

## 🔒 安全考虑

### 数据安全
- API 密钥加密存储
- 数据库连接加密
- 敏感数据脱敏

### 访问控制
- 基于角色的权限控制
- API 访问限流
- 审计日志记录

### 网络安全
- HTTPS/WSS 强制加密
- CORS 策略配置
- DDoS 防护

## 🚀 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t tt-quant .

# 运行容器
docker run -p 4000:4000 tt-quant
```

### Kubernetes 部署

```bash
# 应用配置
kubectl apply -f k8s/

# 检查状态
kubectl get pods -l app=tt-quant
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码贡献规范

- 遵循项目代码风格
- 添加适当的测试
- 更新相关文档
- 确保 CI 检查通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持和帮助

### 文档资源
- [Phoenix 官方文档](https://hexdocs.pm/phoenix/)
- [Rust 官方文档](https://doc.rust-lang.org/)
- [Rustler 文档](https://hexdocs.pm/rustler/)

### 社区支持
- GitHub Issues: 报告 Bug 和功能请求
- GitHub Discussions: 技术讨论和问答
- 项目 Wiki: 详细技术文档

### 联系方式
- 项目维护者: [维护者信息]
- 技术支持: [支持邮箱]

## 🎯 路线图

### 短期目标 (3-6 个月)
- [ ] 完成核心交易引擎
- [ ] 实现基础 Web 界面
- [ ] 集成主要交易所

### 中期目标 (6-12 个月)
- [ ] 完整策略框架
- [ ] 高级分析工具
- [ ] 移动端支持

### 长期目标 (12+ 个月)
- [ ] 机器学习集成
- [ ] 多资产类别支持
- [ ] 企业级功能

## 📈 项目状态

- **当前版本**: 0.1.0-dev
- **开发状态**: 架构设计阶段
- **测试覆盖率**: 目标 > 90%
- **文档完整性**: 目标 > 95%

---

**注意**: 本项目目前处于开发阶段，不建议用于生产环境。请在充分测试后再考虑实际使用。
