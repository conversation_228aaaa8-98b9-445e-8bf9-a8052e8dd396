[package]
name = "tt_quant_core"
version = "0.1.0"
authors = []
edition = "2021"

[lib]
name = "tt_quant_core"
crate-type = ["cdylib"]

[dependencies]
rustler = "0.36.2"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rust_decimal = { version = "1.32", features = ["serde-with-str"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
anyhow = "1.0"
thiserror = "1.0"
