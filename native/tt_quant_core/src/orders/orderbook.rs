use std::collections::{BTreeMap, HashMap, VecDeque};
use std::sync::{Arc, RwLock};

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::errors::{TtQuantError, TtQuantResult};
use crate::orders::{
    InstrumentId, LiquiditySide, Order, OrderFill, OrderSide,
    OrderType,
};
use crate::types::{Price, Quantity};

/// Order book side (bid or ask)
#[derive(Debug, Clone)]
pub struct OrderBookSide {
    /// Price levels mapped to order queues
    /// For bids: highest price first (descending)
    /// For asks: lowest price first (ascending)
    levels: BTreeMap<Price, OrderQueue>,
    /// Quick lookup from order_id to price level
    order_price_map: HashMap<String, Price>,
    /// Total volume across all levels
    total_volume: Quantity,
    /// Number of orders
    order_count: usize,
    /// Side type
    side: OrderSide,
}

impl OrderBookSide {
    pub fn new(side: OrderSide, precision: u8) -> Self {
        Self {
            levels: BTreeMap::new(),
            order_price_map: HashMap::new(),
            total_volume: Quantity::zero(precision).unwrap_or(Quantity::from_raw(0, precision).unwrap()),
            order_count: 0,
            side,
        }
    }

    /// Add an order to the book side
    pub fn add_order(&mut self, order: &Order) -> TtQuantResult<()> {
        let price = order
            .price
            .ok_or_else(|| TtQuantError::order_error("Limit order must have price"))?;

        // Add to price level
        let queue = self.levels.entry(price).or_insert_with(|| OrderQueue::new());
        queue.add_order(order.clone());

        // Update lookup map
        self.order_price_map
            .insert(order.id.client_order_id.clone(), price);

        // Update statistics
        self.total_volume = (self.total_volume + order.quantity)
            .map_err(|e| TtQuantError::calculation_error(e.to_string()))?;
        self.order_count += 1;

        Ok(())
    }

    /// Remove an order from the book side
    pub fn remove_order(&mut self, order_id: &str) -> TtQuantResult<Option<Order>> {
        // Find the price level
        let price = self
            .order_price_map
            .remove(order_id)
            .ok_or_else(|| TtQuantError::order_error("Order not found in book"))?;

        // Remove from price level
        if let Some(queue) = self.levels.get_mut(&price) {
            if let Some(order) = queue.remove_order(order_id) {
                // Update statistics
                self.total_volume = (self.total_volume - order.quantity)
                    .map_err(|e| TtQuantError::calculation_error(e.to_string()))?;
                self.order_count -= 1;

                // Remove empty level
                if queue.is_empty() {
                    self.levels.remove(&price);
                }

                return Ok(Some(order));
            }
        }

        Ok(None)
    }

    /// Get the best price on this side
    pub fn best_price(&self) -> Option<Price> {
        if self.side == OrderSide::Buy {
            self.levels.keys().next_back().copied() // Highest price for bids
        } else {
            self.levels.keys().next().copied() // Lowest price for asks
        }
    }

    /// Get best level (price and total quantity)
    pub fn best_level(&self) -> Option<(Price, Quantity)> {
        self.best_price().and_then(|price| {
            self.levels
                .get(&price)
                .map(|queue| (price, queue.total_quantity))
        })
    }

    /// Get depth levels up to max_levels
    pub fn depth(&self, max_levels: usize) -> Vec<(Price, Quantity, usize)> {
        let iter: Box<dyn Iterator<Item = _>> = if self.side == OrderSide::Buy {
            Box::new(self.levels.iter().rev())
        } else {
            Box::new(self.levels.iter())
        };

        iter.take(max_levels)
            .map(|(price, queue)| (*price, queue.total_quantity, queue.orders.len()))
            .collect()
    }

    /// Match aggressive order against this side
    pub fn match_order(
        &mut self,
        aggressive_order: &mut Order,
        timestamp: DateTime<Utc>,
    ) -> TtQuantResult<Vec<OrderFill>> {
        let mut fills = Vec::new();
        let mut levels_to_remove = Vec::new();

        // Iterate through price levels
        let price_levels: Vec<Price> = if self.side == OrderSide::Buy {
            self.levels.keys().rev().copied().collect()
        } else {
            self.levels.keys().copied().collect()
        };

        for price in price_levels {
            // Check if we can match at this price
            if !Self::can_match(aggressive_order, price, self.side) {
                break;
            }

            // Match against orders in queue
            if let Some(queue) = self.levels.get_mut(&price) {
                let level_fills = queue.match_order(aggressive_order, price, timestamp)?;
                fills.extend(level_fills);

                // Mark empty levels for removal
                if queue.is_empty() {
                    levels_to_remove.push(price);
                }
            }

            // Check if aggressive order is fully filled
            if aggressive_order.remaining_quantity.is_zero() {
                break;
            }
        }

        // Remove empty levels
        for price in levels_to_remove {
            self.levels.remove(&price);
            // Also clean up order_price_map for removed orders
        }

        Ok(fills)
    }

    /// Check if aggressive order can match at given price
    fn can_match(aggressive_order: &Order, passive_price: Price, passive_side: OrderSide) -> bool {
        match aggressive_order.order_type {
            OrderType::Market => true,
            OrderType::Limit => {
                if let Some(limit_price) = aggressive_order.price {
                    if passive_side == OrderSide::Buy {
                        // Aggressive sell vs passive buy
                        limit_price <= passive_price
                    } else {
                        // Aggressive buy vs passive sell
                        limit_price >= passive_price
                    }
                } else {
                    false
                }
            }
            _ => false, // Other order types not supported yet
        }
    }

    /// Clear all orders
    pub fn clear(&mut self) {
        self.levels.clear();
        self.order_price_map.clear();
        self.total_volume = Quantity::zero(self.total_volume.precision)
            .unwrap_or(Quantity::from_raw(0, self.total_volume.precision).unwrap());
        self.order_count = 0;
    }
}

/// Queue of orders at a specific price level
#[derive(Debug, Clone)]
pub struct OrderQueue {
    orders: VecDeque<Order>,
    total_quantity: Quantity,
}

impl OrderQueue {
    pub fn new() -> Self {
        Self {
            orders: VecDeque::new(),
            total_quantity: Quantity::zero(8).unwrap_or(Quantity::from_raw(0, 8).unwrap()), // Will be updated on first order
        }
    }

    pub fn add_order(&mut self, order: Order) {
        if self.orders.is_empty() {
            self.total_quantity = Quantity::zero(order.quantity.precision)
                .unwrap_or(Quantity::from_raw(0, order.quantity.precision).unwrap());
        }
        self.total_quantity = (self.total_quantity + order.quantity)
            .unwrap_or(self.total_quantity);
        self.orders.push_back(order);
    }

    pub fn remove_order(&mut self, order_id: &str) -> Option<Order> {
        if let Some(pos) = self
            .orders
            .iter()
            .position(|o| o.id.client_order_id == order_id)
        {
            let order = self.orders.remove(pos)?;
            self.total_quantity = (self.total_quantity - order.quantity)
                .unwrap_or(self.total_quantity);
            Some(order)
        } else {
            None
        }
    }

    pub fn is_empty(&self) -> bool {
        self.orders.is_empty()
    }

    /// Match aggressive order against orders in this queue
    pub fn match_order(
        &mut self,
        aggressive_order: &mut Order,
        price: Price,
        timestamp: DateTime<Utc>,
    ) -> TtQuantResult<Vec<OrderFill>> {
        let mut fills = Vec::new();
        let mut filled_orders = Vec::new();

        for (idx, passive_order) in self.orders.iter_mut().enumerate() {
            if aggressive_order.remaining_quantity.is_zero() {
                break;
            }

            // Calculate fill quantity
            let fill_quantity = aggressive_order
                .remaining_quantity
                .min(passive_order.remaining_quantity)?;

            // Create fills for both orders
            let aggressive_fill = OrderFill {
                fill_id: Uuid::new_v4().to_string(),
                order_id: aggressive_order.id.client_order_id.clone(),
                instrument_id: aggressive_order.instrument_id.to_string(),
                quantity: fill_quantity,
                price,
                commission: None, // Will be calculated by commission model
                timestamp,
                liquidity_side: LiquiditySide::Taker,
            };

            let passive_fill = OrderFill {
                fill_id: Uuid::new_v4().to_string(),
                order_id: passive_order.id.client_order_id.clone(),
                instrument_id: passive_order.instrument_id.to_string(),
                quantity: fill_quantity,
                price,
                commission: None,
                timestamp,
                liquidity_side: LiquiditySide::Maker,
            };

            // Update order states
            aggressive_order.add_fill(fill_quantity, price)?;
            passive_order.add_fill(fill_quantity, price)?;

            fills.push(aggressive_fill);
            fills.push(passive_fill);

            // Mark fully filled passive orders for removal
            if passive_order.remaining_quantity.is_zero() {
                filled_orders.push(idx);
            }
        }

        // Remove fully filled orders (in reverse to maintain indices)
        for idx in filled_orders.into_iter().rev() {
            if let Some(order) = self.orders.remove(idx) {
                self.total_quantity = (self.total_quantity - order.quantity)
                    .unwrap_or(self.total_quantity);
            }
        }

        Ok(fills)
    }
}

/// Complete order book with bid and ask sides
#[derive(Debug)]
pub struct OrderBook {
    pub instrument_id: InstrumentId,
    pub bids: OrderBookSide,
    pub asks: OrderBookSide,
    pub last_update: DateTime<Utc>,
    pub sequence: u64,
    precision: u8,
}

impl OrderBook {
    pub fn new(instrument_id: InstrumentId, precision: u8) -> Self {
        Self {
            instrument_id,
            bids: OrderBookSide::new(OrderSide::Buy, precision),
            asks: OrderBookSide::new(OrderSide::Sell, precision),
            last_update: Utc::now(),
            sequence: 0,
            precision,
        }
    }

    /// Add order to the book
    pub fn add_order(&mut self, order: Order) -> TtQuantResult<Vec<OrderFill>> {
        // Validate order
        if order.order_type != OrderType::Limit && order.order_type != OrderType::Market {
            return Err(TtQuantError::order_error(
                "Only limit and market orders supported",
            ));
        }

        let mut order = order;
        let mut fills = Vec::new();

        // Try to match immediately
        if self.can_match_immediately(&order) {
            fills = self.match_order(&mut order)?;
        }

        // Add remaining quantity to book if limit order
        if !order.remaining_quantity.is_zero() && order.order_type == OrderType::Limit {
            match order.side {
                OrderSide::Buy => self.bids.add_order(&order)?,
                OrderSide::Sell => self.asks.add_order(&order)?,
            }
        }

        self.sequence += 1;
        self.last_update = Utc::now();

        Ok(fills)
    }

    /// Check if order can match immediately
    fn can_match_immediately(&self, order: &Order) -> bool {
        match order.side {
            OrderSide::Buy => {
                if let Some(best_ask) = self.asks.best_price() {
                    match order.order_type {
                        OrderType::Market => true,
                        OrderType::Limit => {
                            order.price.map_or(false, |price| price >= best_ask)
                        }
                        _ => false,
                    }
                } else {
                    false
                }
            }
            OrderSide::Sell => {
                if let Some(best_bid) = self.bids.best_price() {
                    match order.order_type {
                        OrderType::Market => true,
                        OrderType::Limit => {
                            order.price.map_or(false, |price| price <= best_bid)
                        }
                        _ => false,
                    }
                } else {
                    false
                }
            }
        }
    }

    /// Match order against the book
    fn match_order(&mut self, order: &mut Order) -> TtQuantResult<Vec<OrderFill>> {
        let timestamp = Utc::now();
        
        match order.side {
            OrderSide::Buy => self.asks.match_order(order, timestamp),
            OrderSide::Sell => self.bids.match_order(order, timestamp),
        }
    }

    /// Cancel order
    pub fn cancel_order(&mut self, order_id: &str, side: OrderSide) -> TtQuantResult<Option<Order>> {
        let result = match side {
            OrderSide::Buy => self.bids.remove_order(order_id),
            OrderSide::Sell => self.asks.remove_order(order_id),
        };

        if result.is_ok() {
            self.sequence += 1;
            self.last_update = Utc::now();
        }

        result
    }

    /// Update order (cancel and replace)
    pub fn update_order(
        &mut self,
        order_id: &str,
        side: OrderSide,
        new_order: Order,
    ) -> TtQuantResult<Vec<OrderFill>> {
        // Cancel old order
        self.cancel_order(order_id, side)?;
        
        // Add new order
        self.add_order(new_order)
    }

    /// Get current spread
    pub fn spread(&self) -> Option<Price> {
        match (self.bids.best_price(), self.asks.best_price()) {
            (Some(best_bid), Some(best_ask)) => {
                (best_ask - best_bid).ok()
            }
            _ => None,
        }
    }

    /// Get mid price
    pub fn mid_price(&self) -> Option<Price> {
        match (self.bids.best_price(), self.asks.best_price()) {
            (Some(best_bid), Some(best_ask)) => {
                let mid = (best_bid.as_f64() + best_ask.as_f64()) / 2.0;
                Price::new(mid, self.precision).ok()
            }
            _ => None,
        }
    }

    /// Get market depth snapshot
    pub fn depth(&self, levels: usize) -> OrderBookSnapshot {
        OrderBookSnapshot {
            instrument_id: self.instrument_id.clone(),
            bid_levels: self.bids.depth(levels),
            ask_levels: self.asks.depth(levels),
            last_update: self.last_update,
            sequence: self.sequence,
        }
    }

    /// Clear the book
    pub fn clear(&mut self) {
        self.bids.clear();
        self.asks.clear();
        self.sequence += 1;
        self.last_update = Utc::now();
    }
}

/// Order book snapshot for external consumption
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrderBookSnapshot {
    pub instrument_id: InstrumentId,
    pub bid_levels: Vec<(Price, Quantity, usize)>, // price, total_qty, order_count
    pub ask_levels: Vec<(Price, Quantity, usize)>,
    pub last_update: DateTime<Utc>,
    pub sequence: u64,
}

/// Thread-safe order book manager
pub struct OrderBookManager {
    books: Arc<RwLock<HashMap<InstrumentId, OrderBook>>>,
}

impl OrderBookManager {
    pub fn new() -> Self {
        Self {
            books: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Get or create order book for instrument
    pub fn get_or_create_book(&self, instrument_id: InstrumentId, precision: u8) -> TtQuantResult<()> {
        let mut books = self.books.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        books.entry(instrument_id.clone())
            .or_insert_with(|| OrderBook::new(instrument_id, precision));
        
        Ok(())
    }

    /// Process order
    pub fn process_order(&self, order: Order) -> TtQuantResult<Vec<OrderFill>> {
        let mut books = self.books.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let book = books.get_mut(&order.instrument_id)
            .ok_or_else(|| TtQuantError::order_error("Order book not found"))?;
        
        book.add_order(order)
    }

    /// Cancel order
    pub fn cancel_order(
        &self,
        instrument_id: &InstrumentId,
        order_id: &str,
        side: OrderSide,
    ) -> TtQuantResult<Option<Order>> {
        let mut books = self.books.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let book = books.get_mut(instrument_id)
            .ok_or_else(|| TtQuantError::order_error("Order book not found"))?;
        
        book.cancel_order(order_id, side)
    }

    /// Get market depth
    pub fn get_depth(&self, instrument_id: &InstrumentId, levels: usize) -> TtQuantResult<OrderBookSnapshot> {
        let books = self.books.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let book = books.get(instrument_id)
            .ok_or_else(|| TtQuantError::order_error("Order book not found"))?;
        
        Ok(book.depth(levels))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_order(side: OrderSide, price: f64, quantity: f64) -> Order {
        Order::builder()
            .client_order_id(format!("test-{}", Uuid::new_v4()))
            .instrument_id(InstrumentId::new("BTCUSD".to_string(), "test".to_string()))
            .side(side)
            .order_type(OrderType::Limit)
            .quantity(Quantity::new(quantity, 8).unwrap())
            .price(Some(Price::new(price, 2).unwrap()))
            .build()
            .unwrap()
    }

    #[test]
    fn test_orderbook_add_and_match() {
        let mut book = OrderBook::new(
            InstrumentId::new("BTCUSD".to_string(), "test".to_string()),
            2,
        );

        // Add buy orders
        let buy1 = create_test_order(OrderSide::Buy, 50000.0, 1.0);
        let buy2 = create_test_order(OrderSide::Buy, 49900.0, 2.0);
        book.add_order(buy1).unwrap();
        book.add_order(buy2).unwrap();

        // Add sell orders
        let sell1 = create_test_order(OrderSide::Sell, 50100.0, 1.5);
        let sell2 = create_test_order(OrderSide::Sell, 50200.0, 2.5);
        book.add_order(sell1).unwrap();
        book.add_order(sell2).unwrap();

        // Check spread
        let spread = book.spread().unwrap();
        assert_eq!(spread.as_f64(), 100.0); // 50100 - 50000

        // Place crossing order
        let mut cross_buy = create_test_order(OrderSide::Buy, 50100.0, 1.0);
        let fills = book.add_order(cross_buy).unwrap();
        
        assert_eq!(fills.len(), 2); // One for buyer, one for seller
        assert_eq!(fills[0].quantity.as_f64(), 1.0);
    }

    #[test]
    fn test_orderbook_cancel() {
        let mut book = OrderBook::new(
            InstrumentId::new("BTCUSD".to_string(), "test".to_string()),
            2,
        );

        let order = create_test_order(OrderSide::Buy, 50000.0, 1.0);
        let order_id = order.id.client_order_id.clone();
        
        book.add_order(order).unwrap();
        assert!(book.bids.best_price().is_some());

        // Cancel order
        let cancelled = book.cancel_order(&order_id, OrderSide::Buy).unwrap();
        assert!(cancelled.is_some());
        assert!(book.bids.best_price().is_none());
    }
}
