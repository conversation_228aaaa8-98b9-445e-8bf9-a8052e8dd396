use std::collections::HashMap;
use std::sync::{<PERSON>, Mutex, RwLock};
use std::sync::mpsc::{channel, Receiver, Sender};
use std::time::Duration;

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use crate::errors::{TtQuantError, TtQuantResult};
use crate::orders::{
    Order, OrderBookManager, OrderBookSnapshot, OrderEvent, OrderEventHandler,
    OrderFill, OrderLifecycleManager,
};

/// Sync event types for Phoenix communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SyncEvent {
    OrderCreated {
        order: Order,
        timestamp: DateTime<Utc>,
    },
    OrderUpdated {
        order: Order,
        event: OrderEvent,
        timestamp: DateTime<Utc>,
    },
    OrderBookUpdate {
        instrument_id: String,
        snapshot: OrderBookSnapshot,
        timestamp: DateTime<Utc>,
    },
    FillExecuted {
        order_id: String,
        fill: OrderFill,
        timestamp: DateTime<Utc>,
    },
    SystemStatus {
        active_orders: usize,
        pending_orders: usize,
        total_fills: usize,
        timestamp: DateTime<Utc>,
    },
}

/// Order synchronization manager
pub struct OrderSyncManager {
    /// Lifecycle manager
    lifecycle: Arc<OrderLifecycleManager>,
    /// Order book manager
    orderbook: Arc<OrderBookManager>,
    /// Event queue for Phoenix
    event_queue: Arc<Mutex<Vec<SyncEvent>>>,
    /// Event sender
    event_sender: Sender<SyncEvent>,
    /// Event receiver
    event_receiver: Arc<Mutex<Receiver<SyncEvent>>>,
    /// Sync state
    is_running: Arc<RwLock<bool>>,
}

impl OrderSyncManager {
    pub fn new(
        lifecycle: Arc<OrderLifecycleManager>,
        orderbook: Arc<OrderBookManager>,
    ) -> Self {
        let (sender, receiver) = channel();
        
        Self {
            lifecycle,
            orderbook,
            event_queue: Arc::new(Mutex::new(Vec::new())),
            event_sender: sender,
            event_receiver: Arc::new(Mutex::new(receiver)),
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    /// Start sync manager
    pub fn start(&self) -> TtQuantResult<()> {
        let mut is_running = self.is_running.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        *is_running = true;
        
        // Register event handler for lifecycle events
        let sender = self.event_sender.clone();
        self.lifecycle.register_event_handler(
            Box::new(SyncEventHandler::new(sender))
        )?;
        
        Ok(())
    }

    /// Stop sync manager
    pub fn stop(&self) -> TtQuantResult<()> {
        let mut is_running = self.is_running.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        *is_running = false;
        Ok(())
    }

    /// Process order from Phoenix
    pub fn process_order(&self, order: Order) -> TtQuantResult<Vec<OrderFill>> {
        // Register with lifecycle manager
        self.lifecycle.register_order(order.clone())?;
        
        // Process in orderbook
        let fills = self.orderbook.process_order(order.clone())?;
        
        // Send sync event
        let event = SyncEvent::OrderCreated {
            order,
            timestamp: Utc::now(),
        };
        self.send_event(event)?;
        
        // Send fill events
        for fill in &fills {
            let fill_event = SyncEvent::FillExecuted {
                order_id: fill.order_id.clone(),
                fill: fill.clone(),
                timestamp: Utc::now(),
            };
            self.send_event(fill_event)?;
        }
        
        Ok(fills)
    }

    /// Cancel order
    pub fn cancel_order(&self, order_id: &str, instrument_id: &str, side: crate::orders::OrderSide) -> TtQuantResult<()> {
        // Cancel in lifecycle
        self.lifecycle.cancel_order(order_id, Some("User requested".to_string()))?;
        
        // Cancel in orderbook
        let instrument = crate::orders::InstrumentId::new(
            instrument_id.to_string(),
            "".to_string(),
        );
        self.orderbook.cancel_order(&instrument, order_id, side)?;
        
        Ok(())
    }

    /// Get order book snapshot
    pub fn get_orderbook_snapshot(&self, instrument_id: &str, levels: usize) -> TtQuantResult<OrderBookSnapshot> {
        let instrument = crate::orders::InstrumentId::new(
            instrument_id.to_string(),
            "".to_string(),
        );
        self.orderbook.get_depth(&instrument, levels)
    }

    /// Get pending sync events
    pub fn get_pending_events(&self) -> TtQuantResult<Vec<SyncEvent>> {
        let mut queue = self.event_queue.lock()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        let events = queue.clone();
        queue.clear();
        Ok(events)
    }

    /// Send event to queue
    fn send_event(&self, event: SyncEvent) -> TtQuantResult<()> {
        let mut queue = self.event_queue.lock()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        queue.push(event.clone());
        
        // Also send through channel for real-time processing
        self.event_sender.send(event)
            .map_err(|e| TtQuantError::internal_error(e.to_string()))?;
        
        Ok(())
    }

    /// Get system status
    pub fn get_system_status(&self) -> TtQuantResult<SyncEvent> {
        let all_orders = self.lifecycle.get_all_orders()?;
        let active_orders = all_orders.iter()
            .filter(|o| o.status.is_active())
            .count();
        let pending_orders = all_orders.iter()
            .filter(|o| o.status.is_pending())
            .count();
        
        Ok(SyncEvent::SystemStatus {
            active_orders,
            pending_orders,
            total_fills: 0, // Would need to track this separately
            timestamp: Utc::now(),
        })
    }
}

/// Event handler for sync events
struct SyncEventHandler {
    sender: Sender<SyncEvent>,
}

impl SyncEventHandler {
    fn new(sender: Sender<SyncEvent>) -> Self {
        Self { sender }
    }
}

impl OrderEventHandler for SyncEventHandler {
    fn handle_event(&self, event: &OrderEvent) -> TtQuantResult<()> {
        // Get order details from event
        let order_id = match event {
            OrderEvent::Initialized { order_id, .. } |
            OrderEvent::Submitted { order_id, .. } |
            OrderEvent::Accepted { order_id, .. } |
            OrderEvent::Rejected { order_id, .. } |
            OrderEvent::Cancelled { order_id, .. } |
            OrderEvent::Expired { order_id, .. } |
            OrderEvent::Triggered { order_id, .. } |
            OrderEvent::Filled { order_id, .. } |
            OrderEvent::PartiallyFilled { order_id, .. } |
            OrderEvent::Updated { order_id, .. } => order_id.clone(),
        };
        
        // Send sync event (in real implementation, would get full order details)
        let sync_event = match event {
            OrderEvent::Filled { fill, .. } |
            OrderEvent::PartiallyFilled { fill, .. } => {
                SyncEvent::FillExecuted {
                    order_id,
                    fill: fill.clone(),
                    timestamp: Utc::now(),
                }
            }
            _ => {
                // For other events, we'd need to fetch the order and send update
                // This is simplified for now
                return Ok(());
            }
        };
        
        self.sender.send(sync_event)
            .map_err(|e| TtQuantError::internal_error(e.to_string()))?;
        
        Ok(())
    }
}

/// Persistence layer for order state
pub struct OrderPersistence {
    /// Storage backend (simplified - in real system would use database)
    storage: Arc<RwLock<HashMap<String, StoredOrder>>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct StoredOrder {
    order: Order,
    events: Vec<OrderEvent>,
    fills: Vec<OrderFill>,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
}

impl OrderPersistence {
    pub fn new() -> Self {
        Self {
            storage: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Save order state
    pub fn save_order(&self, order: &Order, events: Vec<OrderEvent>, fills: Vec<OrderFill>) -> TtQuantResult<()> {
        let mut storage = self.storage.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let stored = StoredOrder {
            order: order.clone(),
            events,
            fills,
            created_at: order.created_at,
            updated_at: Utc::now(),
        };
        
        storage.insert(order.id.client_order_id.clone(), stored);
        Ok(())
    }

    /// Load order state
    pub fn load_order(&self, order_id: &str) -> TtQuantResult<Option<(Order, Vec<OrderEvent>, Vec<OrderFill>)>> {
        let storage = self.storage.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        if let Some(stored) = storage.get(order_id) {
            Ok(Some((
                stored.order.clone(),
                stored.events.clone(),
                stored.fills.clone(),
            )))
        } else {
            Ok(None)
        }
    }

    /// Get all stored orders
    pub fn get_all_orders(&self) -> TtQuantResult<Vec<Order>> {
        let storage = self.storage.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        Ok(storage.values().map(|s| s.order.clone()).collect())
    }

    /// Delete order
    pub fn delete_order(&self, order_id: &str) -> TtQuantResult<()> {
        let mut storage = self.storage.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        storage.remove(order_id);
        Ok(())
    }

    /// Clean up old orders
    pub fn cleanup_old_orders(&self, older_than: Duration) -> TtQuantResult<usize> {
        let mut storage = self.storage.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let cutoff = Utc::now() - chrono::Duration::from_std(older_than)
            .map_err(|e| TtQuantError::internal_error(e.to_string()))?;
        
        let to_remove: Vec<String> = storage
            .iter()
            .filter(|(_, stored)| stored.updated_at < cutoff)
            .map(|(id, _)| id.clone())
            .collect();
        
        let count = to_remove.len();
        for id in to_remove {
            storage.remove(&id);
        }
        
        Ok(count)
    }
}

/// NIF bridge functions for Phoenix integration
pub mod nif_bridge {
    use super::*;
    use rustler::{Env, Term, Binary};
    use crate::serialization::nif_helpers;

    /// Process order from Phoenix
    pub fn process_order_nif<'a>(env: Env<'a>, order_binary: Binary<'a>) -> Term<'a> {
        // Deserialize order
        let _order: Order = match nif_helpers::deserialize_from_nif(order_binary) {
            Ok(o) => o,
            Err(_e) => return nif_helpers::nif_error(env, &TtQuantError::serialization_error("Failed to deserialize order".to_string())),
        };
        
        // Process (would need sync manager instance)
        // This is a simplified example
        nif_helpers::nif_ok(env, rustler::types::atom::ok())
    }

    /// Get sync events
    pub fn get_sync_events_nif<'a>(env: Env<'a>) -> Term<'a> {
        // Would need to get events from sync manager
        // Return serialized events
        nif_helpers::nif_ok(env, rustler::types::atom::ok())
    }

    /// Get orderbook snapshot
    pub fn get_orderbook_snapshot_nif<'a>(env: Env<'a>, _instrument_id: String, _levels: i32) -> Term<'a> {
        // Would need to get snapshot from sync manager
        nif_helpers::nif_ok(env, rustler::types::atom::ok())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::orders::{OrderBuilder, InstrumentId, OrderSide, OrderType};
    use crate::types::{Price, Quantity};

    #[test]
    fn test_order_persistence() {
        let persistence = OrderPersistence::new();
        
        let order = Order::builder()
            .client_order_id("test-001".to_string())
            .instrument_id(InstrumentId::new("BTCUSD".to_string(), "test".to_string()))
            .side(OrderSide::Buy)
            .order_type(OrderType::Limit)
            .quantity(Quantity::new(1.0, 8).unwrap())
            .price(Some(Price::new(50000.0, 2).unwrap()))
            .build()
            .unwrap();
        
        // Save order
        persistence.save_order(&order, vec![], vec![]).unwrap();
        
        // Load order
        let loaded = persistence.load_order("test-001").unwrap();
        assert!(loaded.is_some());
        
        let (loaded_order, _, _) = loaded.unwrap();
        assert_eq!(loaded_order.id.client_order_id, "test-001");
        
        // Delete order
        persistence.delete_order("test-001").unwrap();
        assert!(persistence.load_order("test-001").unwrap().is_none());
    }

    #[test]
    fn test_sync_manager() {
        let lifecycle = Arc::new(OrderLifecycleManager::new());
        let orderbook = Arc::new(OrderBookManager::new());
        
        let sync = OrderSyncManager::new(lifecycle.clone(), orderbook.clone());
        
        // Start sync
        sync.start().unwrap();
        
        // Create instrument
        let instrument_id = InstrumentId::new("BTCUSD".to_string(), "test".to_string());
        orderbook.get_or_create_book(instrument_id.clone(), 2).unwrap();
        
        // Process order
        let order = Order::builder()
            .client_order_id("test-001".to_string())
            .instrument_id(instrument_id)
            .side(OrderSide::Buy)
            .order_type(OrderType::Limit)
            .quantity(Quantity::new(1.0, 8).unwrap())
            .price(Some(Price::new(50000.0, 2).unwrap()))
            .build()
            .unwrap();
        
        sync.process_order(order).unwrap();
        
        // Get events
        let events = sync.get_pending_events().unwrap();
        assert!(!events.is_empty());
        
        // Get status
        let status = sync.get_system_status().unwrap();
        match status {
            SyncEvent::SystemStatus { active_orders, .. } => {
                assert_eq!(active_orders, 0); // Order not yet accepted
            }
            _ => panic!("Expected SystemStatus event"),
        }
        
        // Stop sync
        sync.stop().unwrap();
    }
}
