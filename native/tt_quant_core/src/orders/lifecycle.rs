use std::collections::HashMap;
use std::sync::{Arc, RwLock};

use chrono::Utc;

use crate::errors::{TtQuantError, TtQuantResult};
use crate::orders::{
    Order, OrderEvent, OrderFill, OrderStatus,
    OrderUpdateFields, OrderSide, OrderType, InstrumentId,
};

/// Order state transition rules
pub struct OrderStateMachine;

impl OrderStateMachine {
    /// Check if a state transition is valid
    pub fn is_valid_transition(from: OrderStatus, to: OrderStatus) -> bool {
        match (from, to) {
            // From Initialized
            (OrderStatus::Initialized, OrderStatus::Submitted) => true,
            (OrderStatus::Initialized, OrderStatus::Rejected) => true,
            (OrderStatus::Initialized, OrderStatus::Cancelled) => true,

            // From Submitted
            (OrderStatus::Submitted, OrderStatus::Accepted) => true,
            (OrderStatus::Submitted, OrderStatus::Rejected) => true,
            (OrderStatus::Submitted, OrderStatus::Cancelled) => true,

            // From Accepted
            (OrderStatus::Accepted, OrderStatus::PartiallyFilled) => true,
            (OrderStatus::Accepted, OrderStatus::Filled) => true,
            (OrderStatus::Accepted, OrderStatus::Cancelled) => true,
            (OrderStatus::Accepted, OrderStatus::Expired) => true,
            (OrderStatus::Accepted, OrderStatus::PendingUpdate) => true,
            (OrderStatus::Accepted, OrderStatus::PendingCancel) => true,

            // From PartiallyFilled
            (OrderStatus::PartiallyFilled, OrderStatus::PartiallyFilled) => true, // More fills
            (OrderStatus::PartiallyFilled, OrderStatus::Filled) => true,
            (OrderStatus::PartiallyFilled, OrderStatus::Cancelled) => true,
            (OrderStatus::PartiallyFilled, OrderStatus::Expired) => true,
            (OrderStatus::PartiallyFilled, OrderStatus::PendingUpdate) => true,
            (OrderStatus::PartiallyFilled, OrderStatus::PendingCancel) => true,

            // From PendingUpdate
            (OrderStatus::PendingUpdate, OrderStatus::Accepted) => true,
            (OrderStatus::PendingUpdate, OrderStatus::PartiallyFilled) => true,
            (OrderStatus::PendingUpdate, OrderStatus::Rejected) => true,

            // From PendingCancel
            (OrderStatus::PendingCancel, OrderStatus::Cancelled) => true,
            (OrderStatus::PendingCancel, OrderStatus::Accepted) => true, // Cancel rejected
            (OrderStatus::PendingCancel, OrderStatus::PartiallyFilled) => true, // Cancel rejected

            // Terminal states cannot transition
            (OrderStatus::Filled, _) => false,
            (OrderStatus::Cancelled, _) => false,
            (OrderStatus::Expired, _) => false,
            (OrderStatus::Rejected, _) => false,

            _ => false,
        }
    }

    /// Apply state transition with validation
    pub fn transition(order: &mut Order, new_status: OrderStatus) -> TtQuantResult<()> {
        if !Self::is_valid_transition(order.status, new_status) {
            return Err(TtQuantError::order_error(format!(
                "Invalid state transition from {:?} to {:?}",
                order.status, new_status
            )));
        }

        order.update_status(new_status);
        Ok(())
    }
}

/// Order lifecycle manager - handles order events and state transitions
pub struct OrderLifecycleManager {
    /// Active orders by client_order_id
    orders: Arc<RwLock<HashMap<String, Order>>>,
    /// Order events history
    events: Arc<RwLock<Vec<OrderEvent>>>,
    /// Event handlers
    event_handlers: Arc<RwLock<Vec<Box<dyn OrderEventHandler>>>>,
}

impl OrderLifecycleManager {
    pub fn new() -> Self {
        Self {
            orders: Arc::new(RwLock::new(HashMap::new())),
            events: Arc::new(RwLock::new(Vec::new())),
            event_handlers: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Register an order
    pub fn register_order(&self, order: Order) -> TtQuantResult<()> {
        let mut orders = self.orders.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        orders.insert(order.id.client_order_id.clone(), order.clone());
        
        // Emit initialized event
        let event = OrderEvent::Initialized {
            order_id: order.id.client_order_id.clone(),
            timestamp: Utc::now(),
        };
        
        self.emit_event(event)?;
        Ok(())
    }

    /// Submit an order
    pub fn submit_order(&self, order_id: &str) -> TtQuantResult<()> {
        let mut orders = self.orders.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let order = orders.get_mut(order_id)
            .ok_or_else(|| TtQuantError::order_error("Order not found"))?;
        
        OrderStateMachine::transition(order, OrderStatus::Submitted)?;
        
        let event = OrderEvent::Submitted {
            order_id: order_id.to_string(),
            timestamp: Utc::now(),
        };
        
        drop(orders); // Release lock before emitting event
        self.emit_event(event)?;
        Ok(())
    }

    /// Accept an order (from exchange)
    pub fn accept_order(&self, order_id: &str, venue_order_id: String) -> TtQuantResult<()> {
        let mut orders = self.orders.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let order = orders.get_mut(order_id)
            .ok_or_else(|| TtQuantError::order_error("Order not found"))?;
        
        order.id.venue_order_id = Some(venue_order_id.clone());
        OrderStateMachine::transition(order, OrderStatus::Accepted)?;
        
        let event = OrderEvent::Accepted {
            order_id: order_id.to_string(),
            venue_order_id,
            timestamp: Utc::now(),
        };
        
        drop(orders);
        self.emit_event(event)?;
        Ok(())
    }

    /// Reject an order
    pub fn reject_order(&self, order_id: &str, reason: String) -> TtQuantResult<()> {
        let mut orders = self.orders.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let order = orders.get_mut(order_id)
            .ok_or_else(|| TtQuantError::order_error("Order not found"))?;
        
        OrderStateMachine::transition(order, OrderStatus::Rejected)?;
        
        let event = OrderEvent::Rejected {
            order_id: order_id.to_string(),
            reason,
            timestamp: Utc::now(),
        };
        
        drop(orders);
        self.emit_event(event)?;
        Ok(())
    }

    /// Process fill for an order
    pub fn fill_order(&self, order_id: &str, fill: OrderFill) -> TtQuantResult<()> {
        let mut orders = self.orders.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let order = orders.get_mut(order_id)
            .ok_or_else(|| TtQuantError::order_error("Order not found"))?;
        
        // Add fill to order
        order.add_fill(fill.quantity, fill.price)?;
        
        // Determine event type based on fill status
        let event = if order.is_filled() {
            OrderEvent::Filled {
                order_id: order_id.to_string(),
                fill,
                timestamp: Utc::now(),
            }
        } else {
            OrderEvent::PartiallyFilled {
                order_id: order_id.to_string(),
                fill,
                timestamp: Utc::now(),
            }
        };
        
        drop(orders);
        self.emit_event(event)?;
        Ok(())
    }

    /// Cancel an order
    pub fn cancel_order(&self, order_id: &str, reason: Option<String>) -> TtQuantResult<()> {
        let mut orders = self.orders.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let order = orders.get_mut(order_id)
            .ok_or_else(|| TtQuantError::order_error("Order not found"))?;
        
        // Set pending cancel first
        OrderStateMachine::transition(order, OrderStatus::PendingCancel)?;
        
        // Then complete cancellation (in real system, this would happen after exchange confirms)
        OrderStateMachine::transition(order, OrderStatus::Cancelled)?;
        
        let event = OrderEvent::Cancelled {
            order_id: order_id.to_string(),
            reason,
            timestamp: Utc::now(),
        };
        
        drop(orders);
        self.emit_event(event)?;
        Ok(())
    }

    /// Update an order
    pub fn update_order(&self, order_id: &str, changes: OrderUpdateFields) -> TtQuantResult<()> {
        let mut orders = self.orders.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let order = orders.get_mut(order_id)
            .ok_or_else(|| TtQuantError::order_error("Order not found"))?;
        
        // Apply updates
        if let Some(quantity) = changes.quantity {
            order.quantity = quantity;
            order.remaining_quantity = (quantity - order.filled_quantity)
                .map_err(|e| TtQuantError::calculation_error(e.to_string()))?;
        }
        
        if let Some(price) = changes.price {
            order.price = Some(price);
        }
        
        if let Some(stop_price) = changes.stop_price {
            order.stop_price = Some(stop_price);
        }
        
        order.updated_at = Utc::now();
        
        let event = OrderEvent::Updated {
            order_id: order_id.to_string(),
            changes,
            timestamp: Utc::now(),
        };
        
        drop(orders);
        self.emit_event(event)?;
        Ok(())
    }

    /// Check for expired orders
    pub fn check_expired_orders(&self) -> TtQuantResult<Vec<String>> {
        let mut orders = self.orders.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        let mut expired_ids = Vec::new();
        
        for (order_id, order) in orders.iter_mut() {
            if order.is_expired() && order.status.is_active() {
                OrderStateMachine::transition(order, OrderStatus::Expired)?;
                expired_ids.push(order_id.clone());
            }
        }
        
        drop(orders);
        
        // Emit events for expired orders
        for order_id in &expired_ids {
            let event = OrderEvent::Expired {
                order_id: order_id.clone(),
                timestamp: Utc::now(),
            };
            self.emit_event(event)?;
        }
        
        Ok(expired_ids)
    }

    /// Get order by ID
    pub fn get_order(&self, order_id: &str) -> TtQuantResult<Option<Order>> {
        let orders = self.orders.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        Ok(orders.get(order_id).cloned())
    }

    /// Get all orders
    pub fn get_all_orders(&self) -> TtQuantResult<Vec<Order>> {
        let orders = self.orders.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        Ok(orders.values().cloned().collect())
    }

    /// Get orders by status
    pub fn get_orders_by_status(&self, status: OrderStatus) -> TtQuantResult<Vec<Order>> {
        let orders = self.orders.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        Ok(orders.values()
            .filter(|o| o.status == status)
            .cloned()
            .collect())
    }

    /// Register event handler
    pub fn register_event_handler(&self, handler: Box<dyn OrderEventHandler>) -> TtQuantResult<()> {
        let mut handlers = self.event_handlers.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        handlers.push(handler);
        Ok(())
    }

    /// Emit event to all handlers
    fn emit_event(&self, event: OrderEvent) -> TtQuantResult<()> {
        // Store event in history
        let mut events = self.events.write()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        events.push(event.clone());
        drop(events);
        
        // Notify handlers
        let handlers = self.event_handlers.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        for handler in handlers.iter() {
            handler.handle_event(&event)?;
        }
        
        Ok(())
    }

    /// Get event history
    pub fn get_event_history(&self) -> TtQuantResult<Vec<OrderEvent>> {
        let events = self.events.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        Ok(events.clone())
    }

    /// Get event history for specific order
    pub fn get_order_events(&self, order_id: &str) -> TtQuantResult<Vec<OrderEvent>> {
        let events = self.events.read()
            .map_err(|e| TtQuantError::lock_error(e.to_string()))?;
        
        Ok(events.iter()
            .filter(|e| match e {
                OrderEvent::Initialized { order_id: id, .. } |
                OrderEvent::Submitted { order_id: id, .. } |
                OrderEvent::Accepted { order_id: id, .. } |
                OrderEvent::Rejected { order_id: id, .. } |
                OrderEvent::Cancelled { order_id: id, .. } |
                OrderEvent::Expired { order_id: id, .. } |
                OrderEvent::Triggered { order_id: id, .. } |
                OrderEvent::Filled { order_id: id, .. } |
                OrderEvent::PartiallyFilled { order_id: id, .. } |
                OrderEvent::Updated { order_id: id, .. } => id == order_id,
            })
            .cloned()
            .collect())
    }
}

/// Trait for handling order events
pub trait OrderEventHandler: Send + Sync {
    fn handle_event(&self, event: &OrderEvent) -> TtQuantResult<()>;
}

/// Simple event logger implementation
pub struct OrderEventLogger;

impl OrderEventHandler for OrderEventLogger {
    fn handle_event(&self, event: &OrderEvent) -> TtQuantResult<()> {
        match event {
            OrderEvent::Initialized { order_id, timestamp } => {
                println!("[{}] Order {} initialized", timestamp, order_id);
            }
            OrderEvent::Submitted { order_id, timestamp } => {
                println!("[{}] Order {} submitted", timestamp, order_id);
            }
            OrderEvent::Accepted { order_id, venue_order_id, timestamp } => {
                println!("[{}] Order {} accepted with venue ID {}", timestamp, order_id, venue_order_id);
            }
            OrderEvent::Rejected { order_id, reason, timestamp } => {
                println!("[{}] Order {} rejected: {}", timestamp, order_id, reason);
            }
            OrderEvent::Filled { order_id, fill, timestamp } => {
                println!("[{}] Order {} filled: {} @ {}", 
                    timestamp, order_id, fill.quantity, fill.price);
            }
            OrderEvent::PartiallyFilled { order_id, fill, timestamp } => {
                println!("[{}] Order {} partially filled: {} @ {}", 
                    timestamp, order_id, fill.quantity, fill.price);
            }
            OrderEvent::Cancelled { order_id, reason, timestamp } => {
                println!("[{}] Order {} cancelled: {:?}", timestamp, order_id, reason);
            }
            OrderEvent::Expired { order_id, timestamp } => {
                println!("[{}] Order {} expired", timestamp, order_id);
            }
            OrderEvent::Triggered { order_id, timestamp } => {
                println!("[{}] Order {} triggered", timestamp, order_id);
            }
            OrderEvent::Updated { order_id, changes, timestamp } => {
                println!("[{}] Order {} updated: {:?}", timestamp, order_id, changes);
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::orders::OrderBuilder;
    use crate::types::{Price, Quantity};

    #[test]
    fn test_state_transitions() {
        assert!(OrderStateMachine::is_valid_transition(
            OrderStatus::Initialized,
            OrderStatus::Submitted
        ));
        
        assert!(OrderStateMachine::is_valid_transition(
            OrderStatus::Accepted,
            OrderStatus::PartiallyFilled
        ));
        
        assert!(!OrderStateMachine::is_valid_transition(
            OrderStatus::Filled,
            OrderStatus::Cancelled
        ));
        
        assert!(!OrderStateMachine::is_valid_transition(
            OrderStatus::Initialized,
            OrderStatus::Filled
        ));
    }

    #[test]
    fn test_lifecycle_manager() {
        let manager = OrderLifecycleManager::new();
        
        // Create and register order
        let order = Order::builder()
            .client_order_id("test-001".to_string())
            .instrument_id(InstrumentId::new("BTCUSD".to_string(), "test".to_string()))
            .side(OrderSide::Buy)
            .order_type(OrderType::Limit)
            .quantity(Quantity::new(1.0, 8).unwrap())
            .price(Some(Price::new(50000.0, 2).unwrap()))
            .build()
            .unwrap();
        
        manager.register_order(order).unwrap();
        
        // Submit order
        manager.submit_order("test-001").unwrap();
        
        // Accept order
        manager.accept_order("test-001", "venue-123".to_string()).unwrap();
        
        // Get order and verify status
        let retrieved = manager.get_order("test-001").unwrap().unwrap();
        assert_eq!(retrieved.status, OrderStatus::Accepted);
        assert_eq!(retrieved.id.venue_order_id, Some("venue-123".to_string()));
        
        // Cancel order
        manager.cancel_order("test-001", Some("User requested".to_string())).unwrap();
        
        let cancelled = manager.get_order("test-001").unwrap().unwrap();
        assert_eq!(cancelled.status, OrderStatus::Cancelled);
        
        // Check events
        let events = manager.get_order_events("test-001").unwrap();
        assert!(events.len() >= 4); // Initialized, Submitted, Accepted, Cancelled
    }
}
