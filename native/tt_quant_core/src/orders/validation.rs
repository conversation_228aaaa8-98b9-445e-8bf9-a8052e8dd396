use super::{Order, OrderType, TimeInForce};
use crate::errors::{TtQuantError, TtQuantResult};
use crate::types::{Money, Price, Quantity};
use chrono::Utc;

/// Order validation rules and constraints
#[derive(Default)]
pub struct OrderValidator {
    /// Minimum order quantity
    pub min_quantity: Option<Quantity>,
    /// Maximum order quantity
    pub max_quantity: Option<Quantity>,
    /// Minimum order value
    pub min_value: Option<Money>,
    /// Maximum order value
    pub max_value: Option<Money>,
    /// Price tick size
    pub tick_size: Option<Price>,
    /// Quantity step size
    pub step_size: Option<Quantity>,
    /// Maximum price deviation from market (percentage)
    pub max_price_deviation: Option<f64>,
}

impl OrderValidator {
    /// Create a new validator with default settings
    pub fn new() -> Self {
        Self::default()
    }

    /// Set minimum quantity constraint
    pub fn with_min_quantity(mut self, min_quantity: Quantity) -> Self {
        self.min_quantity = Some(min_quantity);
        self
    }

    /// Set maximum quantity constraint
    pub fn with_max_quantity(mut self, max_quantity: Quantity) -> Self {
        self.max_quantity = Some(max_quantity);
        self
    }

    /// Set minimum order value constraint
    pub fn with_min_value(mut self, min_value: Money) -> Self {
        self.min_value = Some(min_value);
        self
    }

    /// Set maximum order value constraint
    pub fn with_max_value(mut self, max_value: Money) -> Self {
        self.max_value = Some(max_value);
        self
    }

    /// Set price tick size
    pub fn with_tick_size(mut self, tick_size: Price) -> Self {
        self.tick_size = Some(tick_size);
        self
    }

    /// Set quantity step size
    pub fn with_step_size(mut self, step_size: Quantity) -> Self {
        self.step_size = Some(step_size);
        self
    }

    /// Set maximum price deviation from market
    pub fn with_max_price_deviation(mut self, max_deviation: f64) -> Self {
        self.max_price_deviation = Some(max_deviation);
        self
    }

    /// Validate an order against all constraints
    pub fn validate_order(&self, order: &Order) -> TtQuantResult<()> {
        self.validate_basic_constraints(order)?;
        self.validate_order_type_constraints(order)?;
        self.validate_time_constraints(order)?;
        self.validate_price_constraints(order)?;
        self.validate_quantity_constraints(order)?;
        Ok(())
    }

    /// Validate basic order constraints
    fn validate_basic_constraints(&self, order: &Order) -> TtQuantResult<()> {
        // Validate quantity is positive
        if !order.quantity.is_positive() {
            return Err(TtQuantError::order_error("Order quantity must be positive"));
        }

        // Validate prices are positive (if present)
        if let Some(price) = &order.price {
            if !price.is_positive() {
                return Err(TtQuantError::order_error("Order price must be positive"));
            }
        }

        if let Some(stop_price) = &order.stop_price {
            if !stop_price.is_positive() {
                return Err(TtQuantError::order_error("Stop price must be positive"));
            }
        }

        Ok(())
    }

    /// Validate order type specific constraints
    fn validate_order_type_constraints(&self, order: &Order) -> TtQuantResult<()> {
        match order.order_type {
            OrderType::Market => {
                if order.price.is_some() {
                    return Err(TtQuantError::order_error(
                        "Market orders cannot have a price",
                    ));
                }
                if order.stop_price.is_some() {
                    return Err(TtQuantError::order_error(
                        "Market orders cannot have a stop price",
                    ));
                }
            }
            OrderType::Limit => {
                if order.price.is_none() {
                    return Err(TtQuantError::order_error("Limit orders must have a price"));
                }
                if order.stop_price.is_some() {
                    return Err(TtQuantError::order_error(
                        "Limit orders cannot have a stop price",
                    ));
                }
            }
            OrderType::Stop => {
                if order.price.is_some() {
                    return Err(TtQuantError::order_error(
                        "Stop orders cannot have a limit price",
                    ));
                }
                if order.stop_price.is_none() {
                    return Err(TtQuantError::order_error(
                        "Stop orders must have a stop price",
                    ));
                }
            }
            OrderType::StopLimit => {
                if order.price.is_none() {
                    return Err(TtQuantError::order_error(
                        "Stop limit orders must have a price",
                    ));
                }
                if order.stop_price.is_none() {
                    return Err(TtQuantError::order_error(
                        "Stop limit orders must have a stop price",
                    ));
                }
            }
            OrderType::TrailingStop | OrderType::TrailingStopLimit => {
                if order.stop_price.is_none() {
                    return Err(TtQuantError::order_error(
                        "Trailing stop orders must have a stop price",
                    ));
                }
            }
        }

        Ok(())
    }

    /// Validate time-related constraints
    fn validate_time_constraints(&self, order: &Order) -> TtQuantResult<()> {
        // Check if GTD orders have expiration time
        if order.time_in_force == TimeInForce::GTD && order.expires_at.is_none() {
            return Err(TtQuantError::order_error(
                "GTD orders must have an expiration time",
            ));
        }

        // Check if expiration time is in the future
        if let Some(expires_at) = order.expires_at {
            if expires_at <= Utc::now() {
                return Err(TtQuantError::order_error(
                    "Order expiration time must be in the future",
                ));
            }
        }

        Ok(())
    }

    /// Validate price constraints
    fn validate_price_constraints(&self, order: &Order) -> TtQuantResult<()> {
        // Validate tick size
        if let (Some(tick_size), Some(price)) = (&self.tick_size, &order.price) {
            if !self.is_valid_tick(price, tick_size) {
                return Err(TtQuantError::order_error(format!(
                    "Price {} does not conform to tick size {}",
                    price, tick_size
                )));
            }
        }

        if let (Some(tick_size), Some(stop_price)) = (&self.tick_size, &order.stop_price) {
            if !self.is_valid_tick(stop_price, tick_size) {
                return Err(TtQuantError::order_error(format!(
                    "Stop price {} does not conform to tick size {}",
                    stop_price, tick_size
                )));
            }
        }

        Ok(())
    }

    /// Validate quantity constraints
    fn validate_quantity_constraints(&self, order: &Order) -> TtQuantResult<()> {
        // Validate minimum quantity
        if let Some(min_qty) = &self.min_quantity {
            if order.quantity < *min_qty {
                return Err(TtQuantError::order_error(format!(
                    "Order quantity {} is below minimum {}",
                    order.quantity, min_qty
                )));
            }
        }

        // Validate maximum quantity
        if let Some(max_qty) = &self.max_quantity {
            if order.quantity > *max_qty {
                return Err(TtQuantError::order_error(format!(
                    "Order quantity {} exceeds maximum {}",
                    order.quantity, max_qty
                )));
            }
        }

        // Validate step size
        if let Some(step_size) = &self.step_size {
            if !self.is_valid_step(&order.quantity, step_size) {
                return Err(TtQuantError::order_error(format!(
                    "Quantity {} does not conform to step size {}",
                    order.quantity, step_size
                )));
            }
        }

        Ok(())
    }

    /// Check if price conforms to tick size
    fn is_valid_tick(&self, price: &Price, tick_size: &Price) -> bool {
        if tick_size.is_zero() {
            return true;
        }

        // Convert both to same precision for comparison
        let max_precision = price.precision.max(tick_size.precision);
        let price_normalized = price.round_to_precision(max_precision).unwrap_or(*price);
        let tick_normalized = tick_size
            .round_to_precision(max_precision)
            .unwrap_or(*tick_size);

        let price_raw = price_normalized.raw;
        let tick_raw = tick_normalized.raw;

        if tick_raw == 0 {
            return true;
        }

        price_raw % tick_raw == 0
    }

    /// Check if quantity conforms to step size
    fn is_valid_step(&self, quantity: &Quantity, step_size: &Quantity) -> bool {
        let qty_raw = quantity.raw as f64;
        let step_raw = step_size.raw as f64;

        if step_raw == 0.0 {
            return true;
        }

        let remainder = qty_raw % step_raw;
        remainder.abs() < 1e-9 // Allow for floating point precision errors
    }

    /// Validate order against market price (requires market data)
    pub fn validate_against_market(
        &self,
        order: &Order,
        market_price: Option<Price>,
    ) -> TtQuantResult<()> {
        if let (Some(max_deviation), Some(market), Some(order_price)) =
            (self.max_price_deviation, market_price, &order.price)
        {
            let price_diff = (order_price.as_f64() - market.as_f64()).abs();
            let max_diff = market.as_f64() * max_deviation;

            if price_diff > max_diff {
                return Err(TtQuantError::order_error(format!(
                    "Order price {} deviates too much from market price {}",
                    order_price, market
                )));
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::orders::{InstrumentId, OrderBuilder, OrderSide, OrderType};

    fn test_instrument() -> InstrumentId {
        InstrumentId::new("BTCUSD".to_string(), "binance".to_string())
    }

    #[test]
    fn test_basic_validation() {
        let validator = OrderValidator::new();

        let order = OrderBuilder::new()
            .client_order_id("test-001".to_string())
            .instrument_id(test_instrument())
            .side(OrderSide::Buy)
            .order_type(OrderType::Market)
            .quantity(Quantity::new(1.0, 8).unwrap())
            .build()
            .unwrap();

        assert!(validator.validate_order(&order).is_ok());
    }

    #[test]
    fn test_limit_order_validation() {
        let validator = OrderValidator::new();

        // Valid limit order
        let order = OrderBuilder::limit_buy(
            test_instrument(),
            Quantity::new(1.0, 8).unwrap(),
            Price::new(50000.0, 2).unwrap(),
        )
        .client_order_id("test-002".to_string())
        .build()
        .unwrap();

        assert!(validator.validate_order(&order).is_ok());

        // Invalid limit order (no price)
        let invalid_order = OrderBuilder::new()
            .client_order_id("test-003".to_string())
            .instrument_id(test_instrument())
            .side(OrderSide::Buy)
            .order_type(OrderType::Limit)
            .quantity(Quantity::new(1.0, 8).unwrap())
            .build();

        assert!(invalid_order.is_err());
    }

    #[test]
    fn test_quantity_constraints() {
        let validator = OrderValidator::new()
            .with_min_quantity(Quantity::new(0.1, 8).unwrap())
            .with_max_quantity(Quantity::new(10.0, 8).unwrap());

        // Valid quantity
        let order = OrderBuilder::market_buy(test_instrument(), Quantity::new(1.0, 8).unwrap())
            .client_order_id("test-004".to_string())
            .build()
            .unwrap();

        assert!(validator.validate_order(&order).is_ok());

        // Too small quantity
        let small_order =
            OrderBuilder::market_buy(test_instrument(), Quantity::new(0.05, 8).unwrap())
                .client_order_id("test-005".to_string())
                .build()
                .unwrap();

        assert!(validator.validate_order(&small_order).is_err());
    }

    #[test]
    fn test_tick_size_validation() {
        let validator = OrderValidator::new().with_tick_size(Price::new(0.01, 2).unwrap());

        // Valid price (multiple of tick size)
        let order = OrderBuilder::limit_buy(
            test_instrument(),
            Quantity::new(1.0, 8).unwrap(),
            Price::new(50000.00, 2).unwrap(),
        )
        .client_order_id("test-006".to_string())
        .build()
        .unwrap();

        assert!(validator.validate_order(&order).is_ok());

        // Invalid price (not multiple of tick size)
        let invalid_order = OrderBuilder::limit_buy(
            test_instrument(),
            Quantity::new(1.0, 8).unwrap(),
            Price::new(50000.005, 3).unwrap(),
        )
        .client_order_id("test-007".to_string())
        .build()
        .unwrap();

        assert!(validator.validate_order(&invalid_order).is_err());
    }
}
