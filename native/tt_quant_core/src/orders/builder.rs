use super::{InstrumentId, Order, OrderId, OrderSide, OrderStatus, OrderType, TimeInForce};
use crate::errors::{TtQuantError, TtQuantResult};
use crate::types::{Price, Quantity};
use chrono::{DateTime, Utc};

/// Builder pattern for creating orders with validation
#[derive(Debug, Default)]
pub struct OrderBuilder {
    client_order_id: Option<String>,
    venue_order_id: Option<String>,
    instrument_id: Option<InstrumentId>,
    side: Option<OrderSide>,
    order_type: Option<OrderType>,
    quantity: Option<Quantity>,
    price: Option<Price>,
    stop_price: Option<Price>,
    time_in_force: Option<TimeInForce>,
    expires_at: Option<DateTime<Utc>>,
    tags: Vec<String>,
}

impl OrderBuilder {
    /// Create a new order builder
    pub fn new() -> Self {
        Self::default()
    }

    /// Set client order ID
    pub fn client_order_id(mut self, id: String) -> Self {
        self.client_order_id = Some(id);
        self
    }

    /// Set venue order ID
    pub fn venue_order_id(mut self, id: String) -> Self {
        self.venue_order_id = Some(id);
        self
    }

    /// Set instrument ID
    pub fn instrument_id(mut self, instrument_id: InstrumentId) -> Self {
        self.instrument_id = Some(instrument_id);
        self
    }

    /// Set order side
    pub fn side(mut self, side: OrderSide) -> Self {
        self.side = Some(side);
        self
    }

    /// Set order type
    pub fn order_type(mut self, order_type: OrderType) -> Self {
        self.order_type = Some(order_type);
        self
    }

    /// Set quantity
    pub fn quantity(mut self, quantity: Quantity) -> Self {
        self.quantity = Some(quantity);
        self
    }

    /// Set price (for limit orders)
    pub fn price(mut self, price: Option<Price>) -> Self {
        self.price = price;
        self
    }

    /// Set stop price (for stop orders)
    pub fn stop_price(mut self, stop_price: Option<Price>) -> Self {
        self.stop_price = stop_price;
        self
    }

    /// Set time in force
    pub fn time_in_force(mut self, tif: TimeInForce) -> Self {
        self.time_in_force = Some(tif);
        self
    }

    /// Set expiration time
    pub fn expires_at(mut self, expires_at: Option<DateTime<Utc>>) -> Self {
        self.expires_at = expires_at;
        self
    }

    /// Add a tag
    pub fn tag(mut self, tag: String) -> Self {
        self.tags.push(tag);
        self
    }

    /// Add multiple tags
    pub fn tags(mut self, tags: Vec<String>) -> Self {
        self.tags.extend(tags);
        self
    }

    /// Build the order with validation
    pub fn build(self) -> TtQuantResult<Order> {
        // Validate required fields
        let client_order_id = self
            .client_order_id
            .ok_or_else(|| TtQuantError::order_error("Client order ID is required"))?;

        let instrument_id = self
            .instrument_id
            .ok_or_else(|| TtQuantError::order_error("Instrument ID is required"))?;

        let side = self
            .side
            .ok_or_else(|| TtQuantError::order_error("Order side is required"))?;

        let order_type = self
            .order_type
            .ok_or_else(|| TtQuantError::order_error("Order type is required"))?;

        let quantity = self
            .quantity
            .ok_or_else(|| TtQuantError::order_error("Quantity is required"))?;

        // Validate quantity is positive
        if !quantity.is_positive() {
            return Err(TtQuantError::order_error("Quantity must be positive"));
        }

        // Validate price requirements based on order type
        match order_type {
            OrderType::Limit => {
                if self.price.is_none() {
                    return Err(TtQuantError::order_error(
                        "Price is required for limit orders",
                    ));
                }
            }
            OrderType::StopLimit => {
                if self.price.is_none() {
                    return Err(TtQuantError::order_error(
                        "Price is required for stop limit orders",
                    ));
                }
                if self.stop_price.is_none() {
                    return Err(TtQuantError::order_error(
                        "Stop price is required for stop limit orders",
                    ));
                }
            }
            OrderType::Stop | OrderType::TrailingStop | OrderType::TrailingStopLimit => {
                if self.stop_price.is_none() {
                    return Err(TtQuantError::order_error(
                        "Stop price is required for stop orders",
                    ));
                }
            }
            _ => {}
        }

        // Validate price values are positive
        if let Some(price) = &self.price {
            if !price.is_positive() {
                return Err(TtQuantError::order_error("Price must be positive"));
            }
        }

        if let Some(stop_price) = &self.stop_price {
            if !stop_price.is_positive() {
                return Err(TtQuantError::order_error("Stop price must be positive"));
            }
        }

        // Set default time in force if not specified
        let time_in_force = self.time_in_force.unwrap_or(TimeInForce::GTC);

        // Validate GTD orders have expiration time
        if time_in_force == TimeInForce::GTD && self.expires_at.is_none() {
            return Err(TtQuantError::order_error(
                "GTD orders must have expiration time",
            ));
        }

        // Create order ID
        let order_id = if let Some(venue_id) = self.venue_order_id {
            OrderId::new(client_order_id).with_venue_id(venue_id)
        } else {
            OrderId::new(client_order_id)
        };

        let now = Utc::now();

        Ok(Order {
            id: order_id,
            instrument_id,
            side,
            order_type,
            quantity,
            price: self.price,
            stop_price: self.stop_price,
            time_in_force,
            status: OrderStatus::Initialized,
            filled_quantity: Quantity::zero(quantity.precision)?,
            remaining_quantity: quantity,
            average_fill_price: None,
            created_at: now,
            updated_at: now,
            expires_at: self.expires_at,
            tags: self.tags,
        })
    }
}

/// Convenience methods for common order types
impl OrderBuilder {
    /// Create a market buy order
    pub fn market_buy(instrument_id: InstrumentId, quantity: Quantity) -> Self {
        Self::new()
            .instrument_id(instrument_id)
            .side(OrderSide::Buy)
            .order_type(OrderType::Market)
            .quantity(quantity)
            .time_in_force(TimeInForce::IOC)
    }

    /// Create a market sell order
    pub fn market_sell(instrument_id: InstrumentId, quantity: Quantity) -> Self {
        Self::new()
            .instrument_id(instrument_id)
            .side(OrderSide::Sell)
            .order_type(OrderType::Market)
            .quantity(quantity)
            .time_in_force(TimeInForce::IOC)
    }

    /// Create a limit buy order
    pub fn limit_buy(instrument_id: InstrumentId, quantity: Quantity, price: Price) -> Self {
        Self::new()
            .instrument_id(instrument_id)
            .side(OrderSide::Buy)
            .order_type(OrderType::Limit)
            .quantity(quantity)
            .price(Some(price))
    }

    /// Create a limit sell order
    pub fn limit_sell(instrument_id: InstrumentId, quantity: Quantity, price: Price) -> Self {
        Self::new()
            .instrument_id(instrument_id)
            .side(OrderSide::Sell)
            .order_type(OrderType::Limit)
            .quantity(quantity)
            .price(Some(price))
    }

    /// Create a stop loss order
    pub fn stop_loss(
        instrument_id: InstrumentId,
        quantity: Quantity,
        stop_price: Price,
        side: OrderSide,
    ) -> Self {
        Self::new()
            .instrument_id(instrument_id)
            .side(side)
            .order_type(OrderType::Stop)
            .quantity(quantity)
            .stop_price(Some(stop_price))
    }

    /// Create a stop limit order
    pub fn stop_limit(
        instrument_id: InstrumentId,
        quantity: Quantity,
        price: Price,
        stop_price: Price,
        side: OrderSide,
    ) -> Self {
        Self::new()
            .instrument_id(instrument_id)
            .side(side)
            .order_type(OrderType::StopLimit)
            .quantity(quantity)
            .price(Some(price))
            .stop_price(Some(stop_price))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn test_instrument() -> InstrumentId {
        InstrumentId::new("BTCUSD".to_string(), "binance".to_string())
    }

    #[test]
    fn test_order_builder_basic() {
        let order = OrderBuilder::new()
            .client_order_id("test-001".to_string())
            .instrument_id(test_instrument())
            .side(OrderSide::Buy)
            .order_type(OrderType::Market)
            .quantity(Quantity::new(1.0, 8).unwrap())
            .build()
            .unwrap();

        assert_eq!(order.id.client_order_id, "test-001");
        assert_eq!(order.side, OrderSide::Buy);
        assert_eq!(order.order_type, OrderType::Market);
        assert_eq!(order.status, OrderStatus::Initialized);
    }

    #[test]
    fn test_limit_order_requires_price() {
        let result = OrderBuilder::new()
            .client_order_id("test-002".to_string())
            .instrument_id(test_instrument())
            .side(OrderSide::Buy)
            .order_type(OrderType::Limit)
            .quantity(Quantity::new(1.0, 8).unwrap())
            .build();

        assert!(result.is_err());
        assert!(result
            .unwrap_err()
            .to_string()
            .contains("Price is required"));
    }

    #[test]
    fn test_convenience_methods() {
        let instrument = test_instrument();
        let quantity = Quantity::new(1.0, 8).unwrap();
        let price = Price::new(50000.0, 2).unwrap();

        // Market buy
        let market_buy = OrderBuilder::market_buy(instrument.clone(), quantity)
            .client_order_id("mb-001".to_string())
            .build()
            .unwrap();
        assert_eq!(market_buy.side, OrderSide::Buy);
        assert_eq!(market_buy.order_type, OrderType::Market);

        // Limit sell
        let limit_sell = OrderBuilder::limit_sell(instrument.clone(), quantity, price)
            .client_order_id("ls-001".to_string())
            .build()
            .unwrap();
        assert_eq!(limit_sell.side, OrderSide::Sell);
        assert_eq!(limit_sell.order_type, OrderType::Limit);
        assert_eq!(limit_sell.price, Some(price));
    }

    #[test]
    fn test_validation_errors() {
        let instrument = test_instrument();
        let quantity = Quantity::new(1.0, 8).unwrap();

        // Missing client order ID
        let result = OrderBuilder::new()
            .instrument_id(instrument.clone())
            .side(OrderSide::Buy)
            .order_type(OrderType::Market)
            .quantity(quantity)
            .build();
        assert!(result.is_err());

        // Zero quantity
        let zero_qty = Quantity::zero(8).unwrap();
        let result = OrderBuilder::new()
            .client_order_id("test".to_string())
            .instrument_id(instrument)
            .side(OrderSide::Buy)
            .order_type(OrderType::Market)
            .quantity(zero_qty)
            .build();
        assert!(result.is_err());
    }
}
