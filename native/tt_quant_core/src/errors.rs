use serde::{Deserialize, Serialize};
use thiserror::Error;

/// Core error types for the TT Quant trading engine
#[derive(<PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum TtQuantError {
    #[error("Invalid input: {message}")]
    InvalidInput { message: String },

    #[error("Serialization error: {message}")]
    SerializationError { message: String },

    #[error("Calculation error: {message}")]
    CalculationError { message: String },

    #[error("Price error: {message}")]
    PriceError { message: String },

    #[error("Quantity error: {message}")]
    QuantityError { message: String },

    #[error("Money error: {message}")]
    MoneyError { message: String },

    #[error("Currency error: {message}")]
    CurrencyError { message: String },

    #[error("Order error: {message}")]
    OrderError { message: String },

    #[error("Instrument error: {message}")]
    InstrumentError { message: String },

    #[error("Risk management error: {message}")]
    RiskError { message: String },

    #[error("Portfolio error: {message}")]
    PortfolioError { message: String },

    #[error("Market data error: {message}")]
    MarketDataError { message: String },

    #[error("Exchange adapter error: {message}")]
    ExchangeError { message: String },

    #[error("Network error: {message}")]
    NetworkError { message: String },

    #[error("Configuration error: {message}")]
    ConfigError { message: String },

    #[error("Internal error: {message}")]
    InternalError { message: String },

    #[error("Lock error: {message}")]
    LockError { message: String },
}

impl TtQuantError {
    /// Create a new InvalidInput error
    pub fn invalid_input(message: impl Into<String>) -> Self {
        Self::InvalidInput {
            message: message.into(),
        }
    }

    /// Create a new SerializationError
    pub fn serialization_error(message: impl Into<String>) -> Self {
        Self::SerializationError {
            message: message.into(),
        }
    }

    /// Create a new CalculationError
    pub fn calculation_error(message: impl Into<String>) -> Self {
        Self::CalculationError {
            message: message.into(),
        }
    }

    /// Create a new PriceError
    pub fn price_error(message: impl Into<String>) -> Self {
        Self::PriceError {
            message: message.into(),
        }
    }

    /// Create a new QuantityError
    pub fn quantity_error(message: impl Into<String>) -> Self {
        Self::QuantityError {
            message: message.into(),
        }
    }

    /// Create a new MoneyError
    pub fn money_error(message: impl Into<String>) -> Self {
        Self::MoneyError {
            message: message.into(),
        }
    }

    /// Create a new OrderError
    pub fn order_error(message: impl Into<String>) -> Self {
        Self::OrderError {
            message: message.into(),
        }
    }

    /// Create a new RiskError
    pub fn risk_error(message: impl Into<String>) -> Self {
        Self::RiskError {
            message: message.into(),
        }
    }

    /// Create a new CurrencyError
    pub fn currency_error(message: impl Into<String>) -> Self {
        Self::CurrencyError {
            message: message.into(),
        }
    }

    /// Create a new InstrumentError
    pub fn instrument_error(message: impl Into<String>) -> Self {
        Self::InstrumentError {
            message: message.into(),
        }
    }

    /// Create a new LockError
    pub fn lock_error(message: impl Into<String>) -> Self {
        Self::LockError {
            message: message.into(),
        }
    }

    /// Create a new InternalError
    pub fn internal_error(message: impl Into<String>) -> Self {
        Self::InternalError {
            message: message.into(),
        }
    }

    /// Get error category for logging and metrics
    pub fn category(&self) -> &'static str {
        match self {
            Self::InvalidInput { .. } => "invalid_input",
            Self::SerializationError { .. } => "serialization",
            Self::CalculationError { .. } => "calculation",
            Self::PriceError { .. } => "price",
            Self::QuantityError { .. } => "quantity",
            Self::MoneyError { .. } => "money",
            Self::CurrencyError { .. } => "currency",
            Self::OrderError { .. } => "order",
            Self::InstrumentError { .. } => "instrument",
            Self::RiskError { .. } => "risk",
            Self::PortfolioError { .. } => "portfolio",
            Self::MarketDataError { .. } => "market_data",
            Self::ExchangeError { .. } => "exchange",
            Self::NetworkError { .. } => "network",
            Self::ConfigError { .. } => "config",
            Self::InternalError { .. } => "internal",
            Self::LockError { .. } => "lock",
        }
    }

    /// Check if error is recoverable
    pub fn is_recoverable(&self) -> bool {
        match self {
            Self::NetworkError { .. } => true,
            Self::ExchangeError { .. } => true,
            Self::MarketDataError { .. } => true,
            Self::SerializationError { .. } => false,
            Self::InvalidInput { .. } => false,
            Self::InternalError { .. } => false,
            _ => true,
        }
    }

    /// Convert to a user-friendly message
    pub fn user_message(&self) -> String {
        match self {
            Self::InvalidInput { message } => format!("Invalid input: {}", message),
            Self::PriceError { message } => format!("Price validation failed: {}", message),
            Self::QuantityError { message } => format!("Quantity validation failed: {}", message),
            Self::MoneyError { message } => format!("Money calculation failed: {}", message),
            Self::OrderError { message } => format!("Order processing failed: {}", message),
            Self::RiskError { message } => format!("Risk check failed: {}", message),
            Self::NetworkError { message } => format!("Network connection issue: {}", message),
            Self::ExchangeError { message } => format!("Exchange communication error: {}", message),
            _ => self.to_string(),
        }
    }
}

/// Result type alias for TT Quant operations
pub type TtQuantResult<T> = Result<T, TtQuantError>;

/// Convert from anyhow::Error to TtQuantError
impl From<anyhow::Error> for TtQuantError {
    fn from(err: anyhow::Error) -> Self {
        Self::InternalError {
            message: err.to_string(),
        }
    }
}

/// Convert from serde_json::Error to TtQuantError
impl From<serde_json::Error> for TtQuantError {
    fn from(err: serde_json::Error) -> Self {
        Self::SerializationError {
            message: err.to_string(),
        }
    }
}

/// Convert from std::num::ParseFloatError to TtQuantError
impl From<std::num::ParseFloatError> for TtQuantError {
    fn from(err: std::num::ParseFloatError) -> Self {
        Self::InvalidInput {
            message: format!("Failed to parse number: {}", err),
        }
    }
}

/// Convert from std::num::ParseIntError to TtQuantError
impl From<std::num::ParseIntError> for TtQuantError {
    fn from(err: std::num::ParseIntError) -> Self {
        Self::InvalidInput {
            message: format!("Failed to parse integer: {}", err),
        }
    }
}

/// Convert from rustler::Error to TtQuantError
impl From<rustler::Error> for TtQuantError {
    fn from(err: rustler::Error) -> Self {
        Self::SerializationError {
            message: format!("Rustler error: {:?}", err),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let err = TtQuantError::invalid_input("Test message");
        assert_eq!(err.category(), "invalid_input");
        assert!(!err.is_recoverable());
    }

    #[test]
    fn test_error_recoverability() {
        let network_err = TtQuantError::NetworkError {
            message: "Connection timeout".to_string(),
        };
        assert!(network_err.is_recoverable());

        let invalid_input = TtQuantError::InvalidInput {
            message: "Bad data".to_string(),
        };
        assert!(!invalid_input.is_recoverable());
    }

    #[test]
    fn test_user_message() {
        let err = TtQuantError::price_error("Value out of range");
        let user_msg = err.user_message();
        assert!(user_msg.contains("Price validation failed"));
        assert!(user_msg.contains("Value out of range"));
    }

    #[test]
    fn test_error_conversion() {
        let anyhow_err = anyhow::anyhow!("Test error");
        let tt_err: TtQuantError = anyhow_err.into();
        assert_eq!(tt_err.category(), "internal");
    }
}
