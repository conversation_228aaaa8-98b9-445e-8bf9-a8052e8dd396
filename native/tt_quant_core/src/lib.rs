pub mod errors;
pub mod instruments;
pub mod orders;
pub mod serialization;
pub mod types;

use crate::errors::TtQuantError;
use crate::orders::{InstrumentId, OrderBuilder, OrderSide};
use crate::serialization::{dto, nif_helpers};
use crate::types::{Currency, Money, Price, Quantity};
use rustler::{Binary, Env, Term};
use std::str::FromStr;

// Export atoms for consistent error handling
mod atoms {
    rustler::atoms! {
        ok,
        error,
        invalid_input,
        serialization_error,
        calculation_error,
    }
}

// Basic test NIF function
#[rustler::nif]
fn add(a: i64, b: i64) -> i64 {
    a + b
}

// Price operations
#[rustler::nif]
fn create_price(env: Env, value: f64, precision: u8) -> Term {
    match Price::new(value, precision) {
        Ok(price) => {
            let dto = dto::PriceDto::from(price);
            match nif_helpers::serialize_to_nif(env, &dto) {
                Ok(binary) => nif_helpers::nif_ok(env, binary),
                Err(_) => nif_helpers::nif_error(
                    env,
                    &TtQuantError::serialization_error("Failed to serialize price"),
                ),
            }
        }
        Err(e) => nif_helpers::nif_error(env, &TtQuantError::from(e)),
    }
}

#[rustler::nif]
fn add_prices<'a>(env: Env<'a>, price1_binary: Binary<'a>, price2_binary: Binary<'a>) -> Term<'a> {
    let result = (|| -> Result<Binary, TtQuantError> {
        let price1_dto: dto::PriceDto = nif_helpers::deserialize_from_nif(price1_binary)?;
        let price2_dto: dto::PriceDto = nif_helpers::deserialize_from_nif(price2_binary)?;

        let price1 = Price::try_from(price1_dto)?;
        let price2 = Price::try_from(price2_dto)?;

        let result = (price1 + price2).map_err(TtQuantError::from)?;
        let result_dto = dto::PriceDto::from(result);

        Ok(nif_helpers::serialize_to_nif(env, &result_dto)?)
    })();

    match result {
        Ok(binary) => nif_helpers::nif_ok(env, binary),
        Err(e) => nif_helpers::nif_error(env, &e),
    }
}

// Quantity operations
#[rustler::nif]
fn create_quantity(env: Env, value: f64, precision: u8) -> Term {
    match Quantity::new(value, precision) {
        Ok(quantity) => {
            let dto = dto::QuantityDto::from(quantity);
            match nif_helpers::serialize_to_nif(env, &dto) {
                Ok(binary) => nif_helpers::nif_ok(env, binary),
                Err(_) => nif_helpers::nif_error(
                    env,
                    &TtQuantError::serialization_error("Failed to serialize quantity"),
                ),
            }
        }
        Err(e) => nif_helpers::nif_error(env, &TtQuantError::from(e)),
    }
}

// Order operations
#[rustler::nif]
fn create_market_order<'a>(
    env: Env<'a>,
    symbol: String,
    venue: String,
    side: String,
    quantity_binary: Binary<'a>,
) -> Term<'a> {
    let result = (|| -> Result<Binary, TtQuantError> {
        let quantity_dto: dto::QuantityDto = nif_helpers::deserialize_from_nif(quantity_binary)?;
        let quantity = Quantity::try_from(quantity_dto)?;

        let order_side = match side.as_str() {
            "buy" => OrderSide::Buy,
            "sell" => OrderSide::Sell,
            _ => return Err(TtQuantError::invalid_input("Invalid order side")),
        };

        let instrument_id = InstrumentId::new(symbol, venue);
        let client_order_id = format!("order_{}", uuid::Uuid::new_v4());

        let order = OrderBuilder::market_buy(instrument_id, quantity)
            .client_order_id(client_order_id)
            .side(order_side)
            .build()?;

        Ok(nif_helpers::serialize_to_nif(env, &order)?)
    })();

    match result {
        Ok(binary) => nif_helpers::nif_ok(env, binary),
        Err(e) => nif_helpers::nif_error(env, &e),
    }
}

#[rustler::nif]
fn create_limit_order<'a>(
    env: Env<'a>,
    symbol: String,
    venue: String,
    side: String,
    quantity_binary: Binary<'a>,
    price_binary: Binary<'a>,
) -> Term<'a> {
    let result = (|| -> Result<Binary, TtQuantError> {
        let quantity_dto: dto::QuantityDto = nif_helpers::deserialize_from_nif(quantity_binary)?;
        let price_dto: dto::PriceDto = nif_helpers::deserialize_from_nif(price_binary)?;

        let quantity = Quantity::try_from(quantity_dto)?;
        let price = Price::try_from(price_dto)?;

        let order_side = match side.as_str() {
            "buy" => OrderSide::Buy,
            "sell" => OrderSide::Sell,
            _ => return Err(TtQuantError::invalid_input("Invalid order side")),
        };

        let instrument_id = InstrumentId::new(symbol, venue);
        let client_order_id = format!("order_{}", uuid::Uuid::new_v4());

        let order = OrderBuilder::limit_buy(instrument_id, quantity, price)
            .client_order_id(client_order_id)
            .side(order_side)
            .build()?;

        Ok(nif_helpers::serialize_to_nif(env, &order)?)
    })();

    match result {
        Ok(binary) => nif_helpers::nif_ok(env, binary),
        Err(e) => nif_helpers::nif_error(env, &e),
    }
}

// Money operations
#[rustler::nif]
fn create_money(env: Env, amount: f64, currency_code: String) -> Term {
    let result = (|| -> Result<Binary, TtQuantError> {
        let currency = Currency::from_str(&currency_code)
            .map_err(|e| TtQuantError::currency_error(e.to_string()))?;
        let money = Money::new(amount, currency)?;
        let dto = dto::MoneyDto::from(money);
        Ok(nif_helpers::serialize_to_nif(env, &dto)?)
    })();

    match result {
        Ok(binary) => nif_helpers::nif_ok(env, binary),
        Err(e) => nif_helpers::nif_error(env, &e),
    }
}

rustler::init!("Elixir.TtQuant.Core");
