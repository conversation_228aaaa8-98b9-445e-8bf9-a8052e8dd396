use crate::errors::{TtQuantError, TtQuantR<PERSON>ult};
use serde::{Deserialize, Serialize};

/// Trait for types that can be serialized to/from binary format for NIF communication
pub trait NifSerializable: Serialize + for<'de> Deserialize<'de> {
    /// Serialize to binary format for NIF
    fn to_nif_binary(&self) -> TtQuantResult<Vec<u8>> {
        serde_json::to_vec(self).map_err(TtQuantError::from)
    }

    /// Deserialize from binary format from NIF
    fn from_nif_binary(data: &[u8]) -> TtQuantResult<Self> {
        serde_json::from_slice(data).map_err(TtQuantError::from)
    }
}

/// Implement NifSerializable for all types that implement Serialize + Deserialize
impl<T> NifSerializable for T where T: Serialize + for<'de> Deserialize<'de> {}

/// Helper functions for NIF serialization
pub mod nif_helpers {
    use super::*;
    use rustler::{Binary, Encoder, Env, NifR<PERSON>ult, Term};

    /// Serialize a value to NIF binary
    pub fn serialize_to_nif<'a, T: NifSerializable>(
        env: Env<'a>,
        value: &T,
    ) -> NifResult<Binary<'a>> {
        match value.to_nif_binary() {
            Ok(data) => {
                let mut binary = rustler::OwnedBinary::new(data.len()).unwrap();
                binary.as_mut_slice().copy_from_slice(&data);
                Ok(binary.release(env))
            }
            Err(_) => Err(rustler::Error::BadArg),
        }
    }

    /// Deserialize a value from NIF binary
    pub fn deserialize_from_nif<T: NifSerializable>(binary: Binary) -> NifResult<T> {
        T::from_nif_binary(binary.as_slice()).map_err(|_| rustler::Error::BadArg)
    }

    /// Create a success result tuple for NIF
    pub fn nif_ok<T: Encoder>(env: Env, value: T) -> Term {
        (atoms::ok(), value).encode(env)
    }

    /// Create an error result tuple for NIF
    pub fn nif_error<'a>(env: Env<'a>, error: &TtQuantError) -> Term<'a> {
        let error_atom = match error.category() {
            "invalid_input" => atoms::invalid_input(),
            "serialization" => atoms::serialization_error(),
            "calculation" => atoms::calculation_error(),
            _ => atoms::error(),
        };
        (error_atom, error.user_message()).encode(env)
    }

    /// Atoms for NIF communication
    mod atoms {
        rustler::atoms! {
            ok,
            error,
            invalid_input,
            serialization_error,
            calculation_error,
        }
    }
}

/// Serialization formats supported by the engine
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SerializationFormat {
    Json,
    MessagePack,
    Bincode,
}

/// JSON serializer implementation
pub struct JsonSerializer;

impl JsonSerializer {
    pub fn serialize<T: Serialize>(&self, value: &T) -> TtQuantResult<Vec<u8>> {
        serde_json::to_vec(value).map_err(TtQuantError::from)
    }

    pub fn deserialize<T: for<'de> Deserialize<'de>>(&self, data: &[u8]) -> TtQuantResult<T> {
        serde_json::from_slice(data).map_err(TtQuantError::from)
    }
}

/// Get JSON serializer
pub fn get_json_serializer() -> JsonSerializer {
    JsonSerializer
}

/// Data transfer objects for NIF communication
pub mod dto {
    use super::*;
    use crate::types::{Currency, Money, Price, Quantity};
    use chrono::{DateTime, Utc};
    use std::str::FromStr;

    /// Price data transfer object
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct PriceDto {
        pub value: f64,
        pub precision: u8,
    }

    impl From<Price> for PriceDto {
        fn from(price: Price) -> Self {
            Self {
                value: price.as_f64(),
                precision: price.precision,
            }
        }
    }

    impl TryFrom<PriceDto> for Price {
        type Error = TtQuantError;

        fn try_from(dto: PriceDto) -> Result<Self, Self::Error> {
            Price::new(dto.value, dto.precision).map_err(TtQuantError::from)
        }
    }

    /// Quantity data transfer object
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct QuantityDto {
        pub value: f64,
        pub precision: u8,
    }

    impl From<Quantity> for QuantityDto {
        fn from(quantity: Quantity) -> Self {
            Self {
                value: quantity.as_f64(),
                precision: quantity.precision,
            }
        }
    }

    impl TryFrom<QuantityDto> for Quantity {
        type Error = TtQuantError;

        fn try_from(dto: QuantityDto) -> Result<Self, Self::Error> {
            Quantity::new(dto.value, dto.precision).map_err(TtQuantError::from)
        }
    }

    /// Money data transfer object
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct MoneyDto {
        pub amount: f64,
        pub currency_code: String,
    }

    impl From<Money> for MoneyDto {
        fn from(money: Money) -> Self {
            Self {
                amount: money.as_f64(),
                currency_code: money.currency.code_str().to_string(),
            }
        }
    }

    impl TryFrom<MoneyDto> for Money {
        type Error = TtQuantError;

        fn try_from(dto: MoneyDto) -> Result<Self, Self::Error> {
            let currency = Currency::from_str(&dto.currency_code)
                .map_err(|e| TtQuantError::currency_error(e.to_string()))?;
            Money::new(dto.amount, currency).map_err(TtQuantError::from)
        }
    }

    /// Generic response wrapper for NIF operations
    #[derive(Debug, Clone, Serialize, Deserialize)]
    pub struct ResponseDto<T> {
        pub success: bool,
        pub data: Option<T>,
        pub error: Option<String>,
        pub timestamp: DateTime<Utc>,
    }

    impl<T> ResponseDto<T> {
        pub fn success(data: T) -> Self {
            Self {
                success: true,
                data: Some(data),
                error: None,
                timestamp: Utc::now(),
            }
        }

        pub fn error(error: String) -> Self {
            Self {
                success: false,
                data: None,
                error: Some(error),
                timestamp: Utc::now(),
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{currency::USD, Price};

    #[test]
    fn test_json_serialization() {
        let serializer = JsonSerializer;
        let price = Price::new(123.45, 2).unwrap();

        let serialized = serializer.serialize(&price).unwrap();
        let deserialized: Price = serializer.deserialize(&serialized).unwrap();

        assert_eq!(price, deserialized);
    }

    #[test]
    fn test_price_dto_conversion() {
        let price = Price::new(123.45, 2).unwrap();
        let dto = dto::PriceDto::from(price);
        let converted_back = Price::try_from(dto).unwrap();

        assert_eq!(price, converted_back);
    }

    #[test]
    fn test_response_dto() {
        let success_response = dto::ResponseDto::success("test data");
        assert!(success_response.success);
        assert!(success_response.data.is_some());
        assert!(success_response.error.is_none());

        let error_response: dto::ResponseDto<String> =
            dto::ResponseDto::error("test error".to_string());
        assert!(!error_response.success);
        assert!(error_response.data.is_none());
        assert!(error_response.error.is_some());
    }
}
