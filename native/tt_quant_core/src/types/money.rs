use anyhow::{anyhow, Result};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::cmp::Ordering;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON>er, Result as FmtR<PERSON>ult};
use std::ops::{Add, AddAssign, Div, Mul, Sub, SubAssign};

use super::currency::Currency;
use super::fixed::{
    check_in_range_f64, f64_to_fixed_i64, fixed_i64_to_f64, MoneyRaw, MONEY_MAX, MONEY_MIN,
};

/// Represents a monetary amount in a specific currency
///
/// Combines an amount with a currency denomination for precise financial calculations.
/// Based on Nautilus Trader's Money implementation.
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Money {
    /// Raw fixed-point amount using currency's precision
    pub raw: MoneyRaw,
    /// Currency denomination
    pub currency: Currency,
}

impl Money {
    /// Create a new Money amount with validation
    pub fn new(amount: f64, currency: Currency) -> Result<Self> {
        check_in_range_f64(amount, MONEY_MIN, MONEY_MAX, "money amount")?;

        let raw = f64_to_fixed_i64(amount, currency.precision)?;
        Ok(Money { raw, currency })
    }

    /// Create Money from raw fixed-point value
    pub fn from_raw(raw: MoneyRaw, currency: Currency) -> Self {
        Money { raw, currency }
    }

    /// Create Money from string amount and currency
    pub fn from_str_with_currency(s: &str, currency: Currency) -> Result<Self> {
        let amount: f64 = s
            .parse()
            .map_err(|_| anyhow!("Invalid money amount string: {}", s))?;
        Self::new(amount, currency)
    }

    /// Convert to f64
    pub fn as_f64(&self) -> f64 {
        fixed_i64_to_f64(self.raw, self.currency.precision)
    }

    /// Convert to Decimal for precise calculations
    pub fn as_decimal(&self) -> Decimal {
        let value = self.as_f64();
        Decimal::from_f64_retain(value).unwrap_or_default()
    }

    /// Check if amount is zero
    pub fn is_zero(&self) -> bool {
        self.raw == 0
    }

    /// Check if amount is positive
    pub fn is_positive(&self) -> bool {
        self.raw > 0
    }

    /// Check if amount is negative
    pub fn is_negative(&self) -> bool {
        self.raw < 0
    }

    /// Get absolute value
    pub fn abs(&self) -> Self {
        Money {
            raw: self.raw.abs(),
            currency: self.currency.clone(),
        }
    }

    /// Format as string with currency symbol
    pub fn to_formatted_string(&self) -> String {
        format!(
            "{:.precision$} {}",
            self.as_f64(),
            self.currency.code_str(),
            precision = self.currency.precision as usize
        )
    }

    /// Check if currencies are compatible for operations
    fn check_currency_compatibility(&self, other: &Money) -> Result<()> {
        if self.currency != other.currency {
            return Err(anyhow!(
                "Currency mismatch: {} vs {}",
                self.currency.code_str(),
                other.currency.code_str()
            ));
        }
        Ok(())
    }

    /// Convert to another currency (requires exchange rate)
    pub fn convert_to(&self, target_currency: Currency, exchange_rate: f64) -> Result<Money> {
        if exchange_rate <= 0.0 {
            return Err(anyhow!("Exchange rate must be positive"));
        }

        let converted_amount = self.as_f64() * exchange_rate;
        Money::new(converted_amount, target_currency)
    }
}

impl Display for Money {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(
            f,
            "{:.precision$} {}",
            self.as_f64(),
            self.currency.code_str(),
            precision = self.currency.precision as usize
        )
    }
}

impl PartialOrd for Money {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        if self.currency != other.currency {
            None // Cannot compare different currencies
        } else {
            Some(self.raw.cmp(&other.raw))
        }
    }
}

impl Add for Money {
    type Output = Result<Money>;

    fn add(self, other: Money) -> Self::Output {
        self.check_currency_compatibility(&other)?;

        let result_raw = self
            .raw
            .checked_add(other.raw)
            .ok_or_else(|| anyhow!("Money addition overflow"))?;

        Ok(Money::from_raw(result_raw, self.currency))
    }
}

impl Sub for Money {
    type Output = Result<Money>;

    fn sub(self, other: Money) -> Self::Output {
        self.check_currency_compatibility(&other)?;

        let result_raw = self
            .raw
            .checked_sub(other.raw)
            .ok_or_else(|| anyhow!("Money subtraction underflow"))?;

        Ok(Money::from_raw(result_raw, self.currency))
    }
}

impl Mul<f64> for Money {
    type Output = Result<Money>;

    fn mul(self, scalar: f64) -> Self::Output {
        let new_amount = self.as_f64() * scalar;
        Money::new(new_amount, self.currency)
    }
}

impl Div<f64> for Money {
    type Output = Result<Money>;

    fn div(self, scalar: f64) -> Self::Output {
        if scalar == 0.0 {
            return Err(anyhow!("Division by zero"));
        }
        let new_amount = self.as_f64() / scalar;
        Money::new(new_amount, self.currency)
    }
}

impl AddAssign<Money> for Money {
    fn add_assign(&mut self, other: Money) {
        if let Ok(result) = self.clone() + other {
            *self = result;
        }
    }
}

impl SubAssign<Money> for Money {
    fn sub_assign(&mut self, other: Money) {
        if let Ok(result) = self.clone() - other {
            *self = result;
        }
    }
}

// Convenience constructors
impl Money {
    pub fn zero(currency: Currency) -> Self {
        Money::from_raw(0, currency)
    }

    pub fn one(currency: Currency) -> Result<Self> {
        Self::new(1.0, currency)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::currency::{BTC, EUR, USD};

    #[test]
    fn test_money_creation() {
        let money = Money::new(123.45, USD()).unwrap();
        assert_eq!(money.currency, USD());
        assert!((money.as_f64() - 123.45).abs() < 1e-9);
    }

    #[test]
    fn test_money_arithmetic_same_currency() {
        let m1 = Money::new(100.0, USD()).unwrap();
        let m2 = Money::new(50.0, USD()).unwrap();

        let sum = (m1.clone() + m2.clone()).unwrap();
        assert!((sum.as_f64() - 150.0).abs() < 1e-9);
        assert_eq!(sum.currency, USD());

        let diff = (m1 - m2).unwrap();
        assert!((diff.as_f64() - 50.0).abs() < 1e-9);
    }

    #[test]
    fn test_money_arithmetic_different_currency() {
        let usd = Money::new(100.0, USD()).unwrap();
        let eur = Money::new(50.0, EUR()).unwrap();

        // Should fail due to currency mismatch
        assert!((usd.clone() + eur.clone()).is_err());
        assert!((usd - eur).is_err());
    }

    #[test]
    fn test_money_comparison() {
        let m1 = Money::new(100.0, USD()).unwrap();
        let m2 = Money::new(50.0, USD()).unwrap();
        let eur = Money::new(100.0, EUR()).unwrap();

        assert!(m1.partial_cmp(&m2) == Some(Ordering::Greater));
        assert!(m2.partial_cmp(&m1) == Some(Ordering::Less));
        assert!(m1.partial_cmp(&m1) == Some(Ordering::Equal));

        // Different currencies cannot be compared
        assert!(m1.partial_cmp(&eur).is_none());
    }

    #[test]
    fn test_money_properties() {
        let positive = Money::new(100.0, USD()).unwrap();
        let negative = Money::new(-50.0, USD()).unwrap();
        let zero = Money::zero(USD());

        assert!(positive.is_positive());
        assert!(!positive.is_negative());
        assert!(!positive.is_zero());

        assert!(negative.is_negative());
        assert!(!negative.is_positive());

        assert!(zero.is_zero());
        assert!(!zero.is_positive());
        assert!(!zero.is_negative());
    }

    #[test]
    fn test_currency_conversion() {
        let usd = Money::new(100.0, USD()).unwrap();
        let eur_rate = 0.85; // 1 USD = 0.85 EUR

        let eur = usd.convert_to(EUR(), eur_rate).unwrap();
        assert!((eur.as_f64() - 85.0).abs() < 1e-9);
        assert_eq!(eur.currency, EUR());
    }

    #[test]
    fn test_money_display() {
        let money = Money::new(123.45, USD()).unwrap();
        assert_eq!(format!("{}", money), "123.45 USD");

        let btc = Money::new(0.12345678, BTC()).unwrap();
        assert_eq!(format!("{}", btc), "0.12345678 BTC");
    }
}
