use anyhow::{anyhow, Result};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::cmp::Ordering;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON>er, Result as FmtR<PERSON>ult};
use std::ops::{Add, Div, Mul, Sub};
use std::str::FromStr;

use super::fixed::{
    check_fixed_precision, check_in_range_f64, f64_to_fixed_i64, fixed_i64_to_f64, PriceRaw,
    FIXED_PRECISION, PRICE_MAX, PRICE_MIN,
};

/// Represents a price in a market with specified precision
///
/// Based on Nautilus Trader's Price implementation with fixed-point arithmetic
/// for precise financial calculations without floating-point errors.
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Price {
    /// Raw fixed-point value
    pub raw: PriceRaw,
    /// Number of decimal places (0-9)
    pub precision: u8,
}

impl Price {
    /// Create a new Price with validation
    pub fn new(value: f64, precision: u8) -> Result<Self> {
        check_in_range_f64(value, PRICE_MIN, PRICE_MAX, "price")?;
        check_fixed_precision(precision)?;

        let raw = f64_to_fixed_i64(value, precision)?;
        Ok(Price { raw, precision })
    }

    /// Create Price from raw fixed-point value
    pub fn from_raw(raw: PriceRaw, precision: u8) -> Result<Self> {
        check_fixed_precision(precision)?;
        Ok(Price { raw, precision })
    }

    /// Create Price from string
    pub fn from_str_with_precision(s: &str, precision: u8) -> Result<Self> {
        let value: f64 = s
            .parse()
            .map_err(|_| anyhow!("Invalid price string: {}", s))?;
        Self::new(value, precision)
    }

    /// Convert to f64
    pub fn as_f64(&self) -> f64 {
        fixed_i64_to_f64(self.raw, self.precision)
    }

    /// Convert to Decimal for precise calculations
    pub fn as_decimal(&self) -> Decimal {
        let value = self.as_f64();
        Decimal::from_f64_retain(value).unwrap_or_default()
    }

    /// Check if price is zero
    pub fn is_zero(&self) -> bool {
        self.raw == 0
    }

    /// Check if price is positive
    pub fn is_positive(&self) -> bool {
        self.raw > 0
    }

    /// Check if price is negative
    pub fn is_negative(&self) -> bool {
        self.raw < 0
    }

    /// Get absolute value
    pub fn abs(&self) -> Self {
        Price {
            raw: self.raw.abs(),
            precision: self.precision,
        }
    }

    /// Round to specified precision
    pub fn round_to_precision(&self, precision: u8) -> Result<Self> {
        if precision > FIXED_PRECISION {
            return Err(anyhow!(
                "Precision {} exceeds maximum {}",
                precision,
                FIXED_PRECISION
            ));
        }

        if precision == self.precision {
            return Ok(*self);
        }

        let value = self.as_f64();
        Self::new(value, precision)
    }

    /// Format as string with proper precision
    pub fn to_formatted_string(&self) -> String {
        format!(
            "{:.precision$}",
            self.as_f64(),
            precision = self.precision as usize
        )
    }
}

impl Display for Price {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(
            f,
            "{:.precision$}",
            self.as_f64(),
            precision = self.precision as usize
        )
    }
}

impl FromStr for Price {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self> {
        let value: f64 = s
            .parse()
            .map_err(|_| anyhow!("Invalid price string: {}", s))?;

        // Infer precision from decimal places
        let precision = if let Some(dot_pos) = s.find('.') {
            (s.len() - dot_pos - 1).min(FIXED_PRECISION as usize) as u8
        } else {
            0
        };

        Self::new(value, precision)
    }
}

impl PartialOrd for Price {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for Price {
    fn cmp(&self, other: &Self) -> Ordering {
        // Convert both to same precision for comparison
        let max_precision = self.precision.max(other.precision);

        let self_normalized = self.round_to_precision(max_precision).unwrap_or(*self);
        let other_normalized = other.round_to_precision(max_precision).unwrap_or(*other);

        self_normalized.raw.cmp(&other_normalized.raw)
    }
}

impl Add for Price {
    type Output = Result<Price>;

    fn add(self, other: Price) -> Self::Output {
        let max_precision = self.precision.max(other.precision);
        let self_norm = self.round_to_precision(max_precision)?;
        let other_norm = other.round_to_precision(max_precision)?;

        let result_raw = self_norm
            .raw
            .checked_add(other_norm.raw)
            .ok_or_else(|| anyhow!("Price addition overflow"))?;

        Price::from_raw(result_raw, max_precision)
    }
}

impl Sub for Price {
    type Output = Result<Price>;

    fn sub(self, other: Price) -> Self::Output {
        let max_precision = self.precision.max(other.precision);
        let self_norm = self.round_to_precision(max_precision)?;
        let other_norm = other.round_to_precision(max_precision)?;

        let result_raw = self_norm
            .raw
            .checked_sub(other_norm.raw)
            .ok_or_else(|| anyhow!("Price subtraction underflow"))?;

        Price::from_raw(result_raw, max_precision)
    }
}

impl Mul<f64> for Price {
    type Output = Result<Price>;

    fn mul(self, scalar: f64) -> Self::Output {
        let new_value = self.as_f64() * scalar;
        Price::new(new_value, self.precision)
    }
}

impl Div<f64> for Price {
    type Output = Result<Price>;

    fn div(self, scalar: f64) -> Self::Output {
        if scalar == 0.0 {
            return Err(anyhow!("Division by zero"));
        }
        let new_value = self.as_f64() / scalar;
        Price::new(new_value, self.precision)
    }
}

// Convenience constructors
impl Price {
    pub fn zero(precision: u8) -> Result<Self> {
        Self::new(0.0, precision)
    }

    pub fn one(precision: u8) -> Result<Self> {
        Self::new(1.0, precision)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_price_creation() {
        let price = Price::new(123.456, 3).unwrap();
        assert_eq!(price.precision, 3);
        assert!((price.as_f64() - 123.456).abs() < 1e-9);
    }

    #[test]
    fn test_price_from_string() {
        let price = Price::from_str("123.45").unwrap();
        assert_eq!(price.precision, 2);
        assert!((price.as_f64() - 123.45).abs() < 1e-9);
    }

    #[test]
    fn test_price_arithmetic() {
        let p1 = Price::new(100.0, 2).unwrap();
        let p2 = Price::new(50.0, 2).unwrap();

        let sum = (p1 + p2).unwrap();
        assert!((sum.as_f64() - 150.0).abs() < 1e-9);

        let diff = (p1 - p2).unwrap();
        assert!((diff.as_f64() - 50.0).abs() < 1e-9);
    }

    #[test]
    fn test_price_comparison() {
        let p1 = Price::new(100.0, 2).unwrap();
        let p2 = Price::new(50.0, 2).unwrap();

        assert!(p1 > p2);
        assert!(p2 < p1);
        assert_eq!(p1, p1);
    }

    #[test]
    fn test_price_properties() {
        let positive = Price::new(100.0, 2).unwrap();
        let negative = Price::new(-50.0, 2).unwrap();
        let zero = Price::zero(2).unwrap();

        assert!(positive.is_positive());
        assert!(!positive.is_negative());
        assert!(!positive.is_zero());

        assert!(negative.is_negative());
        assert!(!negative.is_positive());

        assert!(zero.is_zero());
        assert!(!zero.is_positive());
        assert!(!zero.is_negative());
    }
}
