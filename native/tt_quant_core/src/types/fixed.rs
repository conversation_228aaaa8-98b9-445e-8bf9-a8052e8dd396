/// Fixed-point precision constants and utilities
/// Based on Nautilus Trader's fixed-point arithmetic system
use anyhow::{anyhow, Result};

/// Maximum precision for fixed-point arithmetic (9 decimal places)
pub const FIXED_PRECISION: u8 = 9;

/// Fixed-point scalar for converting between float and fixed-point representation
pub const FIXED_SCALAR: i64 = 1_000_000_000; // 10^9

/// Price type definitions
pub type PriceRaw = i64;
pub const PRICE_MAX: f64 = 9_223_372_036.0; // i64::MAX / FIXED_SCALAR
pub const PRICE_MIN: f64 = -9_223_372_036.0; // i64::MIN / FIXED_SCALAR

/// Quantity type definitions  
pub type QuantityRaw = u64;
pub const QUANTITY_MAX: f64 = 18_446_744_073.0; // u64::MAX / FIXED_SCALAR
pub const QUANTITY_MIN: f64 = 0.0;

/// Money type definitions
pub type MoneyRaw = i64;
pub const M<PERSON><PERSON><PERSON>_MAX: f64 = PRICE_MAX;
pub const MONEY_MIN: f64 = PRICE_MIN;

/// Convert f64 to fixed-point i64
pub fn f64_to_fixed_i64(value: f64, precision: u8) -> Result<i64> {
    if !value.is_finite() {
        return Err(anyhow!("Value must be finite, got {}", value));
    }

    let scale = 10_i64.pow(precision as u32);
    let scaled = value * scale as f64;

    if scaled > i64::MAX as f64 || scaled < i64::MIN as f64 {
        return Err(anyhow!(
            "Value {} out of range for precision {}",
            value,
            precision
        ));
    }

    Ok(scaled.round() as i64)
}

/// Convert fixed-point i64 to f64
pub fn fixed_i64_to_f64(raw: i64, precision: u8) -> f64 {
    let scale = 10_i64.pow(precision as u32);
    raw as f64 / scale as f64
}

/// Convert f64 to fixed-point u64 (for quantities)
pub fn f64_to_fixed_u64(value: f64, precision: u8) -> Result<u64> {
    if !value.is_finite() || value < 0.0 {
        return Err(anyhow!(
            "Value must be finite and non-negative, got {}",
            value
        ));
    }

    let scale = 10_u64.pow(precision as u32);
    let scaled = value * scale as f64;

    if scaled > u64::MAX as f64 {
        return Err(anyhow!(
            "Value {} out of range for precision {}",
            value,
            precision
        ));
    }

    Ok(scaled.round() as u64)
}

/// Convert fixed-point u64 to f64
pub fn fixed_u64_to_f64(raw: u64, precision: u8) -> f64 {
    let scale = 10_u64.pow(precision as u32);
    raw as f64 / scale as f64
}

/// Check if precision is valid
pub fn check_fixed_precision(precision: u8) -> Result<()> {
    if precision > FIXED_PRECISION {
        return Err(anyhow!(
            "Precision {} exceeds maximum allowed precision {}",
            precision,
            FIXED_PRECISION
        ));
    }
    Ok(())
}

/// Check if value is in valid range
pub fn check_in_range_f64(value: f64, min: f64, max: f64, name: &str) -> Result<()> {
    if !value.is_finite() {
        return Err(anyhow!("{} must be finite, got {}", name, value));
    }
    if value < min || value > max {
        return Err(anyhow!(
            "{} {} is out of range [{}, {}]",
            name,
            value,
            min,
            max
        ));
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_f64_to_fixed_i64() {
        assert_eq!(f64_to_fixed_i64(1.23456789, 9).unwrap(), 1_234_567_890);
        assert_eq!(f64_to_fixed_i64(-1.23456789, 9).unwrap(), -1_234_567_890);
        assert_eq!(f64_to_fixed_i64(0.0, 9).unwrap(), 0);
    }

    #[test]
    fn test_fixed_i64_to_f64() {
        assert!((fixed_i64_to_f64(1_234_567_890, 9) - 1.23456789).abs() < 1e-9);
        assert!((fixed_i64_to_f64(-1_234_567_890, 9) + 1.23456789).abs() < 1e-9);
        assert_eq!(fixed_i64_to_f64(0, 9), 0.0);
    }

    #[test]
    fn test_f64_to_fixed_u64() {
        assert_eq!(f64_to_fixed_u64(1.23456789, 9).unwrap(), 1_234_567_890);
        assert_eq!(f64_to_fixed_u64(0.0, 9).unwrap(), 0);
        assert!(f64_to_fixed_u64(-1.0, 9).is_err());
    }

    #[test]
    fn test_check_fixed_precision() {
        assert!(check_fixed_precision(9).is_ok());
        assert!(check_fixed_precision(0).is_ok());
        assert!(check_fixed_precision(10).is_err());
    }
}
