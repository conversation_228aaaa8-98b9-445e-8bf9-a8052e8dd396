# 更新日志

本文档记录了 TT Quant 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增

- 人民币 (CNY) 货币支持，包含完整的测试覆盖
- 多币种支持文档和使用示例

### 变更

- 更新 README.md，添加 1.2 阶段完成状态和多币种支持说明
- 优化货币类型的序列化性能

## [0.1.0-dev.2] - 2024-12-19

### 新增

**🎉 核心 Rust 引擎框架 (1.2 阶段完成)**

- **基础数据类型系统**

  - `Price` 类型：基于固定精度算术的价格类型，支持精确金融计算
  - `Quantity` 类型：非负数量类型，用于订单数量和持仓大小
  - `Money` 类型：包含货币信息的金额类型，支持跨币种操作
  - `Currency` 类型：完整的货币定义，支持 ISO 4217 标准
  - 固定精度数学模块：提供最多 9 位小数的高精度计算

- **订单管理系统**

  - `Order` 核心结构：完整的订单数据模型和状态管理
  - `OrderBuilder`：构建器模式，支持各种订单类型的安全创建
  - 订单验证系统：价格、数量、时间等多维度约束验证
  - 订单类型支持：Market、Limit、Stop、StopLimit、TrailingStop 等
  - 订单生命周期管理：从创建到完成的完整状态跟踪

- **金融工具定义**

  - `Instrument` 类型：交易工具的完整定义和规则
  - tick size 和 lot size 验证：确保价格和数量符合交易规则
  - 费用计算：手续费和保证金的精确计算
  - 工具分类：现货、期货、期权等多种工具类型支持

- **多币种支持**

  - **法定货币**: USD, EUR, GBP, JPY, CNY (人民币)
  - **加密货币**: BTC, ETH, USDT, USDC
  - 货币精度管理：每种货币的标准精度设置
  - 类型安全：编译时货币类型检查和验证

- **NIF 接口框架**

  - Rustler 集成：高效的 Elixir-Rust 通信接口
  - 类型安全的跨语言调用：避免运行时类型错误
  - 二进制序列化：优化的数据传输格式
  - 错误处理：统一的错误传播和处理机制

- **测试覆盖**
  - 52 个单元测试：覆盖所有核心功能
  - 边界条件测试：确保极端情况下的稳定性
  - 类型安全测试：验证编译时和运行时的类型安全
  - 性能基准测试：关键路径的性能验证

### 技术实现

- **架构设计**

  - 模块化设计：清晰的模块分离和依赖关系
  - 零拷贝优化：高效的内存使用和数据传输
  - 错误恢复：可恢复错误的识别和处理策略

- **性能优化**
  - 固定精度算术：避免浮点数计算误差
  - 内存效率：优化的数据结构和内存布局
  - 计算效率：高性能的数学运算实现

### 变更

- 项目开发阶段从"基础架构搭建"升级到"核心引擎开发"
- 完成进度更新为"第二阶段 1.2 核心 Rust 引擎框架 (100% 完成)"

## [0.1.0-dev] - 2025-08-28

### 新增

- **基础架构搭建**

  - Phoenix 项目初始化，使用 `--binary-id` 和 `--live` 选项
  - 完整的 Phoenix LiveView 配置
  - 数据库集成 (PostgreSQL + Ecto)
  - 前端资源构建 (esbuild + Tailwind CSS)
  - DaisyUI 和 Heroicons 集成

- **Rustler NIF 集成**

  - 添加 Rustler 依赖 (v0.36.2)
  - 创建 `tt_quant_core` Rust crate
  - 配置 Rust 工作区 (`Cargo.toml`)
  - 实现基础 NIF 接口 (`TtQuant.Core.add/2`)
  - macOS 特定的 Rust 编译配置

- **开发工具链**

  - 代码质量检查工具 (Credo v1.7, Dialyxir v1.4)
  - 自动化开发命令 (`mix code.check`, `mix rust.check` 等)
  - VSCode 配置和拼写检查
  - 预提交检查流程

- **项目文档**
  - 综合性项目文档 (`tt_quant_docs/`)
  - 架构设计文档 (ARCHITECTURE_DESIGN.md)
  - 8 阶段开发计划 (DEVELOPMENT_PLAN.md)
  - 技术决策分析 (TECHNICAL_DECISIONS.md)
  - Nautilus Trader 系统分析 (NAUTILUS_TRADER_ANALYSIS.md)
  - 完整的 README 文档

### 技术栈

- **后端**: Elixir 1.15+ / Phoenix 1.8.0
- **核心引擎**: Rust 1.70+ / Rustler 0.36.2
- **数据库**: PostgreSQL (Ecto 3.13)
- **前端**: LiveView 1.1.0 / Tailwind CSS / DaisyUI
- **构建工具**: esbuild 0.10 / Mix

### 项目结构

```
tt_quant/
├── lib/tt_quant/           # 核心业务逻辑
├── lib/tt_quant_web/       # Web 层和 LiveView
├── native/tt_quant_core/   # Rust NIF 实现
├── config/                 # 应用配置
├── assets/                 # 前端资源
├── test/                   # 测试套件
└── tt_quant_docs/          # 项目文档
```

### 已验证功能

- Phoenix 服务器启动和基础路由
- Rustler NIF 编译和加载
- 基础 NIF 函数调用 (`TtQuant.Core.add/2`)
- 开发环境热重载
- 代码格式化和质量检查

### 开发进度

- ✅ **第一阶段 1.1**: 项目初始化 (100% 完成)

  - ✅ Phoenix 项目创建
  - ✅ Rustler 集成和 NIF 配置
  - ✅ 开发环境和工具链设置
  - ✅ 基础文档和架构设计

- ✅ **第二阶段 1.2**: 核心 Rust 引擎框架 (100% 完成)
  - ✅ 基础数据类型实现 (Price, Quantity, Money, Currency)
  - ✅ 订单管理系统 (Order, OrderBuilder, 验证)
  - ✅ 金融工具定义 (Instrument, 交易规则)
  - ✅ NIF 接口框架 (序列化, 错误处理)
  - ✅ 多币种支持 (包括人民币 CNY)
  - ✅ 完整测试覆盖 (52 个单元测试)

### 下一步计划

- 🚧 **第三阶段 1.3**: Phoenix 基础服务 (计划中)

  - 市场数据处理模块
  - 风险管理系统设计
  - Phoenix 业务层集成

- 📋 **第四阶段 1.4**: 交易执行引擎 (计划中)
  - 实时交易执行
  - 订单路由和管理
  - 交易所适配器框架

---

## 版本说明

- **[未发布]**: 正在开发中的功能
- **[0.1.0-dev]**: 开发版本，包含基础架构和 NIF 集成

## 贡献指南

请在提交 Pull Request 时更新此更新日志，并遵循以下格式：

- **新增**: 新功能
- **变更**: 现有功能的变更
- **弃用**: 即将移除的功能
- **移除**: 已移除的功能
- **修复**: Bug 修复
- **安全**: 安全相关的修复

每个条目应该简洁明了，并包含相关的 issue 或 PR 链接（如果适用）。
