# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## 项目概述

TT Quant 是基于 Phoenix + Rust 技术栈构建的高性能算法交易平台，旨在重新实现 Nautilus Trader 的核心功能。项目采用混合语言架构，Phoenix 负责业务逻辑和 Web 界面，Rust 负责性能关键的交易引擎。

## 核心架构

### 混合语言架构
- **Phoenix (Elixir)**: Web 界面、业务逻辑、配置管理、实时通信
- **Rust**: 高性能交易引擎、订单匹配、风险计算、数据处理
- **Rustler NIF**: Phoenix 和 Rust 之间的桥梁，提供高效的跨语言通信

### 数据流架构
```
Phoenix LiveView ↔ Phoenix Business Layer ↔ Rustler NIF ↔ Rust Core Engine
```

### 状态管理策略
- **Rust 管理**: 订单状态、持仓状态、市场数据、风险指标（性能关键）
- **Phoenix 管理**: 用户配置、策略参数、系统配置、UI 状态（灵活性优先）

## 开发环境设置

### 环境要求
- Elixir 1.15+
- Rust 1.70+
- PostgreSQL 15+
- Redis 7+
- Node.js 18+

### 完整设置
```bash
# 一键设置所有依赖和数据库
mix setup
```

### 开发服务器
```bash
# 开发模式启动 (推荐)
mix phx.server

# 或在 IEx 中启动 (用于调试)
iex -S mix phx.server
```

### 验证安装
```bash
# 启动 IEx 并测试 Rust NIF 集成
iex -S mix

# 测试基础功能
iex> TtQuant.Core.add(1, 2)
3

# 测试价格创建
iex> TtQuant.Core.create_price(100.50, 2)
{:ok, <<binary_data>>}

# 测试人民币支持
iex> TtQuant.Core.create_money(1000.0, "CNY")
{:ok, <<binary_data>>}
```

## 常用开发命令

### 测试
```bash
# 运行所有测试
mix test

# 运行单个测试文件
mix test test/integration/elixir_rust_integration_test.exs

# 运行失败的测试
mix test --failed
```

### 代码质量
```bash
# Elixir 代码检查 (格式化 + Credo)
mix code.check

# Elixir 代码修复
mix code.fix

# Rust 代码检查 (格式化 + Clippy)
mix rust.check

# Rust 代码修复
mix rust.fix

# 完整的预提交检查
mix precommit
```

### 数据库操作
```bash
# 创建数据库
mix ecto.create

# 运行迁移
mix ecto.migrate

# 重置数据库
mix ecto.reset

# 数据库设置 (创建 + 迁移 + 种子数据)
mix ecto.setup
```

### 前端资源
```bash
# 安装前端依赖
mix assets.setup

# 构建前端资源
mix assets.build

# 生产环境资源构建
mix assets.deploy
```

## 项目结构重点

### Phoenix 层 (`lib/`)
- `tt_quant/` - 核心业务逻辑模块
  - `trading/` - 交易业务逻辑
  - `risk/` - 风险管理
  - `portfolio/` - 投资组合管理
  - `strategies/` - 策略管理
- `tt_quant_web/` - Web 层
  - `live/` - LiveView 实时组件
  - `controllers/` - API 控制器
  - `channels/` - WebSocket 频道

### Rust 核心引擎 (`native/tt_quant_core/`)
- `src/lib.rs` - NIF 接口定义
- `src/types.rs` - 基础数据类型 (Price, Quantity, Money)
- `src/orders.rs` - 订单管理系统
- `src/instruments.rs` - 金融工具定义
- `src/errors.rs` - 统一错误处理
- `src/serialization.rs` - 跨语言数据传输

### 测试结构 (`test/`)
- `integration/` - 集成测试（Phoenix-Rust 通信测试）
- `tt_quant/` - 业务逻辑单元测试
- `tt_quant_web/` - Web 层测试

## 开发工作流

### 功能开发流程
1. **Rust 核心逻辑**: 先实现性能关键的核心功能
2. **Phoenix 业务层**: 添加业务逻辑和数据管理
3. **LiveView 界面**: 实现实时 Web 界面

### 数据通信模式
- **Phoenix → Rust**: 通过 Rustler NIF 调用，使用 MessagePack 二进制序列化
- **Rust → Phoenix**: 通过事件回调机制，广播到 LiveView
- **数据验证**: 两端都进行数据验证，确保类型安全

### 测试策略
- **单元测试**: Rust 和 Elixir 分别进行
- **集成测试**: 重点测试 Phoenix-Rust 通信
- **性能测试**: 关键路径的基准测试

## 技术特色

### 多币种支持
支持 9 种货币，特别优化中国市场：
- **法定货币**: USD, EUR, GBP, JPY, CNY
- **加密货币**: BTC, ETH, USDT, USDC
- **精确计算**: 基于 rust_decimal，避免浮点误差

### 高性能设计
- **订单延迟**: 目标 < 100 微秒
- **市场数据处理**: > 100,000 ticks/秒
- **并发连接**: > 10,000 WebSocket 连接

### 实时特性
- **LiveView**: 实时交易仪表板
- **WebSocket**: 实时市场数据推送
- **GenStage**: 背压控制的数据流处理

## 调试和开发技巧

### Rust 调试
```bash
# 编译时显示详细错误
cd native/tt_quant_core && cargo check

# 运行 Rust 单元测试
cd native/tt_quant_core && cargo test
```

### Phoenix 调试
```bash
# 启动带调试信息的服务器
iex -S mix phx.server

# 在 IEx 中测试 NIF 函数
iex> TtQuant.Core.create_price(100.0, 2)
```

### 性能调试
```bash
# 查看编译后的 NIF
file _build/dev/lib/tt_quant/priv/native/libtt_quant_core.so
```

## 常见问题

### NIF 编译失败
确保安装了 Rust 工具链：
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

### 数据库连接问题
检查 PostgreSQL 服务状态：
```bash
pg_ctl status
```

### 前端资源构建失败
重新安装 Node.js 依赖：
```bash
cd assets && npm install && cd ..
```

## 项目规范

### Phoenix 开发规范
- 使用 LiveView 进行实时界面开发
- 通过 PubSub 进行事件广播
- 遵循 Phoenix 1.8 的最佳实践
- 使用 `Req` 进行 HTTP 请求

### Rust 开发规范
- 使用 `rust_decimal` 进行精确数值计算
- 通过 `serde` 进行序列化
- 使用 `thiserror` 进行错误处理
- 遵循 Rust 2021 Edition 标准

### 测试规范
- 集成测试重点验证 Elixir-Rust 通信
- 单元测试覆盖核心业务逻辑
- 性能测试监控关键指标
