defmodule TtQuant.Repo.Migrations.CreatePortfolioTables do
  use Ecto.Migration

  def change do
    # Create accounts table
    create table(:accounts, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :account_type, :string, null: false
      add :base_currency, :string, null: false, default: "USD"
      add :is_active, :boolean, null: false, default: true

      timestamps(type: :utc_datetime)
    end

    create unique_index(:accounts, [:name])
    create index(:accounts, [:account_type])
    create index(:accounts, [:is_active])

    # Create balances table
    create table(:balances, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all), null: false
      add :currency, :string, null: false
      add :total, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :available, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :locked, :decimal, precision: 18, scale: 8, null: false, default: 0

      timestamps(type: :utc_datetime)
    end

    create unique_index(:balances, [:account_id, :currency])
    create index(:balances, [:currency])

    # Create margin_infos table
    create table(:margin_infos, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all), null: false
      add :initial_margin, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :maintenance_margin, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :used_margin, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :available_margin, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :margin_ratio, :decimal, precision: 8, scale: 4, null: false, default: 0
      add :currency, :string, null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:margin_infos, [:account_id])
    create index(:margin_infos, [:currency])

    # Create positions table
    create table(:positions, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all), null: false
      add :instrument_id, :string, null: false
      add :side, :string, null: false
      add :quantity, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :average_price, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :current_price, :decimal, precision: 18, scale: 8
      add :unrealized_pnl, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :realized_pnl, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :currency, :string, null: false
      add :opened_at, :utc_datetime

      timestamps(type: :utc_datetime)
    end

    create unique_index(:positions, [:account_id, :instrument_id])
    create index(:positions, [:instrument_id])
    create index(:positions, [:side])
    create index(:positions, [:currency])

    # Add check constraints
    execute """
    ALTER TABLE balances ADD CONSTRAINT balances_total_check 
    CHECK (total = available + locked)
    """, ""

    execute """
    ALTER TABLE balances ADD CONSTRAINT balances_non_negative_check 
    CHECK (total >= 0 AND available >= 0 AND locked >= 0)
    """, ""

    execute """
    ALTER TABLE margin_infos ADD CONSTRAINT margin_infos_available_check 
    CHECK (available_margin = initial_margin - used_margin)
    """, ""

    execute """
    ALTER TABLE margin_infos ADD CONSTRAINT margin_infos_non_negative_check 
    CHECK (initial_margin >= 0 AND maintenance_margin >= 0 AND used_margin >= 0 AND available_margin >= 0)
    """, ""

    execute """
    ALTER TABLE positions ADD CONSTRAINT positions_quantity_check 
    CHECK ((side = 'flat' AND quantity = 0) OR (side != 'flat' AND quantity > 0))
    """, ""
  end
end
