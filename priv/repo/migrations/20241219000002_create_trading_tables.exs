defmodule TtQuant.Repo.Migrations.CreateTradingTables do
  use Ecto.Migration

  def change do
    # Create orders table
    create table(:orders, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :client_order_id, :string, null: false
      add :venue_order_id, :string
      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all), null: false
      add :instrument_id, :string, null: false
      add :side, :string, null: false
      add :order_type, :string, null: false
      add :quantity, :decimal, precision: 18, scale: 8, null: false
      add :price, :decimal, precision: 18, scale: 8
      add :stop_price, :decimal, precision: 18, scale: 8
      add :time_in_force, :string, null: false, default: "gtc"
      add :status, :string, null: false, default: "initialized"
      add :filled_quantity, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :remaining_quantity, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :average_fill_price, :decimal, precision: 18, scale: 8
      add :currency, :string, null: false
      add :expires_at, :utc_datetime
      add :tags, {:array, :string}, default: []
      add :rust_order_data, :binary

      timestamps(type: :utc_datetime)
    end

    create unique_index(:orders, [:client_order_id])
    create index(:orders, [:account_id])
    create index(:orders, [:instrument_id])
    create index(:orders, [:status])
    create index(:orders, [:side])
    create index(:orders, [:order_type])
    create index(:orders, [:inserted_at])

    # Create trades table
    create table(:trades, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :trade_id, :string, null: false
      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all), null: false
      add :order_id, references(:orders, type: :binary_id, on_delete: :nilify_all)
      add :instrument_id, :string, null: false
      add :side, :string, null: false
      add :quantity, :decimal, precision: 18, scale: 8, null: false
      add :price, :decimal, precision: 18, scale: 8, null: false
      add :value, :decimal, precision: 18, scale: 8, null: false
      add :commission, :decimal, precision: 18, scale: 8, null: false, default: 0
      add :currency, :string, null: false
      add :liquidity_side, :string, null: false, default: "taker"
      add :venue, :string
      add :executed_at, :utc_datetime, null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:trades, [:trade_id])
    create index(:trades, [:account_id])
    create index(:trades, [:order_id])
    create index(:trades, [:instrument_id])
    create index(:trades, [:side])
    create index(:trades, [:executed_at])

    # Add check constraints for orders
    execute """
    ALTER TABLE orders ADD CONSTRAINT orders_quantity_positive_check 
    CHECK (quantity > 0)
    """, ""

    execute """
    ALTER TABLE orders ADD CONSTRAINT orders_remaining_quantity_check 
    CHECK (remaining_quantity = quantity - filled_quantity)
    """, ""

    execute """
    ALTER TABLE orders ADD CONSTRAINT orders_filled_quantity_check 
    CHECK (filled_quantity >= 0 AND filled_quantity <= quantity)
    """, ""

    execute """
    ALTER TABLE orders ADD CONSTRAINT orders_price_requirements_check 
    CHECK (
      (order_type = 'market') OR
      (order_type = 'limit' AND price IS NOT NULL) OR
      (order_type = 'stop' AND stop_price IS NOT NULL) OR
      (order_type = 'stop_limit' AND price IS NOT NULL AND stop_price IS NOT NULL) OR
      (order_type IN ('trailing_stop', 'trailing_stop_limit'))
    )
    """, ""

    execute """
    ALTER TABLE orders ADD CONSTRAINT orders_gtd_expires_check 
    CHECK (
      (time_in_force != 'gtd') OR 
      (time_in_force = 'gtd' AND expires_at IS NOT NULL)
    )
    """, ""

    # Add check constraints for trades
    execute """
    ALTER TABLE trades ADD CONSTRAINT trades_positive_values_check 
    CHECK (quantity > 0 AND price > 0 AND value > 0 AND commission >= 0)
    """, ""

    execute """
    ALTER TABLE trades ADD CONSTRAINT trades_value_calculation_check 
    CHECK (value = quantity * price)
    """, ""
  end
end
