defmodule TtQuant.Repo.Migrations.AddMissingTablesAndIndexes do
  use Ecto.Migration

  def change do
    # Create order_events table for order lifecycle tracking
    create table(:order_events, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :order_id, references(:orders, type: :binary_id, on_delete: :delete_all), null: false
      add :event_type, :string, null: false
      add :event_data, :map, default: %{}
      add :timestamp, :utc_datetime, null: false
      add :source, :string, null: false, default: "system"

      timestamps(type: :utc_datetime)
    end

    create index(:order_events, [:order_id])
    create index(:order_events, [:event_type])
    create index(:order_events, [:timestamp])
    create index(:order_events, [:source])

    # Create market_data table for price feeds
    create table(:market_data, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :instrument_id, :string, null: false
      add :data_type, :string, null: false # 'tick', 'quote', 'bar'
      add :bid_price, :decimal, precision: 18, scale: 8
      add :ask_price, :decimal, precision: 18, scale: 8
      add :bid_quantity, :decimal, precision: 18, scale: 8
      add :ask_quantity, :decimal, precision: 18, scale: 8
      add :last_price, :decimal, precision: 18, scale: 8
      add :last_quantity, :decimal, precision: 18, scale: 8
      add :volume, :decimal, precision: 18, scale: 8
      add :timestamp, :utc_datetime, null: false
      add :venue, :string, null: false

      timestamps(type: :utc_datetime)
    end

    create index(:market_data, [:instrument_id])
    create index(:market_data, [:data_type])
    create index(:market_data, [:timestamp])
    create index(:market_data, [:venue])
    create index(:market_data, [:instrument_id, :timestamp])

    # Create sessions table for user sessions and WebSocket connections
    create table(:sessions, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all)
      add :session_token, :string, null: false
      add :session_type, :string, null: false, default: "web" # 'web', 'api', 'websocket'
      add :ip_address, :string
      add :user_agent, :text
      add :is_active, :boolean, null: false, default: true
      add :expires_at, :utc_datetime, null: false
      add :last_activity_at, :utc_datetime, null: false

      timestamps(type: :utc_datetime)
    end

    create unique_index(:sessions, [:session_token])
    create index(:sessions, [:account_id])
    create index(:sessions, [:session_type])
    create index(:sessions, [:is_active])
    create index(:sessions, [:expires_at])

    # Create notifications table for system notifications
    create table(:notifications, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all)
      add :notification_type, :string, null: false
      add :title, :string, null: false
      add :message, :text, null: false
      add :data, :map, default: %{}
      add :priority, :string, null: false, default: "normal" # 'low', 'normal', 'high', 'urgent'
      add :is_read, :boolean, null: false, default: false
      add :read_at, :utc_datetime
      add :expires_at, :utc_datetime

      timestamps(type: :utc_datetime)
    end

    create index(:notifications, [:account_id])
    create index(:notifications, [:notification_type])
    create index(:notifications, [:priority])
    create index(:notifications, [:is_read])
    create index(:notifications, [:inserted_at])

    # Add performance indexes for existing tables (only if they don't exist)
    create index(:orders, [:account_id, :status], name: :orders_account_status_idx)
    create index(:orders, [:instrument_id, :status], name: :orders_instrument_status_idx)
    create index(:orders, [:account_id, :inserted_at], name: :orders_account_created_idx)
    create index(:trades, [:account_id, :executed_at], name: :trades_account_executed_idx)
    create index(:trades, [:instrument_id, :executed_at], name: :trades_instrument_executed_idx)
    create index(:positions, [:account_id, :side], name: :positions_account_side_idx)

    # Add partial indexes for active records
    create index(:orders, [:account_id],
      where: "status IN ('initialized', 'submitted', 'accepted', 'partially_filled')",
      name: :orders_active_by_account_idx)

    create index(:sessions, [:account_id],
      where: "is_active = true",
      name: :sessions_active_by_account_idx)

    create index(:notifications, [:account_id],
      where: "is_read = false",
      name: :notifications_unread_by_account_idx)
  end
end
