defmodule TtQuant.Repo.Migrations.AddMissingOrderFields do
  use Ecto.Migration

  def change do
    alter table(:orders) do
      add :exchange, :string
      add :metadata, :map, default: %{}
    end

    # Rename average_fill_price to avg_fill_price for consistency
    rename table(:orders), :average_fill_price, to: :avg_fill_price

    # Rename expires_at to expire_time for consistency
    rename table(:orders), :expires_at, to: :expire_time
  end
end
