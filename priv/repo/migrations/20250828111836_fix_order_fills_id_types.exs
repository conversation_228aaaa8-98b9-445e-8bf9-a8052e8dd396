defmodule TtQuant.Repo.Migrations.FixOrderFillsIdTypes do
  use Ecto.Migration

  def change do
    # Create order_fills table with correct ID types
    create table(:order_fills, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :fill_id, :string, null: false
      add :instrument_id, :string, null: false
      add :exchange, :string, null: false

      add :quantity, :decimal, null: false
      add :price, :decimal, null: false
      add :commission, :decimal
      add :commission_currency, :string

      add :liquidity_side, :string
      add :execution_id, :string
      add :venue_fill_id, :string

      add :metadata, :map, default: %{}

      add :order_id, references(:orders, type: :binary_id, on_delete: :delete_all), null: false

      timestamps(type: :utc_datetime)
    end

    create index(:order_fills, [:order_id])
    create index(:order_fills, [:fill_id])
    create index(:order_fills, [:instrument_id])
  end
end
