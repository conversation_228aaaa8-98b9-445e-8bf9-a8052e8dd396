defmodule TtQuant.Repo.Migrations.CreateInstrumentsTable do
  use Ecto.Migration

  def change do
    # Create instruments table
    create table(:instruments, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :symbol, :string, null: false
      add :name, :string, null: false
      add :instrument_type, :string, null: false
      add :base_currency, :string, null: false
      add :quote_currency, :string, null: false
      add :tick_size, :decimal, precision: 18, scale: 8, null: false
      add :lot_size, :decimal, precision: 18, scale: 8, null: false
      add :min_quantity, :decimal, precision: 18, scale: 8, null: false
      add :max_quantity, :decimal, precision: 18, scale: 8, null: false
      add :price_precision, :integer, null: false, default: 2
      add :quantity_precision, :integer, null: false, default: 8
      add :maker_fee, :decimal, precision: 8, scale: 6, null: false, default: 0
      add :taker_fee, :decimal, precision: 8, scale: 6, null: false, default: 0
      add :venue, :string, null: false
      add :is_active, :boolean, null: false, default: true
      add :trading_hours, :map, default: "{}"
      add :contract_size, :decimal, precision: 18, scale: 8
      add :expiry_date, :date
      add :strike_price, :decimal, precision: 18, scale: 8
      add :option_type, :string
      add :rust_instrument_data, :binary

      timestamps(type: :utc_datetime)
    end

    # Create indexes
    create unique_index(:instruments, [:symbol, :venue])
    create index(:instruments, [:instrument_type])
    create index(:instruments, [:base_currency])
    create index(:instruments, [:quote_currency])
    create index(:instruments, [:venue])
    create index(:instruments, [:is_active])
    create index(:instruments, [:expiry_date])

    # Add check constraints
    execute """
    ALTER TABLE instruments ADD CONSTRAINT instruments_positive_values_check 
    CHECK (
      tick_size > 0 AND 
      lot_size > 0 AND 
      min_quantity > 0 AND 
      max_quantity > 0 AND
      maker_fee >= 0 AND
      taker_fee >= 0
    )
    """, ""

    execute """
    ALTER TABLE instruments ADD CONSTRAINT instruments_quantity_bounds_check 
    CHECK (min_quantity <= max_quantity)
    """, ""

    execute """
    ALTER TABLE instruments ADD CONSTRAINT instruments_precision_check 
    CHECK (
      price_precision >= 0 AND price_precision <= 18 AND
      quantity_precision >= 0 AND quantity_precision <= 18
    )
    """, ""

    execute """
    ALTER TABLE instruments ADD CONSTRAINT instruments_options_check 
    CHECK (
      (instrument_type != 'options') OR 
      (instrument_type = 'options' AND strike_price IS NOT NULL AND option_type IS NOT NULL)
    )
    """, ""

    execute """
    ALTER TABLE instruments ADD CONSTRAINT instruments_futures_check 
    CHECK (
      (instrument_type != 'futures') OR 
      (instrument_type = 'futures' AND expiry_date IS NOT NULL)
    )
    """, ""

    execute """
    ALTER TABLE instruments ADD CONSTRAINT instruments_option_type_check 
    CHECK (option_type IS NULL OR option_type IN ('call', 'put'))
    """, ""

    execute """
    ALTER TABLE instruments ADD CONSTRAINT instruments_type_check 
    CHECK (instrument_type IN ('spot', 'futures', 'options', 'swap', 'cfd'))
    """, ""
  end
end
