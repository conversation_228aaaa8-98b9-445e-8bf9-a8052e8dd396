defmodule TtQuant.PortfolioTest do
  use TtQuant.DataCase

  alias TtQuant.Portfolio
  alias TtQuant.Portfolio.{Account, Balance, Position}

  describe "accounts" do
    @valid_attrs %{
      name: "Test Account",
      account_type: :cash,
      base_currency: "USD"
    }

    @invalid_attrs %{
      name: nil,
      account_type: nil,
      base_currency: nil
    }

    test "create_account/1 with valid data creates an account" do
      assert {:ok, %Account{} = account} = Portfolio.create_account(@valid_attrs)
      assert account.name == "Test Account"
      assert account.account_type == :cash
      assert account.base_currency == "USD"
      assert account.is_active == true
    end

    test "create_account/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Portfolio.create_account(@invalid_attrs)
    end

    test "get_account/1 returns the account with given id" do
      {:ok, account} = Portfolio.create_account(@valid_attrs)
      assert Portfolio.get_account(account.id) == account
    end

    test "get_account/1 returns nil for non-existent account" do
      assert Portfolio.get_account(Ecto.UUID.generate()) == nil
    end

    test "list_accounts/0 returns all active accounts" do
      {:ok, account} = Portfolio.create_account(@valid_attrs)
      assert Portfolio.list_accounts() == [account]
    end

    test "update_account/2 with valid data updates the account" do
      {:ok, account} = Portfolio.create_account(@valid_attrs)
      update_attrs = %{name: "Updated Account"}

      assert {:ok, %Account{} = account} = Portfolio.update_account(account, update_attrs)
      assert account.name == "Updated Account"
    end

    test "deactivate_account/1 deactivates the account" do
      {:ok, account} = Portfolio.create_account(@valid_attrs)
      assert {:ok, %Account{} = account} = Portfolio.deactivate_account(account.id)
      assert account.is_active == false
    end
  end

  describe "balances" do
    setup do
      {:ok, account} = Portfolio.create_account(@valid_attrs)
      %{account: account}
    end

    test "update_balance/3 creates or updates balance", %{account: account} do
      amount = Decimal.new("1000.50")
      
      assert {:ok, %Balance{} = balance} = Portfolio.update_balance(account.id, "USD", amount)
      assert balance.currency == "USD"
      assert Decimal.equal?(balance.total, amount)
      assert Decimal.equal?(balance.available, amount)
      assert Decimal.equal?(balance.locked, Decimal.new(0))
    end

    test "get_balance/2 returns balance for currency", %{account: account} do
      amount = Decimal.new("1000.50")
      Portfolio.update_balance(account.id, "USD", amount)
      
      balance = Portfolio.get_balance(account.id, "USD")
      assert Decimal.equal?(balance, amount)
    end

    test "get_available_balance/2 returns available balance", %{account: account} do
      amount = Decimal.new("1000.50")
      Portfolio.update_balance(account.id, "USD", amount)
      
      available = Portfolio.get_available_balance(account.id, "USD")
      assert Decimal.equal?(available, amount)
    end

    test "lock_balance/3 locks amount from available", %{account: account} do
      # First add some balance
      Portfolio.update_balance(account.id, "USD", Decimal.new("1000"))
      
      # Then lock some amount
      lock_amount = Decimal.new("500")
      assert {:ok, %Balance{} = balance} = Portfolio.lock_balance(account.id, "USD", lock_amount)
      
      assert Decimal.equal?(balance.available, Decimal.new("500"))
      assert Decimal.equal?(balance.locked, lock_amount)
      assert Decimal.equal?(balance.total, Decimal.new("1000"))
    end

    test "unlock_balance/3 unlocks amount back to available", %{account: account} do
      # Setup: add balance and lock some
      Portfolio.update_balance(account.id, "USD", Decimal.new("1000"))
      Portfolio.lock_balance(account.id, "USD", Decimal.new("500"))
      
      # Unlock some amount
      unlock_amount = Decimal.new("200")
      assert {:ok, %Balance{} = balance} = Portfolio.unlock_balance(account.id, "USD", unlock_amount)
      
      assert Decimal.equal?(balance.available, Decimal.new("700"))
      assert Decimal.equal?(balance.locked, Decimal.new("300"))
      assert Decimal.equal?(balance.total, Decimal.new("1000"))
    end

    test "has_sufficient_balance?/3 checks balance availability", %{account: account} do
      Portfolio.update_balance(account.id, "USD", Decimal.new("1000"))
      
      assert Portfolio.has_sufficient_balance?(account.id, "USD", Decimal.new("500")) == true
      assert Portfolio.has_sufficient_balance?(account.id, "USD", Decimal.new("1500")) == false
    end
  end

  describe "positions" do
    setup do
      {:ok, account} = Portfolio.create_account(@valid_attrs)
      %{account: account}
    end

    test "update_position/6 creates new position", %{account: account} do
      assert {:ok, %Position{} = position} = Portfolio.update_position(
        account.id, "BTCUSD", :buy, Decimal.new("1.0"), Decimal.new("50000"), "USD"
      )
      
      assert position.instrument_id == "BTCUSD"
      assert position.side == :long
      assert Decimal.equal?(position.quantity, Decimal.new("1.0"))
      assert Decimal.equal?(position.average_price, Decimal.new("50000"))
      assert position.currency == "USD"
    end

    test "update_position/6 adds to existing position", %{account: account} do
      # Create initial position
      Portfolio.update_position(account.id, "BTCUSD", :buy, Decimal.new("1.0"), Decimal.new("50000"), "USD")
      
      # Add to position
      assert {:ok, %Position{} = position} = Portfolio.update_position(
        account.id, "BTCUSD", :buy, Decimal.new("0.5"), Decimal.new("51000"), "USD"
      )
      
      assert Decimal.equal?(position.quantity, Decimal.new("1.5"))
      # Average price should be calculated: (1*50000 + 0.5*51000) / 1.5 = 50333.33
      expected_avg = Decimal.div(Decimal.new("75500"), Decimal.new("1.5"))
      assert Decimal.equal?(position.average_price, expected_avg)
    end

    test "update_position/6 reduces position", %{account: account} do
      # Create initial position
      Portfolio.update_position(account.id, "BTCUSD", :buy, Decimal.new("2.0"), Decimal.new("50000"), "USD")
      
      # Reduce position (sell some)
      assert {:ok, %Position{} = position} = Portfolio.update_position(
        account.id, "BTCUSD", :sell, Decimal.new("0.5"), Decimal.new("51000"), "USD"
      )
      
      assert Decimal.equal?(position.quantity, Decimal.new("1.5"))
      assert position.side == :long
      # Realized P&L should be (51000 - 50000) * 0.5 = 500
      assert Decimal.equal?(position.realized_pnl, Decimal.new("500"))
    end

    test "list_open_positions/1 returns open positions", %{account: account} do
      Portfolio.update_position(account.id, "BTCUSD", :buy, Decimal.new("1.0"), Decimal.new("50000"), "USD")
      Portfolio.update_position(account.id, "ETHUSD", :buy, Decimal.new("10.0"), Decimal.new("3000"), "USD")
      
      positions = Portfolio.list_open_positions(account.id)
      assert length(positions) == 2
      
      instrument_ids = Enum.map(positions, & &1.instrument_id)
      assert "BTCUSD" in instrument_ids
      assert "ETHUSD" in instrument_ids
    end

    test "get_position/2 returns specific position", %{account: account} do
      Portfolio.update_position(account.id, "BTCUSD", :buy, Decimal.new("1.0"), Decimal.new("50000"), "USD")
      
      position = Portfolio.get_position(account.id, "BTCUSD")
      assert position != nil
      assert position.instrument_id == "BTCUSD"
    end
  end

  describe "portfolio summary" do
    setup do
      {:ok, account} = Portfolio.create_account(@valid_attrs)
      %{account: account}
    end

    test "get_portfolio_summary/1 returns summary", %{account: account} do
      # Add some balance and positions
      Portfolio.update_balance(account.id, "USD", Decimal.new("10000"))
      Portfolio.update_position(account.id, "BTCUSD", :buy, Decimal.new("0.1"), Decimal.new("50000"), "USD")
      
      summary = Portfolio.get_portfolio_summary(account.id)
      
      assert %{
        total_value: _,
        total_pnl: _,
        unrealized_pnl: _,
        realized_pnl: _,
        margin_usage: _,
        open_positions: _,
        currencies: _
      } = summary
      
      assert summary.open_positions == 1
      assert "USD" in summary.currencies
    end
  end

  describe "server management" do
    setup do
      {:ok, account} = Portfolio.create_account(@valid_attrs)
      %{account: account}
    end

    test "ensure_portfolio_server/1 starts server", %{account: account} do
      assert {:ok, _pid} = Portfolio.ensure_portfolio_server(account.id)
      assert Portfolio.server_running?(account.id) == true
    end

    test "list_running_servers/0 returns running servers", %{account: account} do
      Portfolio.ensure_portfolio_server(account.id)
      
      servers = Portfolio.list_running_servers()
      assert account.id in servers
    end
  end
end
