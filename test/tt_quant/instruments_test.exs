defmodule TtQuant.InstrumentsCoreTest do
  use TtQuant.DataCase

  alias TtQuant.Instruments.{Instrument, MarketRules}
  alias TtQuant.Trading.Order
  alias TtQuant.Repo

  describe "instrument core functionality" do
    @valid_spot_attrs %{
      symbol: "BTCUSDT",
      name: "Bitcoin/USDT Spot",
      instrument_type: :spot,
      base_currency: "BTC",
      quote_currency: "USDT",
      tick_size: Decimal.new("0.01"),
      lot_size: Decimal.new("0.00001"),
      min_quantity: Decimal.new("0.00001"),
      max_quantity: Decimal.new("1000"),
      price_precision: 2,
      quantity_precision: 8,
      maker_fee: Decimal.new("0.001"),
      taker_fee: Decimal.new("0.001"),
      venue: "test_venue"
    }

    test "create_spot/5 creates valid spot instrument" do
      changeset = Instrument.create_spot("BTCUSD", "Bitcoin/USD", "BTC", "USD")
      assert changeset.valid?

      {:ok, instrument} = Repo.insert(changeset)
      assert instrument.symbol == "BTCUSD"
      assert instrument.instrument_type == :spot
      assert instrument.base_currency == "BTC"
      assert instrument.quote_currency == "USD"
    end

    test "create_futures/6 creates valid futures instrument" do
      expiry_date = Date.add(Date.utc_today(), 30)
      changeset = Instrument.create_futures("BTCUSD0325", "Bitcoin/USD Futures", "BTC", "USD", expiry_date)
      assert changeset.valid?

      {:ok, instrument} = Repo.insert(changeset)
      assert instrument.symbol == "BTCUSD0325"
      assert instrument.instrument_type == :futures
      assert instrument.expiry_date == expiry_date
    end

    test "changeset/2 validates required fields" do
      changeset = Instrument.changeset(%Instrument{}, %{})
      refute changeset.valid?

      required_errors = [:symbol, :name, :instrument_type, :base_currency, :quote_currency,
                        :tick_size, :lot_size, :min_quantity, :max_quantity, :price_precision,
                        :quantity_precision, :venue]

      Enum.each(required_errors, fn field ->
        assert changeset.errors[field]
      end)
    end

    test "changeset/2 validates positive values" do
      invalid_attrs = Map.merge(@valid_spot_attrs, %{
        tick_size: Decimal.new("-0.01"),
        lot_size: Decimal.new("0"),
        min_quantity: Decimal.new("-1"),
        max_quantity: Decimal.new("0")
      })

      changeset = Instrument.changeset(%Instrument{}, invalid_attrs)
      refute changeset.valid?

      assert changeset.errors[:tick_size]
      assert changeset.errors[:lot_size]
      assert changeset.errors[:min_quantity]
      assert changeset.errors[:max_quantity]
    end

    test "changeset/2 validates quantity constraints" do
      invalid_attrs = Map.merge(@valid_spot_attrs, %{
        min_quantity: Decimal.new("100"),
        max_quantity: Decimal.new("10")  # max < min
      })

      changeset = Instrument.changeset(%Instrument{}, invalid_attrs)
      refute changeset.valid?
      assert changeset.errors[:min_quantity]
    end

    test "changeset/2 validates options fields" do
      options_attrs = Map.merge(@valid_spot_attrs, %{
        instrument_type: :options,
        symbol: "BTCUSD50000C",
        name: "Bitcoin Call Option"
        # Missing strike_price and option_type
      })

      changeset = Instrument.changeset(%Instrument{}, options_attrs)
      refute changeset.valid?
      assert changeset.errors[:strike_price]
      assert changeset.errors[:option_type]
    end

    test "changeset/2 validates futures fields" do
      futures_attrs = Map.merge(@valid_spot_attrs, %{
        instrument_type: :futures,
        symbol: "BTCUSD0325",
        name: "Bitcoin Futures"
        # Missing expiry_date
      })

      changeset = Instrument.changeset(%Instrument{}, futures_attrs)
      refute changeset.valid?
      assert changeset.errors[:expiry_date]
    end
  end

  describe "instrument validation functions" do
    setup do
      changeset = Instrument.changeset(%Instrument{}, @valid_spot_attrs)
      {:ok, instrument} = Repo.insert(changeset)
      %{instrument: instrument}
    end

    test "valid_price?/2 validates price against tick size", %{instrument: instrument} do
      assert Instrument.valid_price?(instrument, Decimal.new("100.00")) == true
      assert Instrument.valid_price?(instrument, Decimal.new("100.01")) == true
      assert Instrument.valid_price?(instrument, Decimal.new("100.001")) == false
    end

    test "valid_quantity?/2 validates quantity against lot size and bounds", %{instrument: instrument} do
      assert Instrument.valid_quantity?(instrument, Decimal.new("0.00001")) == true  # min quantity
      assert Instrument.valid_quantity?(instrument, Decimal.new("0.0001")) == true   # multiple of lot size
      assert Instrument.valid_quantity?(instrument, Decimal.new("1000")) == true     # max quantity

      assert Instrument.valid_quantity?(instrument, Decimal.new("0.000001")) == false  # below lot size
      assert Instrument.valid_quantity?(instrument, Decimal.new("0.000005")) == false  # below min quantity
      assert Instrument.valid_quantity?(instrument, Decimal.new("2000")) == false      # above max quantity
    end

    test "round_price/2 rounds price to nearest tick", %{instrument: instrument} do
      rounded = Instrument.round_price(instrument, Decimal.new("100.567"))
      assert Decimal.equal?(rounded, Decimal.new("100.57"))

      rounded = Instrument.round_price(instrument, Decimal.new("100.564"))
      assert Decimal.equal?(rounded, Decimal.new("100.56"))
    end

    test "round_quantity/2 rounds quantity to nearest lot", %{instrument: instrument} do
      rounded = Instrument.round_quantity(instrument, Decimal.new("0.000567"))
      assert Decimal.equal?(rounded, Decimal.new("0.00057"))

      rounded = Instrument.round_quantity(instrument, Decimal.new("0.000564"))
      assert Decimal.equal?(rounded, Decimal.new("0.00056"))
    end

    test "calculate_fee/3 calculates trading fees", %{instrument: instrument} do
      value = Decimal.new("1000.0")

      maker_fee = Instrument.calculate_fee(instrument, value, :maker)
      assert Decimal.equal?(maker_fee, Decimal.new("1.0"))  # 0.001 * 1000

      taker_fee = Instrument.calculate_fee(instrument, value, :taker)
      assert Decimal.equal?(taker_fee, Decimal.new("1.0"))  # 0.001 * 1000
    end

    test "tradeable?/1 checks if instrument is tradeable", %{instrument: instrument} do
      assert Instrument.tradeable?(instrument) == true

      # Test inactive instrument
      inactive_instrument = %{instrument | is_active: false}
      assert Instrument.tradeable?(inactive_instrument) == false

      # Test expired instrument
      expired_instrument = %{instrument | expiry_date: Date.add(Date.utc_today(), -1)}
      assert Instrument.tradeable?(expired_instrument) == false

      # Test future instrument
      future_instrument = %{instrument | expiry_date: Date.add(Date.utc_today(), 30)}
      assert Instrument.tradeable?(future_instrument) == true
    end

    test "full_symbol/1 returns full symbol", %{instrument: instrument} do
      full_symbol = Instrument.full_symbol(instrument)
      assert full_symbol == "BTCUSDT@test_venue"
    end

    test "in_trading_hours?/2 with no trading hours restriction", %{instrument: instrument} do
      # No trading hours specified, should always be true
      assert Instrument.in_trading_hours?(instrument, DateTime.utc_now()) == true
    end
  end

  describe "market rules validation" do
    setup do
      changeset = Instrument.changeset(%Instrument{}, @valid_spot_attrs)
      {:ok, instrument} = Repo.insert(changeset)
      %{instrument: instrument}
    end

    test "validate_order/2 validates valid order", %{instrument: instrument} do
      order = %Order{
        instrument_id: "BTCUSDT@test_venue",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.001"),
        price: Decimal.new("50000.00"),
        currency: "USDT"
      }

      assert :ok = MarketRules.validate_order(order, instrument)
    end

    test "validate_order/2 rejects invalid tick size", %{instrument: instrument} do
      order = %Order{
        instrument_id: "BTCUSDT@test_venue",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.001"),
        price: Decimal.new("50000.001"),  # Invalid tick size
        currency: "USDT"
      }

      assert {:error, :invalid_tick_size} = MarketRules.validate_order(order, instrument)
    end

    test "validate_order/2 rejects invalid lot size", %{instrument: instrument} do
      order = %Order{
        instrument_id: "BTCUSDT@test_venue",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.000001"),  # Below minimum quantity
        price: Decimal.new("50000.00"),
        currency: "USDT"
      }

      assert {:error, :below_minimum} = MarketRules.validate_order(order, instrument)
    end

    test "validate_trading_allowed/1 validates trading status", %{instrument: instrument} do
      assert :ok = MarketRules.validate_trading_allowed(instrument)

      # Test inactive instrument
      inactive_instrument = %{instrument | is_active: false}
      assert {:error, :instrument_inactive} = MarketRules.validate_trading_allowed(inactive_instrument)

      # Test expired instrument
      expired_instrument = %{instrument | expiry_date: Date.add(Date.utc_today(), -1)}
      assert {:error, :expired} = MarketRules.validate_trading_allowed(expired_instrument)
    end

    test "suggest_price/2 rounds to valid tick", %{instrument: instrument} do
      suggested = MarketRules.suggest_price(Decimal.new("50000.567"), instrument)
      assert Decimal.equal?(suggested, Decimal.new("50000.57"))
    end

    test "suggest_quantity/2 rounds to valid lot", %{instrument: instrument} do
      suggested = MarketRules.suggest_quantity(Decimal.new("0.000123"), instrument)
      assert Decimal.equal?(suggested, Decimal.new("0.00012"))
    end

    test "calculate_trading_fees/4 calculates fees correctly", %{instrument: instrument} do
      order = %Order{
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("1.0"),
        price: Decimal.new("50000.00"),
        currency: "USDT"
      }

      fees = MarketRules.calculate_trading_fees(order, instrument, :taker)

      # Fee is 0.001 (0.1%) of order value (50000)
      expected_commission = Decimal.new("50.0")  # 50000 * 0.001
      expected_total = Decimal.new("50050.0")    # 50000 + 50

      assert Decimal.equal?(fees.commission, expected_commission)
      assert Decimal.equal?(fees.total_cost, expected_total)
    end

    test "get_trading_session/2 returns session info", %{instrument: instrument} do
      session = MarketRules.get_trading_session(instrument, DateTime.utc_now())
      assert session.status == :open
      assert session.next_open == nil
    end

    test "get_risk_parameters/1 returns risk parameters", %{instrument: instrument} do
      params = MarketRules.get_risk_parameters(instrument)

      assert Decimal.equal?(params.tick_size, Decimal.new("0.01"))
      assert Decimal.equal?(params.lot_size, Decimal.new("0.00001"))
      assert Decimal.equal?(params.min_quantity, Decimal.new("0.00001"))
      assert Decimal.equal?(params.max_quantity, Decimal.new("1000"))
      assert params.price_precision == 2
      assert params.quantity_precision == 8
    end

    test "format_validation_error/1 formats errors", %{instrument: instrument} do
      assert MarketRules.format_validation_error({:error, :instrument_inactive}) == "Instrument is not active for trading"
      assert MarketRules.format_validation_error({:error, :expired}) == "Instrument has expired"
      assert MarketRules.format_validation_error({:error, :invalid_tick_size}) == "Price does not conform to tick size requirements"
      assert MarketRules.format_validation_error({:error, "Custom error"}) == "Custom error"
    end
  end
end
