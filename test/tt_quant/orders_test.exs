defmodule TtQuant.OrdersTest do
  use TtQuant.DataCase

  alias TtQuant.Orders
  alias TtQuant.Orders.{Order, OrderFill}

  describe "orders" do
    alias TtQuant.Portfolio.Account

    setup do
      # Create a test account
      {:ok, account} = %Account{}
      |> Account.changeset(%{
        name: "Test Account",
        account_type: "cash",
        base_currency: "USD"
      })
      |> Repo.insert()

      %{account: account}
    end

    @valid_attrs %{
      "client_order_id" => "TEST-001",
      "instrument_id" => "BTCUSD",
      "exchange" => "test_exchange",
      "side" => "buy",
      "order_type" => "limit",
      "status" => "initialized",
      "quantity" => "1.0",
      "price" => "50000.0",
      "remaining_quantity" => "1.0",
      "currency" => "USD"
    }

    @update_attrs %{
      "status" => "submitted",
      "quantity" => "2.0",
      "remaining_quantity" => "2.0"
    }

    @invalid_attrs %{
      "instrument_id" => nil,
      "side" => "invalid"
    }

    def order_fixture(attrs \\ %{}, account \\ nil) do
      account_id = if account, do: account.id, else: "********-89ab-cdef-0123-456789abcdef"

      {:ok, order} =
        attrs
        |> Enum.into(@valid_attrs)
        |> Map.put("account_id", account_id)
        |> Orders.create_order()

      order
    end

    test "list_orders/1 returns all orders", %{account: account} do
      order = order_fixture(%{}, account)
      # Reload order to get the updated state from database
      reloaded_order = Orders.get_order!(order.id)
      assert Orders.list_orders() == [reloaded_order]
    end

    test "list_orders/1 with filters", %{account: account} do
      buy_order = order_fixture(%{"side" => "buy"}, account)
      _sell_order = order_fixture(%{"side" => "sell", "client_order_id" => "TEST-002"}, account)

      # Reload buy_order to get the updated state from database
      reloaded_buy_order = Orders.get_order!(buy_order.id)
      assert Orders.list_orders(side: "buy") == [reloaded_buy_order]
    end

    test "get_order!/1 returns the order with given id", %{account: account} do
      order = order_fixture(%{}, account)
      reloaded_order = Orders.get_order!(order.id)
      assert Orders.get_order!(order.id) == reloaded_order
    end

    test "get_order_by_client_id/1 returns order by client_order_id", %{account: account} do
      order = order_fixture(%{}, account)
      reloaded_order = Orders.get_order!(order.id)
      assert Orders.get_order_by_client_id(order.client_order_id) == reloaded_order
    end

    test "create_order/1 with valid data creates an order", %{account: account} do
      attrs = Map.put(@valid_attrs, "account_id", account.id)
      assert {:ok, %Order{} = order} = Orders.create_order(attrs)
      assert order.client_order_id == "TEST-001"
      assert order.instrument_id == "BTCUSD"
      assert order.side == "buy"
      assert order.order_type == "limit"
      assert Decimal.equal?(order.quantity, Decimal.new("1.0"))
      assert Decimal.equal?(order.price, Decimal.new("50000.0"))
    end

    test "create_order/1 with invalid data returns error changeset" do
      assert {:error, %Ecto.Changeset{}} = Orders.create_order(@invalid_attrs)
    end

    test "update_order/2 with valid data updates the order", %{account: account} do
      order = order_fixture(%{}, account)
      assert {:ok, %Order{} = updated} = Orders.update_order(order, @update_attrs)
      assert updated.status == "submitted"
      assert Decimal.equal?(updated.quantity, Decimal.new("2.0"))
    end

    test "update_order_status/2 validates state transitions", %{account: account} do
      order = order_fixture(%{}, account)

      # Valid transition
      assert {:ok, order} = Orders.update_order_status(order, "submitted")
      assert order.status == "submitted"

      # Invalid transition
      assert {:error, _} = Orders.update_order_status(order, "filled")
    end

    test "cancel_order/2 cancels active order", %{account: account} do
      order = order_fixture(%{"status" => "accepted"}, account)
      assert {:ok, cancelled} = Orders.cancel_order(order, "User requested")
      assert cancelled.status == "cancelled"
      assert cancelled.metadata["cancel_reason"] == "User requested"
    end

    test "get_active_orders/0 returns only active orders", %{account: account} do
      _cancelled = order_fixture(%{"status" => "cancelled", "client_order_id" => "TEST-002"}, account)
      active = order_fixture(%{"status" => "accepted", "client_order_id" => "TEST-003"}, account)

      active_orders = Orders.get_active_orders()
      assert length(active_orders) == 1
      assert hd(active_orders).id == active.id
    end
  end

  describe "order state transitions" do
    test "valid transitions are allowed" do
      assert Order.valid_transition?("initialized", "submitted")
      assert Order.valid_transition?("submitted", "accepted")
      assert Order.valid_transition?("accepted", "filled")
      assert Order.valid_transition?("accepted", "cancelled")
      assert Order.valid_transition?("partially_filled", "filled")
    end

    test "invalid transitions are rejected" do
      refute Order.valid_transition?("filled", "cancelled")
      refute Order.valid_transition?("cancelled", "filled")
      refute Order.valid_transition?("initialized", "filled")
    end
  end

  describe "order fills" do
    setup do
      # Create a test account
      {:ok, account} = %TtQuant.Portfolio.Account{}
      |> TtQuant.Portfolio.Account.changeset(%{
        name: "Test Account",
        account_type: "cash",
        base_currency: "USD"
      })
      |> TtQuant.Repo.insert()

      order = order_fixture(%{
        "status" => "accepted",
        "quantity" => "10.0",
        "remaining_quantity" => "10.0"
      }, account)
      {:ok, order: order, account: account}
    end

    test "process_fill/2 creates fill and updates order", %{order: order} do
      fill_attrs = %{
        "fill_id" => "FILL-001",
        "instrument_id" => order.instrument_id,
        "exchange" => order.exchange,
        "quantity" => "5.0",
        "price" => "50000.0"
      }

      assert {:ok, {updated_order, fill}} = Orders.process_fill(order, fill_attrs)
      assert Decimal.equal?(updated_order.filled_quantity, Decimal.new("5.0"))
      assert Decimal.equal?(updated_order.remaining_quantity, Decimal.new("5.0"))
      assert updated_order.status == "partially_filled"
      assert fill.fill_id == "FILL-001"
    end

    test "apply_fill/3 updates order calculations", %{order: order} do
      updated = Order.apply_fill(order, Decimal.new("5.0"), Decimal.new("50000.0"))
      assert Decimal.equal?(updated.filled_quantity, Decimal.new("5.0"))
      assert Decimal.equal?(updated.remaining_quantity, Decimal.new("5.0"))
      assert updated.status == "partially_filled"

      # Fill remaining
      fully_filled = Order.apply_fill(updated, Decimal.new("5.0"), Decimal.new("50100.0"))
      assert Decimal.equal?(fully_filled.filled_quantity, Decimal.new("10.0"))
      assert Decimal.equal?(fully_filled.remaining_quantity, Decimal.new("0"))
      assert fully_filled.status == "filled"
      assert Decimal.equal?(fully_filled.avg_fill_price, Decimal.new("50050.0"))
    end
  end

  describe "order_fills" do
    @valid_fill_attrs %{
      "fill_id" => "FILL-001",
      "order_id" => "********-89ab-cdef-0123-456789abcdef",
      "instrument_id" => "BTCUSD",
      "exchange" => "test_exchange",
      "quantity" => "1.0",
      "price" => "50000.0"
    }

    test "changeset with valid attributes" do
      changeset = OrderFill.changeset(%OrderFill{}, @valid_fill_attrs)
      assert changeset.valid?
    end

    test "changeset requires required fields" do
      changeset = OrderFill.changeset(%OrderFill{}, %{})
      refute changeset.valid?
      assert "can't be blank" in errors_on(changeset).fill_id
      assert "can't be blank" in errors_on(changeset).quantity
    end

    test "value/1 calculates fill value" do
      fill = %OrderFill{
        quantity: Decimal.new("2.0"),
        price: Decimal.new("50000.0")
      }
      assert Decimal.equal?(OrderFill.value(fill), Decimal.new("100000.0"))
    end

    test "net_value/1 calculates value minus commission" do
      fill = %OrderFill{
        quantity: Decimal.new("2.0"),
        price: Decimal.new("50000.0"),
        commission: Decimal.new("10.0")
      }
      assert Decimal.equal?(OrderFill.net_value(fill), Decimal.new("99990.0"))
    end
  end

  # errors_on/1 is already imported from DataCase
end
