defmodule TtQuant.TradingTest do
  use TtQuant.DataCase

  alias TtQuant.{Trading, Portfolio}
  alias TtQuant.Trading.{Order, Trade}

  describe "orders" do
    setup do
      # Create account first
      {:ok, account} = Portfolio.create_account(%{
        name: "Test Trading Account",
        account_type: :cash,
        base_currency: "USD"
      })
      
      # Add some balance for trading
      Portfolio.update_balance(account.id, "USD", Decimal.new("10000"))
      
      %{account: account}
    end

    test "market_buy/4 creates market buy order", %{account: account} do
      assert {:ok, %Order{} = order} = Trading.market_buy(
        account.id, "BTCUSD", Decimal.new("0.1"), "USD"
      )
      
      assert order.account_id == account.id
      assert order.instrument_id == "BTCUSD"
      assert order.side == :buy
      assert order.order_type == :market
      assert Decimal.equal?(order.quantity, Decimal.new("0.1"))
      assert order.currency == "USD"
      assert order.time_in_force == :ioc
      assert order.status == :initialized
    end

    test "market_sell/4 creates market sell order", %{account: account} do
      assert {:ok, %Order{} = order} = Trading.market_sell(
        account.id, "BTCUSD", Decimal.new("0.1"), "USD"
      )
      
      assert order.side == :sell
      assert order.order_type == :market
      assert order.time_in_force == :ioc
    end

    test "limit_buy/5 creates limit buy order", %{account: account} do
      assert {:ok, %Order{} = order} = Trading.limit_buy(
        account.id, "BTCUSD", Decimal.new("0.1"), Decimal.new("45000"), "USD"
      )
      
      assert order.side == :buy
      assert order.order_type == :limit
      assert Decimal.equal?(order.price, Decimal.new("45000"))
      assert order.time_in_force == :gtc
    end

    test "limit_sell/5 creates limit sell order", %{account: account} do
      assert {:ok, %Order{} = order} = Trading.limit_sell(
        account.id, "BTCUSD", Decimal.new("0.1"), Decimal.new("55000"), "USD"
      )
      
      assert order.side == :sell
      assert order.order_type == :limit
      assert Decimal.equal?(order.price, Decimal.new("55000"))
    end

    test "stop_loss/6 creates stop loss order", %{account: account} do
      assert {:ok, %Order{} = order} = Trading.stop_loss(
        account.id, "BTCUSD", Decimal.new("0.1"), Decimal.new("48000"), :sell, "USD"
      )
      
      assert order.side == :sell
      assert order.order_type == :stop
      assert Decimal.equal?(order.stop_price, Decimal.new("48000"))
    end

    test "get_order/2 retrieves order by client_order_id", %{account: account} do
      {:ok, created_order} = Trading.market_buy(account.id, "BTCUSD", Decimal.new("0.1"), "USD")
      
      retrieved_order = Trading.get_order(account.id, created_order.client_order_id)
      assert retrieved_order != nil
      assert retrieved_order.id == created_order.id
    end

    test "list_active_orders/1 returns active orders", %{account: account} do
      {:ok, _order1} = Trading.limit_buy(account.id, "BTCUSD", Decimal.new("0.1"), Decimal.new("45000"), "USD")
      {:ok, _order2} = Trading.limit_sell(account.id, "ETHUSD", Decimal.new("1.0"), Decimal.new("3200"), "USD")
      
      active_orders = Trading.list_active_orders(account.id)
      assert length(active_orders) == 2
    end

    test "list_order_history/2 returns order history", %{account: account} do
      {:ok, _order1} = Trading.market_buy(account.id, "BTCUSD", Decimal.new("0.1"), "USD")
      {:ok, _order2} = Trading.limit_buy(account.id, "ETHUSD", Decimal.new("1.0"), Decimal.new("3000"), "USD")
      
      history = Trading.list_order_history(account.id)
      assert length(history) == 2
    end

    test "list_order_history/2 with filters", %{account: account} do
      {:ok, _order1} = Trading.market_buy(account.id, "BTCUSD", Decimal.new("0.1"), "USD")
      {:ok, _order2} = Trading.limit_buy(account.id, "ETHUSD", Decimal.new("1.0"), Decimal.new("3000"), "USD")
      
      # Filter by instrument
      btc_orders = Trading.list_order_history(account.id, %{instrument_id: "BTCUSD"})
      assert length(btc_orders) == 1
      assert hd(btc_orders).instrument_id == "BTCUSD"
      
      # Filter by limit
      limited_orders = Trading.list_order_history(account.id, %{limit: 1})
      assert length(limited_orders) == 1
    end
  end

  describe "trades" do
    setup do
      {:ok, account} = Portfolio.create_account(%{
        name: "Test Trading Account",
        account_type: :cash,
        base_currency: "USD"
      })
      
      Portfolio.update_balance(account.id, "USD", Decimal.new("10000"))
      
      {:ok, order} = Trading.limit_buy(account.id, "BTCUSD", Decimal.new("0.1"), Decimal.new("50000"), "USD")
      
      %{account: account, order: order}
    end

    test "create_trade/1 creates trade record", %{account: account} do
      trade_attrs = %{
        trade_id: "trade_123",
        account_id: account.id,
        instrument_id: "BTCUSD",
        side: :buy,
        quantity: Decimal.new("0.1"),
        price: Decimal.new("50000"),
        currency: "USD",
        executed_at: DateTime.utc_now()
      }
      
      assert {:ok, %Trade{} = trade} = Trading.create_trade(trade_attrs)
      assert trade.trade_id == "trade_123"
      assert trade.account_id == account.id
      assert trade.instrument_id == "BTCUSD"
      assert trade.side == :buy
      assert Decimal.equal?(trade.quantity, Decimal.new("0.1"))
      assert Decimal.equal?(trade.price, Decimal.new("50000"))
      assert Decimal.equal?(trade.value, Decimal.new("5000"))  # 0.1 * 50000
    end

    test "create_trade_from_fill/4 creates trade from order fill", %{order: order} do
      fill_quantity = Decimal.new("0.05")
      fill_price = Decimal.new("50500")
      
      assert {:ok, %Trade{} = trade} = Trading.create_trade_from_fill(order, fill_quantity, fill_price)
      
      assert trade.account_id == order.account_id
      assert trade.order_id == order.id
      assert trade.instrument_id == order.instrument_id
      assert trade.side == order.side
      assert Decimal.equal?(trade.quantity, fill_quantity)
      assert Decimal.equal?(trade.price, fill_price)
      assert Decimal.equal?(trade.value, Decimal.new("2525"))  # 0.05 * 50500
    end

    test "list_trade_history/2 returns trade history", %{account: account, order: order} do
      # Create some trades
      Trading.create_trade_from_fill(order, Decimal.new("0.03"), Decimal.new("50000"))
      Trading.create_trade_from_fill(order, Decimal.new("0.02"), Decimal.new("50100"))
      
      trades = Trading.list_trade_history(account.id)
      assert length(trades) == 2
    end

    test "list_trade_history/2 with filters", %{account: account, order: order} do
      # Create trades for different instruments
      Trading.create_trade_from_fill(order, Decimal.new("0.05"), Decimal.new("50000"))
      
      # Create another order and trade for different instrument
      {:ok, eth_order} = Trading.limit_buy(account.id, "ETHUSD", Decimal.new("1.0"), Decimal.new("3000"), "USD")
      Trading.create_trade_from_fill(eth_order, Decimal.new("0.5"), Decimal.new("3000"))
      
      # Filter by instrument
      btc_trades = Trading.list_trade_history(account.id, %{instrument_id: "BTCUSD"})
      assert length(btc_trades) == 1
      assert hd(btc_trades).instrument_id == "BTCUSD"
      
      eth_trades = Trading.list_trade_history(account.id, %{instrument_id: "ETHUSD"})
      assert length(eth_trades) == 1
      assert hd(eth_trades).instrument_id == "ETHUSD"
    end

    test "list_order_trades/1 returns trades for specific order", %{order: order} do
      # Create multiple fills for the same order
      Trading.create_trade_from_fill(order, Decimal.new("0.03"), Decimal.new("50000"))
      Trading.create_trade_from_fill(order, Decimal.new("0.02"), Decimal.new("50100"))
      
      order_trades = Trading.list_order_trades(order.id)
      assert length(order_trades) == 2
      assert Enum.all?(order_trades, fn trade -> trade.order_id == order.id end)
    end
  end

  describe "trading statistics" do
    setup do
      {:ok, account} = Portfolio.create_account(%{
        name: "Test Trading Account",
        account_type: :cash,
        base_currency: "USD"
      })
      
      Portfolio.update_balance(account.id, "USD", Decimal.new("10000"))
      
      # Create some sample trades
      start_date = DateTime.utc_now() |> DateTime.add(-7, :day)
      end_date = DateTime.utc_now()
      
      {:ok, order1} = Trading.limit_buy(account.id, "BTCUSD", Decimal.new("0.1"), Decimal.new("50000"), "USD")
      {:ok, order2} = Trading.limit_sell(account.id, "ETHUSD", Decimal.new("1.0"), Decimal.new("3000"), "USD")
      
      Trading.create_trade_from_fill(order1, Decimal.new("0.1"), Decimal.new("50000"))
      Trading.create_trade_from_fill(order2, Decimal.new("1.0"), Decimal.new("3000"))
      
      %{account: account, start_date: start_date, end_date: end_date}
    end

    test "get_trading_stats/3 returns trading statistics", %{account: account, start_date: start_date, end_date: end_date} do
      stats = Trading.get_trading_stats(account.id, start_date, end_date)
      
      assert stats.total_trades == 2
      assert Decimal.equal?(stats.total_volume, Decimal.new("8000"))  # 5000 + 3000
      assert Decimal.equal?(stats.total_commission, Decimal.new("0"))
      assert stats.buy_trades == 1
      assert stats.sell_trades == 1
      assert stats.instruments_traded == 2
      assert stats.period_start == start_date
      assert stats.period_end == end_date
    end
  end

  describe "order validation" do
    setup do
      {:ok, account} = Portfolio.create_account(%{
        name: "Test Trading Account",
        account_type: :cash,
        base_currency: "USD"
      })
      
      %{account: account}
    end

    test "order creation fails with invalid data", %{account: account} do
      # Missing required fields
      assert {:error, changeset} = Trading.create_order(account.id, %{})
      assert changeset.errors[:client_order_id]
      assert changeset.errors[:instrument_id]
      assert changeset.errors[:side]
      assert changeset.errors[:order_type]
      assert changeset.errors[:quantity]
      assert changeset.errors[:currency]
    end

    test "limit order requires price", %{account: account} do
      order_params = %{
        client_order_id: "test_order_1",
        instrument_id: "BTCUSD",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.1"),
        currency: "USD"
        # Missing price
      }
      
      assert {:error, changeset} = Trading.create_order(account.id, order_params)
      assert changeset.errors[:price]
    end

    test "stop order requires stop_price", %{account: account} do
      order_params = %{
        client_order_id: "test_order_2",
        instrument_id: "BTCUSD",
        side: :sell,
        order_type: :stop,
        quantity: Decimal.new("0.1"),
        currency: "USD"
        # Missing stop_price
      }
      
      assert {:error, changeset} = Trading.create_order(account.id, order_params)
      assert changeset.errors[:stop_price]
    end
  end
end
