defmodule TtQuant.MultiCurrencyTest do
  use TtQuant.DataCase

  alias TtQuant.Portfolio
  alias TtQuant.Portfolio.{Account, Balance, Position}
  alias TtQuant.Instruments.Instrument
  alias TtQuant.Trading.Order
  alias TtQuant.Core

  describe "multi-currency balance management" do
    setup do
      # Create test account
      account_attrs = %{
        id: "multi-currency-account",
        name: "Multi-Currency Test Account",
        account_type: :margin,
        base_currency: "USD",
        is_active: true
      }

      {:ok, account} = Portfolio.create_account(account_attrs)

      # Create test instruments for different currency pairs
      instruments = [
        %{
          symbol: "EURUSD",
          name: "EUR/USD Spot",
          instrument_type: :spot,
          base_currency: "EUR",
          quote_currency: "USD",
          tick_size: Decimal.new("0.00001"),
          lot_size: Decimal.new("0.01"),
          min_quantity: Decimal.new("0.01"),
          max_quantity: Decimal.new("1000000"),
          price_precision: 5,
          quantity_precision: 2,
          maker_fee: Decimal.new("0.0001"),
          taker_fee: Decimal.new("0.0001"),
          venue: "forex"
        },
        %{
          symbol: "BTCUSD",
          name: "Bitcoin/USD Spot",
          instrument_type: :spot,
          base_currency: "BTC",
          quote_currency: "USD",
          tick_size: Decimal.new("0.01"),
          lot_size: Decimal.new("0.00001"),
          min_quantity: Decimal.new("0.00001"),
          max_quantity: Decimal.new("1000"),
          price_precision: 2,
          quantity_precision: 8,
          maker_fee: Decimal.new("0.001"),
          taker_fee: Decimal.new("0.001"),
          venue: "crypto"
        },
        %{
          symbol: "USDJPY",
          name: "USD/JPY Spot",
          instrument_type: :spot,
          base_currency: "USD",
          quote_currency: "JPY",
          tick_size: Decimal.new("0.001"),
          lot_size: Decimal.new("0.01"),
          min_quantity: Decimal.new("0.01"),
          max_quantity: Decimal.new("1000000"),
          price_precision: 3,
          quantity_precision: 2,
          maker_fee: Decimal.new("0.0001"),
          taker_fee: Decimal.new("0.0001"),
          venue: "forex"
        }
      ]

      created_instruments = Enum.map(instruments, fn attrs ->
        {:ok, instrument} = Repo.insert(Instrument.changeset(%Instrument{}, attrs))
        instrument
      end)

      %{account: account, instruments: created_instruments}
    end

    test "manages balances in multiple currencies", %{account: account} do
      # Add balances in different currencies
      currencies_and_amounts = [
        {"USD", "10000.00"},
        {"EUR", "5000.00"},
        {"BTC", "0.5"},
        {"JPY", "1000000.00"}
      ]

      # Create balances
      balances = Enum.map(currencies_and_amounts, fn {currency, amount} ->
        {:ok, balance} = Portfolio.update_balance(account.id, currency, Decimal.new(amount))
        balance
      end)

      # Verify all balances were created
      assert length(balances) == 4

      # Verify each balance
      Enum.zip(balances, currencies_and_amounts)
      |> Enum.each(fn {balance, {currency, amount}} ->
        assert balance.currency == currency
        assert Decimal.equal?(balance.total, Decimal.new(amount))
        assert Decimal.equal?(balance.available, Decimal.new(amount))
        assert Decimal.equal?(balance.locked, Decimal.new("0"))
      end)
    end

    test "calculates portfolio value in base currency", %{account: account} do
      # Add balances in different currencies
      Portfolio.update_balance(account.id, "USD", Decimal.new("1000.00"))
      Portfolio.update_balance(account.id, "EUR", Decimal.new("500.00"))
      Portfolio.update_balance(account.id, "BTC", Decimal.new("0.1"))

      # Mock exchange rates (in real system, these would come from market data)
      exchange_rates = %{
        "EUR" => Decimal.new("1.10"),  # 1 EUR = 1.10 USD
        "BTC" => Decimal.new("50000.00")  # 1 BTC = 50000 USD
      }

      # Calculate total portfolio value in USD
      balances = Portfolio.get_balances(account.id)

      total_usd_value = Enum.reduce(balances, Decimal.new("0"), fn balance, acc ->
        usd_value = case balance.currency do
          "USD" -> balance.total
          currency ->
            rate = Map.get(exchange_rates, currency, Decimal.new("1"))
            Decimal.mult(balance.total, rate)
        end
        Decimal.add(acc, usd_value)
      end)

      # Expected: 1000 USD + (500 EUR * 1.10) + (0.1 BTC * 50000) = 1000 + 550 + 5000 = 6550 USD
      expected_total = Decimal.new("6550.00")
      assert Decimal.equal?(total_usd_value, expected_total)
    end

    test "handles currency conversion for orders", %{account: account, instruments: instruments} do
      # Add USD balance
      Portfolio.update_balance(account.id, "USD", Decimal.new("10000.00"))

      # Find EUR/USD instrument
      eurusd_instrument = Enum.find(instruments, &(&1.symbol == "EURUSD"))

      # Create EUR buy order (buying EUR with USD)
      order = %Order{
        id: "eur-buy-order",
        client_order_id: "client-eur-buy",
        account_id: account.id,
        instrument_id: "EURUSD@forex",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("1000.00"),  # 1000 EUR
        price: Decimal.new("1.1000"),      # at 1.1000 USD per EUR
        currency: "USD",
        status: :pending_new,
        time_in_force: :gtc
      }

      # Calculate required USD (quantity * price)
      required_usd = Decimal.mult(order.quantity, order.price)
      assert Decimal.equal?(required_usd, Decimal.new("1100.00"))

      # Verify we have sufficient USD balance
      usd_balance_amount = Portfolio.get_balance(account.id, "USD")
      assert Decimal.compare(usd_balance_amount, required_usd) == :gt
    end

    test "manages cross-currency positions", %{account: account, instruments: instruments} do
      # Add initial balances
      Portfolio.update_balance(account.id, "USD", Decimal.new("10000.00"))
      Portfolio.update_balance(account.id, "BTC", Decimal.new("0.0"))

      # Find BTC/USD instrument
      btcusd_instrument = Enum.find(instruments, &(&1.symbol == "BTCUSD"))

      # Create a BTC position (simulate filled order)
      position_attrs = %{
        account_id: account.id,
        instrument_id: "BTCUSD@crypto",
        side: :long,
        quantity: Decimal.new("0.1"),
        average_price: Decimal.new("45000.00"),
        current_price: Decimal.new("50000.00"),
        unrealized_pnl: Decimal.new("500.00"),  # (50000 - 45000) * 0.1
        realized_pnl: Decimal.new("0.00"),
        currency: "USD"
      }

      {:ok, position} = Repo.insert(Position.changeset(%Position{}, position_attrs))

      # Verify position
      assert Decimal.equal?(position.quantity, Decimal.new("0.1"))
      assert Decimal.equal?(position.average_price, Decimal.new("45000.00"))
      assert Decimal.equal?(position.unrealized_pnl, Decimal.new("500.00"))

      # Calculate position value in different currencies
      position_value_usd = Decimal.mult(position.quantity, position.current_price)
      assert Decimal.equal?(position_value_usd, Decimal.new("5000.00"))

      # If we had EUR exchange rate of 1.10, position value in EUR would be:
      eur_rate = Decimal.new("1.10")
      position_value_eur = Decimal.div(position_value_usd, eur_rate)
      expected_eur_value = Decimal.new("4545.45")

      # Round to 2 decimal places for comparison
      position_value_eur_rounded = Decimal.round(position_value_eur, 2)
      assert Decimal.equal?(position_value_eur_rounded, expected_eur_value)
    end
  end

  describe "currency precision and rounding" do
    test "handles different currency precisions correctly" do
      # Test different currency precisions
      test_cases = [
        {"USD", "1234.56", 2},
        {"JPY", "123456", 0},
        {"BTC", "0.12345678", 8},
        {"EUR", "1234.56", 2},
        {"ETH", "1.234567890123456789", 18}
      ]

      Enum.each(test_cases, fn {currency, amount_str, expected_precision} ->
        amount = Decimal.new(amount_str)

        # Test that we can handle the precision
        assert Decimal.to_string(amount) == amount_str

        # Test rounding to expected precision
        rounded = Decimal.round(amount, expected_precision)

        case currency do
          "JPY" ->
            assert Decimal.equal?(rounded, Decimal.new("123456"))
          "ETH" ->
            # For ETH, we might want to limit to a reasonable precision like 8
            limited_precision = Decimal.round(amount, 8)
            assert Decimal.to_string(limited_precision) == "1.23456789"
          _ ->
            assert Decimal.equal?(rounded, amount)
        end
      end)
    end

    test "validates currency codes" do
      valid_currencies = ["USD", "EUR", "GBP", "JPY", "CHF", "CAD", "AUD", "NZD", "BTC", "ETH", "USDT"]
      invalid_currencies = ["", "US", "DOLLAR", "bitcoin", "usd", "123"]

      Enum.each(valid_currencies, fn currency ->
        assert String.length(currency) >= 3
        assert String.length(currency) <= 5
        assert currency == String.upcase(currency)
        assert Regex.match?(~r/^[A-Z]+$/, currency)
      end)

      Enum.each(invalid_currencies, fn currency ->
        refute (String.length(currency) >= 3 and
                String.length(currency) <= 5 and
                currency == String.upcase(currency) and
                Regex.match?(~r/^[A-Z]+$/, currency))
      end)
    end
  end

  describe "exchange rate calculations" do
    test "calculates cross rates correctly" do
      # Mock direct rates
      rates = %{
        "EURUSD" => Decimal.new("1.1000"),
        "GBPUSD" => Decimal.new("1.3000"),
        "USDJPY" => Decimal.new("110.00")
      }

      # Calculate cross rates
      # EUR/GBP = EUR/USD / GBP/USD = 1.1000 / 1.3000 = 0.8462
      eur_gbp = Decimal.div(rates["EURUSD"], rates["GBPUSD"])
      expected_eur_gbp = Decimal.new("0.8462")

      # Round to 4 decimal places
      eur_gbp_rounded = Decimal.round(eur_gbp, 4)
      assert Decimal.equal?(eur_gbp_rounded, expected_eur_gbp)

      # EUR/JPY = EUR/USD * USD/JPY = 1.1000 * 110.00 = 121.00
      eur_jpy = Decimal.mult(rates["EURUSD"], rates["USDJPY"])
      expected_eur_jpy = Decimal.new("121.00")
      assert Decimal.equal?(eur_jpy, expected_eur_jpy)

      # GBP/JPY = GBP/USD * USD/JPY = 1.3000 * 110.00 = 143.00
      gbp_jpy = Decimal.mult(rates["GBPUSD"], rates["USDJPY"])
      expected_gbp_jpy = Decimal.new("143.00")
      assert Decimal.equal?(gbp_jpy, expected_gbp_jpy)
    end

    test "handles inverse rates" do
      # If we have EUR/USD = 1.1000, then USD/EUR = 1/1.1000 = 0.9091
      eur_usd = Decimal.new("1.1000")
      usd_eur = Decimal.div(Decimal.new("1"), eur_usd)
      expected_usd_eur = Decimal.new("0.9091")

      # Round to 4 decimal places
      usd_eur_rounded = Decimal.round(usd_eur, 4)
      assert Decimal.equal?(usd_eur_rounded, expected_usd_eur)
    end
  end

  describe "risk calculations with multiple currencies" do
    setup do
      account_attrs = %{
        id: "risk-test-account",
        name: "Risk Test Account",
        account_type: :margin,
        base_currency: "USD",
        is_active: true
      }

      {:ok, account} = Portfolio.create_account(account_attrs)
      %{account: account}
    end

    test "calculates total exposure across currencies", %{account: account} do
      # Add positions in different currencies
      positions_data = [
        {"EURUSD@forex", :long, "10000.00", "1.1000", "USD"},
        {"GBPUSD@forex", :short, "5000.00", "1.3000", "USD"},
        {"BTCUSD@crypto", :long, "0.1", "50000.00", "USD"}
      ]

      positions = Enum.map(positions_data, fn {instrument_id, side, quantity, price, currency} ->
        position_attrs = %{
          account_id: account.id,
          instrument_id: instrument_id,
          side: side,
          quantity: Decimal.new(quantity),
          average_price: Decimal.new(price),
          current_price: Decimal.new(price),
          unrealized_pnl: Decimal.new("0.00"),
          realized_pnl: Decimal.new("0.00"),
          currency: currency
        }

        {:ok, position} = Repo.insert(Position.changeset(%Position{}, position_attrs))
        position
      end)

      # Calculate total exposure in USD
      total_exposure = Enum.reduce(positions, Decimal.new("0"), fn position, acc ->
        position_value = Decimal.mult(position.quantity, position.average_price)

        # For short positions, we consider the absolute exposure
        exposure = case position.side do
          :long -> position_value
          :short -> position_value
        end

        Decimal.add(acc, exposure)
      end)

      # Expected: 10000*1.1 + 5000*1.3 + 0.1*50000 = 11000 + 6500 + 5000 = 22500
      expected_exposure = Decimal.new("22500.00")
      assert Decimal.equal?(total_exposure, expected_exposure)
    end

    test "calculates net exposure by currency", %{account: account} do
      # Add positive balances
      Portfolio.update_balance(account.id, "USD", Decimal.new("10000.00"))
      Portfolio.update_balance(account.id, "GBP", Decimal.new("1000.00"))

      balances = Portfolio.get_balances(account.id)

      # Calculate net exposure by currency
      net_exposures = Enum.reduce(balances, %{}, fn balance, acc ->
        Map.put(acc, balance.currency, balance.total)
      end)

      assert Decimal.equal?(net_exposures["USD"], Decimal.new("10000.00"))
      assert Decimal.equal?(net_exposures["GBP"], Decimal.new("1000.00"))

      # Test that we have the expected currencies
      assert Map.has_key?(net_exposures, "USD")
      assert Map.has_key?(net_exposures, "GBP")
    end
  end
end
