defmodule TtQuant.ElixirRustIntegrationTest do
  use TtQuant.DataCase

  alias TtQuant.Core
  alias TtQuant.Trading.Order
  alias TtQuant.Portfolio.{Account, Balance, Position}
  alias TtQuant.Instruments.Instrument

  describe "Rust NIF integration" do
    test "basic NIF function works" do
      # Test the basic add function to verify NIF is loaded
      assert Core.add(1, 2) == 3
      assert Core.add(-5, 10) == 5
      assert Core.add(0, 0) == 0
    end

    test "NIF handles large numbers" do
      large_num = 1_000_000_000
      assert Core.add(large_num, large_num) == 2_000_000_000
    end

    test "NIF error handling" do
      # Test that NIF functions handle errors gracefully
      # This will be expanded when we have more complex NIF functions
      assert is_integer(Core.add(1, 2))
    end
  end

  describe "data type conversion" do
    setup do
      # Create test data for conversion tests
      account_attrs = %{
        id: "test-account-123",
        name: "Test Account",
        account_type: :margin,
        base_currency: "USD",
        is_active: true
      }

      instrument_attrs = %{
        symbol: "BTCUSDT",
        name: "Bitcoin/USDT Spot",
        instrument_type: :spot,
        base_currency: "BTC",
        quote_currency: "USDT",
        tick_size: Decimal.new("0.01"),
        lot_size: Decimal.new("0.00001"),
        min_quantity: Decimal.new("0.00001"),
        max_quantity: Decimal.new("1000"),
        price_precision: 2,
        quantity_precision: 8,
        maker_fee: Decimal.new("0.001"),
        taker_fee: Decimal.new("0.001"),
        venue: "test_venue"
      }

      {:ok, account} = Repo.insert(Account.changeset(%Account{}, account_attrs))
      {:ok, instrument} = Repo.insert(Instrument.changeset(%Instrument{}, instrument_attrs))

      %{account: account, instrument: instrument}
    end

    test "Order struct conversion", %{account: account} do
      order = %Order{
        id: "order-123",
        client_order_id: "client-order-123",
        account_id: account.id,
        instrument_id: "BTCUSDT@test_venue",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("1.0"),
        price: Decimal.new("50000.00"),
        currency: "USDT",
        status: :pending_new,
        time_in_force: :gtc
      }

      # Test that Order can be converted to Rust format
      # This will be implemented when we have the actual NIF functions
      rust_order = Order.to_rust_order(order)

      # For now, just verify the function exists and returns something
      assert is_map(rust_order) || is_tuple(rust_order)
    end

    test "Balance struct conversion", %{account: account} do
      balance = %Balance{
        account_id: account.id,
        currency: "USD",
        total: Decimal.new("10000.00"),
        available: Decimal.new("9000.00"),
        locked: Decimal.new("1000.00")
      }

      # Test Balance conversion (placeholder for future NIF integration)
      assert balance.currency == "USD"
      assert Decimal.equal?(balance.total, Decimal.new("10000.00"))
    end

    test "Position struct conversion", %{account: account} do
      position = %Position{
        account_id: account.id,
        instrument_id: "BTCUSDT@test_venue",
        side: :long,
        quantity: Decimal.new("0.5"),
        average_price: Decimal.new("45000.00"),
        unrealized_pnl: Decimal.new("2500.00"),
        realized_pnl: Decimal.new("0.00"),
        currency: "USDT"
      }

      # Test Position conversion (placeholder for future NIF integration)
      assert position.side == :long
      assert Decimal.equal?(position.quantity, Decimal.new("0.5"))
    end
  end

  describe "currency and money handling" do
    test "currency code validation" do
      # Test common currency codes
      valid_currencies = ["USD", "EUR", "GBP", "JPY", "BTC", "ETH", "USDT"]

      Enum.each(valid_currencies, fn currency ->
        assert String.length(currency) >= 3
        assert String.length(currency) <= 4
        assert currency == String.upcase(currency)
      end)
    end

    test "decimal precision handling" do
      # Test that Decimal values maintain precision
      price = Decimal.new("50000.123456789")
      assert Decimal.to_string(price) == "50000.123456789"

      # Test rounding
      rounded = Decimal.round(price, 2)
      assert Decimal.to_string(rounded) == "50000.12"
    end

    test "money arithmetic" do
      # Test basic money operations
      amount1 = Decimal.new("1000.50")
      amount2 = Decimal.new("500.25")

      sum = Decimal.add(amount1, amount2)
      assert Decimal.equal?(sum, Decimal.new("1500.75"))

      diff = Decimal.sub(amount1, amount2)
      assert Decimal.equal?(diff, Decimal.new("500.25"))

      product = Decimal.mult(amount1, Decimal.new("2"))
      assert Decimal.equal?(product, Decimal.new("2001.00"))
    end
  end

  describe "performance benchmarks" do
    test "NIF performance vs Elixir" do
      # Simple performance comparison
      elixir_start = System.monotonic_time(:microsecond)

      # Elixir calculation
      elixir_result = Enum.reduce(1..1000, 0, fn i, acc -> acc + i end)

      elixir_end = System.monotonic_time(:microsecond)
      elixir_time = elixir_end - elixir_start

      # NIF calculation (using simple add function repeatedly)
      nif_start = System.monotonic_time(:microsecond)

      nif_result = Enum.reduce(1..1000, 0, fn i, acc -> Core.add(acc, i) end)

      nif_end = System.monotonic_time(:microsecond)
      nif_time = nif_end - nif_start

      # Results should be the same
      assert elixir_result == nif_result
      assert elixir_result == 500_500  # Sum of 1 to 1000

      # Log performance comparison
      IO.puts("Elixir time: #{elixir_time} microseconds")
      IO.puts("NIF time: #{nif_time} microseconds")

      # Both should complete in reasonable time (less than 1 second)
      assert elixir_time < 1_000_000
      assert nif_time < 1_000_000
    end
  end

  describe "error handling and edge cases" do
    test "handles nil values gracefully" do
      # Test that our functions handle nil inputs appropriately
      order = %Order{
        id: nil,
        client_order_id: "test",
        account_id: "test-account",
        instrument_id: "BTCUSDT@test_venue",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("1.0"),
        price: Decimal.new("50000.00"),
        currency: "USDT",
        status: :pending_new,
        time_in_force: :gtc
      }

      # Should handle nil ID gracefully
      assert order.id == nil
      assert order.client_order_id == "test"
    end

    test "handles invalid decimal values" do
      # Test edge cases with Decimal
      assert_raise Decimal.Error, fn ->
        Decimal.new("invalid")
      end

      # Test very small numbers
      tiny = Decimal.new("0.********")
      assert Decimal.positive?(tiny)

      # Test very large numbers
      huge = Decimal.new("999999999999999999.99")
      assert Decimal.positive?(huge)
    end

    test "handles currency conversion edge cases" do
      # Test same currency conversion
      usd_amount = Decimal.new("1000.00")

      # Converting USD to USD should return the same amount
      # (This will be implemented when we have actual conversion functions)
      assert Decimal.equal?(usd_amount, usd_amount)

      # Test zero amounts
      zero = Decimal.new("0.00")
      assert Decimal.equal?(zero, Decimal.new("0"))
    end
  end

  describe "concurrent operations" do
    test "NIF functions are thread-safe" do
      # Test concurrent access to NIF functions
      tasks = for i <- 1..10 do
        Task.async(fn ->
          Core.add(i, i * 2)
        end)
      end

      results = Task.await_many(tasks)

      # Verify all results are correct
      expected_results = for i <- 1..10, do: i + (i * 2)
      assert results == expected_results
    end

    test "concurrent order processing simulation" do
      # Simulate concurrent order creation
      tasks = for i <- 1..5 do
        Task.async(fn ->
          %Order{
            id: "order-#{i}",
            client_order_id: "client-order-#{i}",
            account_id: "test-account",
            instrument_id: "BTCUSDT@test_venue",
            side: if(rem(i, 2) == 0, do: :buy, else: :sell),
            order_type: :limit,
            quantity: Decimal.new("#{i}.0"),
            price: Decimal.new("#{50000 + i * 100}.00"),
            currency: "USDT",
            status: :pending_new,
            time_in_force: :gtc
          }
        end)
      end

      orders = Task.await_many(tasks)

      # Verify all orders were created correctly
      assert length(orders) == 5

      Enum.with_index(orders, 1)
      |> Enum.each(fn {order, i} ->
        assert order.id == "order-#{i}"
        assert Decimal.equal?(order.quantity, Decimal.new("#{i}.0"))
      end)
    end
  end
end
