defmodule TtQuant.OrderLifecycleSimpleTest do
  use TtQuant.DataCase

  alias TtQuant.{Portfolio, Trading}
  alias TtQuant.Instruments.Instrument

  describe "basic order lifecycle" do
    setup do
      # Create test account
      account_attrs = %{
        id: "simple-order-account",
        name: "Simple Order Test Account",
        account_type: :margin,
        base_currency: "USD",
        is_active: true
      }

      {:ok, account} = Portfolio.create_account(account_attrs)

      # Add initial balance
      Portfolio.update_balance(account.id, "USD", Decimal.new("100000.00"))

      # Create test instrument
      instrument_attrs = %{
        symbol: "BTCUSD",
        name: "Bitcoin/USD Spot",
        instrument_type: :spot,
        base_currency: "BTC",
        quote_currency: "USD",
        tick_size: Decimal.new("0.01"),
        lot_size: Decimal.new("0.00001"),
        min_quantity: Decimal.new("0.00001"),
        max_quantity: Decimal.new("1000"),
        price_precision: 2,
        quantity_precision: 8,
        maker_fee: Decimal.new("0.001"),
        taker_fee: Decimal.new("0.001"),
        venue: "crypto"
      }

      {:ok, instrument} = Repo.insert(Instrument.changeset(%Instrument{}, instrument_attrs))

      %{account: account, instrument: instrument}
    end

    test "creates buy order successfully", %{account: account} do
      # Create buy order
      order_attrs = %{
        client_order_id: "simple-buy-001",
        instrument_id: "BTCUSD@crypto",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.1"),
        price: Decimal.new("50000.00"),
        currency: "USD",
        time_in_force: :gtc
      }

      {:ok, order} = Trading.create_order(account.id, order_attrs)

      # Verify order creation
      assert order.account_id == account.id
      assert order.client_order_id == "simple-buy-001"
      assert order.side == :buy
      assert order.order_type == :limit
      assert Decimal.equal?(order.quantity, Decimal.new("0.1"))
      assert Decimal.equal?(order.price, Decimal.new("50000.00"))
      assert order.currency == "USD"
      assert order.time_in_force == :gtc
      assert order.status == :initialized
    end

    test "creates sell order successfully", %{account: account} do
      # Create sell order
      order_attrs = %{
        client_order_id: "simple-sell-001",
        instrument_id: "BTCUSD@crypto",
        side: :sell,
        order_type: :limit,
        quantity: Decimal.new("0.05"),
        price: Decimal.new("51000.00"),
        currency: "USD",
        time_in_force: :gtc
      }

      {:ok, order} = Trading.create_order(account.id, order_attrs)

      # Verify order creation
      assert order.account_id == account.id
      assert order.side == :sell
      assert Decimal.equal?(order.quantity, Decimal.new("0.05"))
      assert Decimal.equal?(order.price, Decimal.new("51000.00"))
      assert order.status == :initialized
    end

    test "creates market buy order", %{account: account} do
      {:ok, order} = Trading.market_buy(
        account.id,
        "BTCUSD@crypto",
        Decimal.new("0.1"),
        "USD"
      )

      assert order.side == :buy
      assert order.order_type == :market
      assert Decimal.equal?(order.quantity, Decimal.new("0.1"))
      assert order.time_in_force == :ioc
      assert order.status == :initialized
    end

    test "creates market sell order", %{account: account} do
      {:ok, order} = Trading.market_sell(
        account.id,
        "BTCUSD@crypto",
        Decimal.new("0.05"),
        "USD"
      )

      assert order.side == :sell
      assert order.order_type == :market
      assert Decimal.equal?(order.quantity, Decimal.new("0.05"))
      assert order.time_in_force == :ioc
      assert order.status == :initialized
    end

    test "creates limit buy order", %{account: account} do
      {:ok, order} = Trading.limit_buy(
        account.id,
        "BTCUSD@crypto",
        Decimal.new("0.2"),
        Decimal.new("49000.00"),
        "USD"
      )

      assert order.side == :buy
      assert order.order_type == :limit
      assert Decimal.equal?(order.quantity, Decimal.new("0.2"))
      assert Decimal.equal?(order.price, Decimal.new("49000.00"))
      assert order.status == :initialized
    end

    test "creates limit sell order", %{account: account} do
      {:ok, order} = Trading.limit_sell(
        account.id,
        "BTCUSD@crypto",
        Decimal.new("0.15"),
        Decimal.new("52000.00"),
        "USD"
      )

      assert order.side == :sell
      assert order.order_type == :limit
      assert Decimal.equal?(order.quantity, Decimal.new("0.15"))
      assert Decimal.equal?(order.price, Decimal.new("52000.00"))
      assert order.status == :initialized
    end

    test "updates order status", %{account: account} do
      # Create order first
      order_attrs = %{
        client_order_id: "status-update-001",
        instrument_id: "BTCUSD@crypto",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.1"),
        price: Decimal.new("50000.00"),
        currency: "USD",
        time_in_force: :gtc
      }

      {:ok, order} = Trading.create_order(account.id, order_attrs)
      assert order.status == :initialized

      # Update status to submitted
      :ok = Trading.update_order_status(account.id, order.client_order_id, :submitted)

      # Retrieve updated order
      updated_order = Trading.get_order(account.id, order.client_order_id)
      assert updated_order.status == :submitted
    end

    test "cancels order", %{account: account} do
      # Create order first
      order_attrs = %{
        client_order_id: "cancel-test-001",
        instrument_id: "BTCUSD@crypto",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.1"),
        price: Decimal.new("50000.00"),
        currency: "USD",
        time_in_force: :gtc
      }

      {:ok, order} = Trading.create_order(account.id, order_attrs)

      # Update to submitted status first
      :ok = Trading.update_order_status(account.id, order.client_order_id, :submitted)

      # Cancel order
      {:ok, cancelled_order} = Trading.cancel_order(account.id, order.client_order_id)
      assert cancelled_order.status == :pending_cancel
    end

    test "lists active orders", %{account: account} do
      # Create multiple orders
      order_data = [
        %{client_order_id: "active-1", side: :buy, quantity: "0.1", price: "49000.00"},
        %{client_order_id: "active-2", side: :sell, quantity: "0.05", price: "51000.00"},
        %{client_order_id: "active-3", side: :buy, quantity: "0.2", price: "48000.00"}
      ]

      # Create orders
      created_orders = Enum.map(order_data, fn data ->
        order_attrs = %{
          client_order_id: data.client_order_id,
          instrument_id: "BTCUSD@crypto",
          side: data.side,
          order_type: :limit,
          quantity: Decimal.new(data.quantity),
          price: Decimal.new(data.price),
          currency: "USD",
          time_in_force: :gtc
        }

        {:ok, order} = Trading.create_order(account.id, order_attrs)
        order
      end)

      # Update all to submitted status
      Enum.each(created_orders, fn order ->
        :ok = Trading.update_order_status(account.id, order.client_order_id, :submitted)
      end)

      # List active orders
      active_orders = Trading.list_active_orders(account.id)
      assert length(active_orders) == 3

      # Verify all orders are present
      client_order_ids = Enum.map(active_orders, & &1.client_order_id)
      assert "active-1" in client_order_ids
      assert "active-2" in client_order_ids
      assert "active-3" in client_order_ids
    end

    test "gets order history", %{account: account} do
      # Create and complete some orders
      order_attrs = %{
        client_order_id: "history-001",
        instrument_id: "BTCUSD@crypto",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.1"),
        price: Decimal.new("50000.00"),
        currency: "USD",
        time_in_force: :gtc
      }

      {:ok, order} = Trading.create_order(account.id, order_attrs)

      # Update status to simulate completion
      :ok = Trading.update_order_status(account.id, order.client_order_id, :filled)

      # Get order history
      history = Trading.list_order_history(account.id)
      assert length(history) >= 1

      # Find our order in history
      our_order = Enum.find(history, &(&1.client_order_id == "history-001"))
      assert our_order != nil
      assert our_order.status == :filled
    end
  end

  describe "order validation" do
    setup do
      account_attrs = %{
        id: "validation-account",
        name: "Validation Test Account",
        account_type: :cash,
        base_currency: "USD",
        is_active: true
      }

      {:ok, account} = Portfolio.create_account(account_attrs)
      Portfolio.update_balance(account.id, "USD", Decimal.new("1000.00"))  # Limited balance

      %{account: account}
    end

    test "validates sufficient balance", %{account: account} do
      # Try to create order that exceeds balance
      order_attrs = %{
        client_order_id: "insufficient-balance",
        instrument_id: "BTCUSD@crypto",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("1.0"),
        price: Decimal.new("50000.00"),  # Needs $50,000, but only have $1,000
        currency: "USD",
        time_in_force: :gtc
      }

      # Order creation might succeed, but balance check should fail
      required_amount = Decimal.mult(Decimal.new("1.0"), Decimal.new("50000.00"))
      available_balance = Portfolio.get_balance(account.id, "USD")

      assert Decimal.compare(available_balance, required_amount) == :lt
      assert Portfolio.has_sufficient_balance?(account.id, "USD", required_amount) == false
    end

    test "validates order parameters", %{account: account} do
      # Test with valid parameters
      valid_order_attrs = %{
        client_order_id: "valid-order",
        instrument_id: "BTCUSD@crypto",
        side: :buy,
        order_type: :limit,
        quantity: Decimal.new("0.01"),  # Valid quantity
        price: Decimal.new("100.00"),   # Affordable price
        currency: "USD",
        time_in_force: :gtc
      }

      {:ok, order} = Trading.create_order(account.id, valid_order_attrs)
      assert order.status == :initialized

      # Verify order parameters
      assert Decimal.equal?(order.quantity, Decimal.new("0.01"))
      assert Decimal.equal?(order.price, Decimal.new("100.00"))
      assert order.side == :buy
      assert order.order_type == :limit
    end
  end
end
