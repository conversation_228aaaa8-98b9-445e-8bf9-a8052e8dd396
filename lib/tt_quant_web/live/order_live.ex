defmodule TtQuantWeb.OrderLive do
  @moduledoc """
  LiveView for order management interface.

  Provides functionality to create, view, and manage orders
  with real-time updates and form validation.
  """

  use TtQuantWeb, :live_view

  alias TtQuant.{Trading, Instruments, Portfolio}
  alias TtQuant.Events.EventDispatcher

  @impl true
  def mount(%{"account_id" => account_id}, _session, socket) do
    if connected?(socket) do
      # Subscribe to real-time order events
      EventDispatcher.subscribe_to_orders(account_id)
    end

    # Load initial data
    account = Portfolio.get_account(account_id)
    instruments = Instruments.list_instruments()
    active_orders = Trading.list_active_orders(account_id)
    order_history = Trading.list_order_history(account_id, %{limit: 20})

    # Initialize form
    changeset = Trading.change_order(%{})

    socket =
      socket
      |> assign(:account, account)
      |> assign(:account_id, account_id)
      |> assign(:instruments, instruments)
      |> assign(:active_orders, active_orders)
      |> assign(:order_history, order_history)
      |> assign(:changeset, changeset)
      |> assign(:page_title, "Order Management")
      |> assign(:show_form, false)
      |> assign(:selected_tab, "active")

    {:ok, socket}
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("show_order_form", _params, socket) do
    changeset = Trading.change_order(%{})
    socket =
      socket
      |> assign(:show_form, true)
      |> assign(:changeset, changeset)

    {:noreply, socket}
  end

  @impl true
  def handle_event("hide_order_form", _params, socket) do
    socket = assign(socket, :show_form, false)
    {:noreply, socket}
  end

  @impl true
  def handle_event("validate_order", %{"order" => order_params}, socket) do
    changeset =
      %{}
      |> Trading.change_order(order_params)
      |> Map.put(:action, :validate)

    socket = assign(socket, :changeset, changeset)
    {:noreply, socket}
  end

  @impl true
  def handle_event("create_order", %{"order" => order_params}, socket) do
    account_id = socket.assigns.account_id

    case Trading.create_order(account_id, order_params) do
      {:ok, order} ->
        # Update active orders list
        updated_orders = [order | socket.assigns.active_orders]

        socket =
          socket
          |> assign(:active_orders, updated_orders)
          |> assign(:show_form, false)
          |> assign(:changeset, Trading.change_order(%{}))
          |> put_flash(:info, "Order created successfully")

        {:noreply, socket}

      {:error, changeset} ->
        socket =
          socket
          |> assign(:changeset, changeset)
          |> put_flash(:error, "Failed to create order")

        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("cancel_order", %{"client_order_id" => client_order_id}, socket) do
    account_id = socket.assigns.account_id

    case Trading.cancel_order(account_id, client_order_id) do
      {:ok, order} ->
        # Update orders in the list
        updated_orders = Enum.map(socket.assigns.active_orders, fn o ->
          if o.client_order_id == client_order_id, do: order, else: o
        end)

        socket =
          socket
          |> assign(:active_orders, updated_orders)
          |> put_flash(:info, "Order cancelled successfully")

        {:noreply, socket}

      {:error, reason} ->
        socket = put_flash(socket, :error, "Failed to cancel order: #{inspect(reason)}")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("select_tab", %{"tab" => tab}, socket) do
    socket = assign(socket, :selected_tab, tab)
    {:noreply, socket}
  end

  @impl true
  def handle_event("refresh_orders", _params, socket) do
    account_id = socket.assigns.account_id

    active_orders = Trading.list_active_orders(account_id)
    order_history = Trading.list_order_history(account_id, %{limit: 20})

    socket =
      socket
      |> assign(:active_orders, active_orders)
      |> assign(:order_history, order_history)
      |> put_flash(:info, "Orders refreshed")

    {:noreply, socket}
  end

  # Handle real-time events

  @impl true
  def handle_info(%{event_type: :order_created, data: data}, socket) do
    # Add new order to active orders list
    if client_order_id = Map.get(data, :client_order_id) do
      case Trading.get_order(socket.assigns.account_id, client_order_id) do
        nil -> {:noreply, socket}
        order ->
          updated_orders = [order | socket.assigns.active_orders]
          socket = assign(socket, :active_orders, updated_orders)
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info(%{event_type: :order_filled, data: data}, socket) do
    client_order_id = Map.get(data, :client_order_id)

    # Update order in active orders list
    updated_orders = Enum.map(socket.assigns.active_orders, fn order ->
      if order.client_order_id == client_order_id do
        Trading.get_order(socket.assigns.account_id, client_order_id) || order
      else
        order
      end
    end)

    socket = assign(socket, :active_orders, updated_orders)
    {:noreply, socket}
  end

  @impl true
  def handle_info(%{event_type: event_type, data: data}, socket) when event_type in [:order_cancelled, :order_rejected] do
    client_order_id = Map.get(data, :client_order_id)

    # Update order status in active orders list
    updated_orders = Enum.map(socket.assigns.active_orders, fn order ->
      if order.client_order_id == client_order_id do
        Trading.get_order(socket.assigns.account_id, client_order_id) || order
      else
        order
      end
    end)

    socket = assign(socket, :active_orders, updated_orders)
    {:noreply, socket}
  end

  @impl true
  def handle_info(_message, socket) do
    {:noreply, socket}
  end

  # Helper functions

  defp order_status_class(status) do
    case status do
      :initialized -> "bg-blue-100 text-blue-800"
      :submitted -> "bg-yellow-100 text-yellow-800"
      :accepted -> "bg-green-100 text-green-800"
      :partially_filled -> "bg-orange-100 text-orange-800"
      :filled -> "bg-green-100 text-green-800"
      :cancelled -> "bg-red-100 text-red-800"
      :rejected -> "bg-red-100 text-red-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end

  defp side_class(side) do
    case side do
      :buy -> "bg-green-100 text-green-800"
      :sell -> "bg-red-100 text-red-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end

  defp can_cancel_order?(order) do
    order.status in [:initialized, :submitted, :accepted, :partially_filled]
  end

  defp format_datetime(datetime) do
    Calendar.strftime(datetime, "%Y-%m-%d %H:%M:%S")
  end

  defp instrument_options(instruments) do
    Enum.map(instruments, fn instrument ->
      {instrument.name, "#{instrument.symbol}@#{instrument.venue}"}
    end)
  end

  defp change_order(attrs \\ %{}) do
    # This is a placeholder - we need to implement this in Trading context
    %Ecto.Changeset{
      data: %{},
      changes: attrs,
      errors: [],
      valid?: true
    }
  end
end
