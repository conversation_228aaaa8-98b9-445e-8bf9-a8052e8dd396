<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div class="flex items-center">
          <h1 class="text-3xl font-bold text-gray-900">Trading Dashboard</h1>
          <span class="ml-4 px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
            Live
          </span>
        </div>
        <div class="flex items-center space-x-4">
          <button 
            phx-click="refresh_data"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            Refresh Data
          </button>
          <div class="text-sm text-gray-500">
            Account: <%= @account.name %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Flash Messages -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
    <.flash :if={@flash[:info]} kind={:info} flash={@flash} />
    <.flash :if={@flash[:error]} kind={:error} flash={@flash} />
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      
      <!-- Left Column: Account Overview & Balances -->
      <div class="lg:col-span-1 space-y-6">
        
        <!-- Account Overview -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Overview</h3>
            <div class="grid grid-cols-1 gap-4">
              <div class="bg-gray-50 px-4 py-3 rounded-md">
                <div class="text-sm font-medium text-gray-500">Total Balance</div>
                <div class="text-2xl font-bold text-gray-900">
                  <%= format_currency(total_balance(@balances), @account.base_currency) %>
                </div>
              </div>
              <div class="bg-gray-50 px-4 py-3 rounded-md">
                <div class="text-sm font-medium text-gray-500">Available Balance</div>
                <div class="text-xl font-semibold text-green-600">
                  <%= format_currency(available_balance(@balances), @account.base_currency) %>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Balances by Currency -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Balances</h3>
            <div class="space-y-3">
              <%= for balance <- @balances do %>
                <div class="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-900"><%= balance.currency %></span>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">
                      <%= Decimal.to_string(balance.total, :normal) %>
                    </div>
                    <div class="text-xs text-gray-500">
                      Available: <%= Decimal.to_string(balance.available, :normal) %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Recent Notifications -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Notifications</h3>
            <div class="space-y-3">
              <%= if Enum.empty?(@notifications) do %>
                <p class="text-sm text-gray-500">No new notifications</p>
              <% else %>
                <%= for notification <- @notifications do %>
                  <div class={"border-l-4 p-3 rounded-md #{notification_priority_class(notification.priority)}"}>
                    <div class="flex justify-between items-start">
                      <div class="flex-1">
                        <h4 class="text-sm font-medium"><%= notification.title %></h4>
                        <p class="text-xs mt-1"><%= notification.message %></p>
                        <p class="text-xs text-gray-500 mt-1">
                          <%= Calendar.strftime(notification.inserted_at, "%H:%M") %>
                        </p>
                      </div>
                      <button 
                        phx-click="mark_notification_read" 
                        phx-value-notification_id={notification.id}
                        class="text-xs text-gray-400 hover:text-gray-600"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Active Orders -->
      <div class="lg:col-span-2">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg leading-6 font-medium text-gray-900">Active Orders</h3>
              <span class="text-sm text-gray-500">
                <%= length(@active_orders) %> active orders
              </span>
            </div>
            
            <%= if Enum.empty?(@active_orders) do %>
              <div class="text-center py-8">
                <div class="text-gray-400 text-lg mb-2">📋</div>
                <p class="text-sm text-gray-500">No active orders</p>
              </div>
            <% else %>
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Order ID
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Instrument
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Side
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <%= for order <- @active_orders do %>
                      <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          <%= String.slice(order.client_order_id, -8..-1) %>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <%= order.instrument_id %>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span class={"px-2 py-1 text-xs font-medium rounded-full #{if order.side == :buy, do: "bg-green-100 text-green-800", else: "bg-red-100 text-red-800"}"}>
                            <%= String.upcase(to_string(order.side)) %>
                          </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <%= String.upcase(to_string(order.order_type)) %>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <%= Decimal.to_string(order.quantity, :normal) %>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <%= if order.price, do: Decimal.to_string(order.price, :normal), else: "Market" %>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <span class={"px-2 py-1 text-xs font-medium rounded-full #{order_status_class(order.status)}"}>
                            <%= String.upcase(to_string(order.status)) %>
                          </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <%= if order.status in [:initialized, :submitted, :accepted] do %>
                            <button 
                              phx-click="cancel_order" 
                              phx-value-client_order_id={order.client_order_id}
                              class="text-red-600 hover:text-red-900"
                              data-confirm="Are you sure you want to cancel this order?"
                            >
                              Cancel
                            </button>
                          <% else %>
                            <span class="text-gray-400">-</span>
                          <% end %>
                        </td>
                      </tr>
                    <% end %>
                  </tbody>
                </table>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
