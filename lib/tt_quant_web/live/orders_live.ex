defmodule TtQuantWeb.OrdersLive do
  @moduledoc """
  订单管理实时界面
  """
  use TtQuantWeb, :live_view

  alias TtQuant.Trading.Orders
  alias TtQuantWeb.OrdersLive.{OrderForm, OrderTable, OrderBook}
  alias Phoenix.PubSub

  @impl true
  def mount(_params, %{"user_id" => user_id} = _session, socket) do
    if connected?(socket) do
      Orders.subscribe_to_orders(user_id)
      :timer.send_interval(1000, self(), :refresh_orderbook)
    end

    {:ok,
     socket
     |> assign(:user_id, user_id)
     |> assign(:page_title, "订单管理")
     |> assign(:orders, [])
     |> assign(:active_orders, [])
     |> assign(:orderbook, %{})
     |> assign(:selected_instrument, "BTCUSD")
     |> assign(:show_order_form, false)
     |> assign(:editing_order, nil)
     |> load_orders()}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "订单列表")
    |> assign(:editing_order, nil)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "创建订单")
    |> assign(:show_order_form, true)
    |> assign(:editing_order, nil)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    order = Enum.find(socket.assigns.orders, &(&1.id == id))
    
    socket
    |> assign(:page_title, "编辑订单")
    |> assign(:show_order_form, true)
    |> assign(:editing_order, order)
  end

  @impl true
  def handle_event("create_order", %{"order" => order_params}, socket) do
    order_params = Map.put(order_params, "user_id", socket.assigns.user_id)
    
    case Orders.create_order(order_params) do
      {:ok, order} ->
        {:noreply,
         socket
         |> put_flash(:info, "订单创建成功: #{order.client_order_id}")
         |> assign(:show_order_form, false)
         |> load_orders()}
         
      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply,
         socket
         |> put_flash(:error, "创建订单失败")
         |> assign(:changeset, changeset)}
         
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "创建订单失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("cancel_order", %{"id" => id}, socket) do
    case Orders.cancel_order(id) do
      {:ok, _order} ->
        {:noreply,
         socket
         |> put_flash(:info, "订单已取消")
         |> load_orders()}
         
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "取消订单失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("update_order", %{"id" => id, "order" => order_params}, socket) do
    case Orders.update_order(id, order_params) do
      {:ok, _order} ->
        {:noreply,
         socket
         |> put_flash(:info, "订单已更新")
         |> assign(:editing_order, nil)
         |> assign(:show_order_form, false)
         |> load_orders()}
         
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "更新订单失败: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("select_instrument", %{"instrument" => instrument}, socket) do
    {:noreply,
     socket
     |> assign(:selected_instrument, instrument)
     |> load_orderbook()}
  end

  @impl true
  def handle_event("toggle_order_form", _params, socket) do
    {:noreply, assign(socket, :show_order_form, !socket.assigns.show_order_form)}
  end

  @impl true
  def handle_event("close_order_form", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_order_form, false)
     |> assign(:editing_order, nil)}
  end

  @impl true
  def handle_info(:refresh_orderbook, socket) do
    {:noreply, load_orderbook(socket)}
  end

  @impl true
  def handle_info(%{event: :order_created, order: order}, socket) do
    {:noreply,
     socket
     |> update(:orders, fn orders -> [order | orders] end)
     |> update_active_orders()}
  end

  @impl true
  def handle_info(%{event: :order_updated, order: order}, socket) do
    {:noreply,
     socket
     |> update(:orders, fn orders ->
       Enum.map(orders, fn o ->
         if o.id == order.id, do: order, else: o
       end)
     end)
     |> update_active_orders()}
  end

  @impl true
  def handle_info(%{event: :order_cancelled, order: order}, socket) do
    {:noreply,
     socket
     |> update(:orders, fn orders ->
       Enum.map(orders, fn o ->
         if o.id == order.id, do: order, else: o
       end)
     end)
     |> update_active_orders()}
  end

  @impl true
  def handle_info(%{event: :order_filled, order: order, extra: fill_data}, socket) do
    {:noreply,
     socket
     |> update(:orders, fn orders ->
       Enum.map(orders, fn o ->
         if o.id == order.id, do: order, else: o
       end)
     end)
     |> update_active_orders()
     |> put_flash(:info, "订单成交: #{order.client_order_id} - #{fill_data.quantity} @ #{fill_data.price}")}
  end

  defp load_orders(socket) do
    orders = Orders.list_orders(socket.assigns.user_id)
    active_orders = Orders.list_active_orders(socket.assigns.user_id)
    
    socket
    |> assign(:orders, orders)
    |> assign(:active_orders, active_orders)
  end

  defp update_active_orders(socket) do
    active_orders = Enum.filter(socket.assigns.orders, & &1.status in [:accepted, :partially_filled])
    assign(socket, :active_orders, active_orders)
  end

  defp load_orderbook(socket) do
    case Orders.get_orderbook_snapshot(socket.assigns.selected_instrument, 10) do
      {:ok, snapshot} ->
        assign(socket, :orderbook, snapshot)
      {:error, _reason} ->
        socket
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="orders-container">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold">订单管理</h1>
        <div class="flex gap-4">
          <select phx-change="select_instrument" class="px-4 py-2 border rounded">
            <option value="BTCUSD" selected={@selected_instrument == "BTCUSD"}>BTC/USD</option>
            <option value="ETHUSD" selected={@selected_instrument == "ETHUSD"}>ETH/USD</option>
            <option value="SOLUSD" selected={@selected_instrument == "SOLUSD"}>SOL/USD</option>
          </select>
          <button 
            phx-click="toggle_order_form"
            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            <%= if @show_order_form, do: "关闭表单", else: "创建订单" %>
          </button>
        </div>
      </div>

      <%= if @show_order_form do %>
        <div class="mb-6 p-4 border rounded bg-gray-50">
          <.live_component
            module={OrderForm}
            id="order-form"
            order={@editing_order}
            user_id={@user_id}
            instrument={@selected_instrument}
          />
        </div>
      <% end %>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 订单簿 -->
        <div class="lg:col-span-1">
          <.live_component
            module={OrderBook}
            id="orderbook"
            orderbook={@orderbook}
            instrument={@selected_instrument}
          />
        </div>

        <!-- 活跃订单 -->
        <div class="lg:col-span-2">
          <div class="mb-6">
            <h2 class="text-xl font-semibold mb-4">活跃订单</h2>
            <.live_component
              module={OrderTable}
              id="active-orders"
              orders={@active_orders}
              show_actions={true}
            />
          </div>

          <!-- 历史订单 -->
          <div>
            <h2 class="text-xl font-semibold mb-4">历史订单</h2>
            <.live_component
              module={OrderTable}
              id="history-orders"
              orders={@orders}
              show_actions={false}
            />
          </div>
        </div>
      </div>
    </div>
    """
  end
end
