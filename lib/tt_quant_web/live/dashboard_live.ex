defmodule TtQuantWeb.DashboardLive do
  @moduledoc """
  Main dashboard LiveView for real-time trading interface.
  
  Provides an overview of account status, active orders, positions,
  and real-time market data updates.
  """

  use TtQuantWeb, :live_view
  
  alias TtQuant.{Portfolio, Trading, MarketData, Notifications}
  alias TtQuant.Events.EventDispatcher

  @impl true
  def mount(%{"account_id" => account_id}, _session, socket) do
    if connected?(socket) do
      # Subscribe to real-time events
      EventDispatcher.subscribe_to_orders(account_id)
      EventDispatcher.subscribe_to_portfolio(account_id)
      EventDispatcher.subscribe_to_notifications(account_id)
    end

    # Load initial data
    account = Portfolio.get_account(account_id)
    balances = Portfolio.get_balances(account_id)
    active_orders = Trading.list_active_orders(account_id)
    recent_notifications = Notifications.list_unread_notifications(account_id, %{limit: 5})

    socket = 
      socket
      |> assign(:account, account)
      |> assign(:account_id, account_id)
      |> assign(:balances, balances)
      |> assign(:active_orders, active_orders)
      |> assign(:notifications, recent_notifications)
      |> assign(:page_title, "Trading Dashboard")
      |> assign(:loading, false)

    {:ok, socket}
  end

  @impl true
  def handle_params(_params, _url, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("refresh_data", _params, socket) do
    account_id = socket.assigns.account_id
    
    # Reload all data
    balances = Portfolio.get_balances(account_id)
    active_orders = Trading.list_active_orders(account_id)
    notifications = Notifications.list_unread_notifications(account_id, %{limit: 5})

    socket = 
      socket
      |> assign(:balances, balances)
      |> assign(:active_orders, active_orders)
      |> assign(:notifications, notifications)
      |> put_flash(:info, "Data refreshed")

    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_order", %{"client_order_id" => client_order_id}, socket) do
    account_id = socket.assigns.account_id
    
    case Trading.cancel_order(account_id, client_order_id) do
      {:ok, _order} ->
        socket = put_flash(socket, :info, "Order cancelled successfully")
        {:noreply, socket}
      
      {:error, reason} ->
        socket = put_flash(socket, :error, "Failed to cancel order: #{inspect(reason)}")
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("mark_notification_read", %{"notification_id" => notification_id}, socket) do
    case Notifications.mark_as_read(notification_id) do
      {:ok, _notification} ->
        # Remove from unread list
        updated_notifications = Enum.reject(socket.assigns.notifications, &(&1.id == notification_id))
        socket = assign(socket, :notifications, updated_notifications)
        {:noreply, socket}
      
      {:error, _reason} ->
        socket = put_flash(socket, :error, "Failed to mark notification as read")
        {:noreply, socket}
    end
  end

  # Handle real-time events

  @impl true
  def handle_info(%{event_type: :order_created, data: data}, socket) do
    # Add new order to active orders list
    if order_id = Map.get(data, :order_id) do
      case Trading.get_order(socket.assigns.account_id, Map.get(data, :client_order_id)) do
        nil -> {:noreply, socket}
        order ->
          updated_orders = [order | socket.assigns.active_orders]
          socket = assign(socket, :active_orders, updated_orders)
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info(%{event_type: :order_filled, data: data}, socket) do
    # Update order in active orders list
    client_order_id = Map.get(data, :client_order_id)
    
    updated_orders = Enum.map(socket.assigns.active_orders, fn order ->
      if order.client_order_id == client_order_id do
        # Reload order from database to get latest status
        Trading.get_order(socket.assigns.account_id, client_order_id) || order
      else
        order
      end
    end)
    
    socket = 
      socket
      |> assign(:active_orders, updated_orders)
      |> put_flash(:info, "Order filled: #{Map.get(data, :filled_quantity)} @ #{Map.get(data, :fill_price)}")

    {:noreply, socket}
  end

  @impl true
  def handle_info(%{event_type: :balance_updated, data: _data}, socket) do
    # Reload balances
    balances = Portfolio.get_balances(socket.assigns.account_id)
    socket = assign(socket, :balances, balances)
    {:noreply, socket}
  end

  @impl true
  def handle_info(%{event_type: :new_notification, notification: notification}, socket) do
    # Add new notification to the list
    updated_notifications = [notification | socket.assigns.notifications]
    |> Enum.take(5)  # Keep only 5 most recent
    
    socket = 
      socket
      |> assign(:notifications, updated_notifications)
      |> put_flash(:info, "New notification: #{notification.title}")

    {:noreply, socket}
  end

  @impl true
  def handle_info(message, socket) do
    # Handle other messages
    {:noreply, socket}
  end

  # Helper functions

  defp format_currency(amount, currency) do
    case currency do
      "USD" -> "$#{Decimal.to_string(amount, :normal)}"
      "EUR" -> "€#{Decimal.to_string(amount, :normal)}"
      "GBP" -> "£#{Decimal.to_string(amount, :normal)}"
      "JPY" -> "¥#{Decimal.to_string(amount, :normal)}"
      _ -> "#{Decimal.to_string(amount, :normal)} #{currency}"
    end
  end

  defp format_percentage(value) do
    "#{Decimal.to_string(Decimal.mult(value, Decimal.new("100")), :normal)}%"
  end

  defp order_status_class(status) do
    case status do
      :initialized -> "bg-blue-100 text-blue-800"
      :submitted -> "bg-yellow-100 text-yellow-800"
      :accepted -> "bg-green-100 text-green-800"
      :partially_filled -> "bg-orange-100 text-orange-800"
      :filled -> "bg-green-100 text-green-800"
      :cancelled -> "bg-red-100 text-red-800"
      :rejected -> "bg-red-100 text-red-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end

  defp notification_priority_class(priority) do
    case priority do
      "urgent" -> "bg-red-100 border-red-500 text-red-800"
      "high" -> "bg-orange-100 border-orange-500 text-orange-800"
      "normal" -> "bg-blue-100 border-blue-500 text-blue-800"
      "low" -> "bg-gray-100 border-gray-500 text-gray-800"
      _ -> "bg-gray-100 border-gray-500 text-gray-800"
    end
  end

  defp total_balance(balances) do
    Enum.reduce(balances, Decimal.new("0"), fn balance, acc ->
      Decimal.add(acc, balance.total)
    end)
  end

  defp available_balance(balances) do
    Enum.reduce(balances, Decimal.new("0"), fn balance, acc ->
      Decimal.add(acc, balance.available)
    end)
  end
end
