defmodule TtQuantWeb.OrdersLive.Index do
  use TtQuantWeb, :live_view

  alias TtQuant.Orders
  alias TtQuant.Orders.Order
  alias Phoenix.PubSub

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # Subscribe to order updates
      PubSub.subscribe(TtQuant.PubSub, "orders:all")
    end

    orders = Orders.list_orders(order_by: [desc: :inserted_at])
    
    {:ok,
     socket
     |> assign(:page_title, "Orders")
     |> assign(:orders, orders)
     |> assign(:selected_order, nil)
     |> assign(:filter_status, "all")
     |> assign(:filter_side, "all")
     |> assign(:show_new_order_form, false)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Orders")
    |> assign(:selected_order, nil)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    order = Orders.get_order!(id)
    socket
    |> assign(:page_title, "Order Details")
    |> assign(:selected_order, order)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Order")
    |> assign(:show_new_order_form, true)
  end

  @impl true
  def handle_event("filter_status", %{"status" => status}, socket) do
    orders = fetch_filtered_orders(status, socket.assigns.filter_side)
    {:noreply, assign(socket, filter_status: status, orders: orders)}
  end

  @impl true
  def handle_event("filter_side", %{"side" => side}, socket) do
    orders = fetch_filtered_orders(socket.assigns.filter_status, side)
    {:noreply, assign(socket, filter_side: side, orders: orders)}
  end

  @impl true
  def handle_event("cancel_order", %{"id" => id}, socket) do
    order = Orders.get_order!(id)
    
    case Orders.cancel_order(order) do
      {:ok, cancelled_order} ->
        orders = update_order_in_list(socket.assigns.orders, cancelled_order)
        {:noreply,
         socket
         |> put_flash(:info, "Order cancelled successfully")
         |> assign(:orders, orders)}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to cancel: #{reason}")}
    end
  end

  @impl true
  def handle_event("create_order", %{"order" => order_params}, socket) do
    case Orders.create_order(order_params) do
      {:ok, order} ->
        orders = [order | socket.assigns.orders]
        {:noreply,
         socket
         |> put_flash(:info, "Order created successfully")
         |> assign(:orders, orders)
         |> assign(:show_new_order_form, false)}
      
      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, put_flash(socket, :error, format_errors(changeset))}
    end
  end

  @impl true
  def handle_event("close_new_order_form", _params, socket) do
    {:noreply, assign(socket, :show_new_order_form, false)}
  end

  @impl true
  def handle_event("refresh", _params, socket) do
    orders = fetch_filtered_orders(socket.assigns.filter_status, socket.assigns.filter_side)
    {:noreply, assign(socket, :orders, orders)}
  end

  # Handle PubSub events
  @impl true
  def handle_info({:created, order, _fill}, socket) do
    orders = [order | socket.assigns.orders]
    {:noreply, assign(socket, :orders, orders)}
  end

  def handle_info({:updated, order, _fill}, socket) do
    orders = update_order_in_list(socket.assigns.orders, order)
    selected = if socket.assigns.selected_order && socket.assigns.selected_order.id == order.id do
      order
    else
      socket.assigns.selected_order
    end
    
    {:noreply, assign(socket, orders: orders, selected_order: selected)}
  end

  def handle_info({:fill, order, fill}, socket) do
    orders = update_order_in_list(socket.assigns.orders, order)
    selected = if socket.assigns.selected_order && socket.assigns.selected_order.id == order.id do
      order
    else
      socket.assigns.selected_order
    end
    
    {:noreply, 
     socket
     |> put_flash(:info, "Order filled: #{fill.quantity} @ #{fill.price}")
     |> assign(orders: orders, selected_order: selected)}
  end

  def handle_info({:cancelled, order, _fill}, socket) do
    orders = update_order_in_list(socket.assigns.orders, order)
    {:noreply, assign(socket, :orders, orders)}
  end

  def handle_info({:rejected, order, _fill}, socket) do
    orders = update_order_in_list(socket.assigns.orders, order)
    {:noreply, assign(socket, :orders, orders)}
  end

  # Private functions

  defp fetch_filtered_orders(status, side) do
    filters = []
    filters = if status != "all", do: [{:status, status} | filters], else: filters
    filters = if side != "all", do: [{:side, side} | filters], else: filters
    filters = [{:order_by, [desc: :inserted_at]} | filters]
    
    Orders.list_orders(filters)
  end

  defp update_order_in_list(orders, updated_order) do
    Enum.map(orders, fn order ->
      if order.id == updated_order.id, do: updated_order, else: order
    end)
  end

  defp format_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Regex.replace(~r"%{(\w+)}", msg, fn _, key ->
        opts |> Keyword.get(String.to_existing_atom(key), key) |> to_string()
      end)
    end)
    |> Enum.map(fn {field, errors} -> "#{field}: #{Enum.join(errors, ", ")}" end)
    |> Enum.join("; ")
  end

  defp status_badge(status) do
    color = case status do
      "initialized" -> "gray"
      "submitted" -> "blue"
      "accepted" -> "indigo"
      "partially_filled" -> "yellow"
      "filled" -> "green"
      "cancelled" -> "gray"
      "expired" -> "gray"
      "rejected" -> "red"
      "pending_update" -> "yellow"
      "pending_cancel" -> "orange"
      _ -> "gray"
    end
    
    {String.upcase(status), color}
  end

  defp side_badge(side) do
    color = if side == "buy", do: "green", else: "red"
    {String.upcase(side), color}
  end

  def status_badge_class(status) do
    case status do
      "initialized" -> "bg-gray-100 text-gray-800"
      "submitted" -> "bg-blue-100 text-blue-800"
      "accepted" -> "bg-indigo-100 text-indigo-800"
      "partially_filled" -> "bg-yellow-100 text-yellow-800"
      "filled" -> "bg-green-100 text-green-800"
      "cancelled" -> "bg-gray-100 text-gray-800"
      "expired" -> "bg-gray-100 text-gray-800"
      "rejected" -> "bg-red-100 text-red-800"
      "pending_update" -> "bg-yellow-100 text-yellow-800"
      "pending_cancel" -> "bg-orange-100 text-orange-800"
      _ -> "bg-gray-100 text-gray-800"
    end
  end
end
