defmodule TtQuantWeb.Api.OrderController do
  use TtQuantWeb, :controller

  alias TtQuant.Orders
  alias TtQuant.Orders.Order

  action_fallback TtQuantWeb.FallbackController

  @doc """
  List all orders with optional filters
  """
  def index(conn, params) do
    filters = build_filters(params)
    orders = Orders.list_orders(filters)
    
    render(conn, "index.json", orders: orders)
  end

  @doc """
  Get a single order by ID
  """
  def show(conn, %{"id" => id}) do
    order = Orders.get_order!(id)
    render(conn, "show.json", order: order)
  end

  @doc """
  Create a new order
  """
  def create(conn, %{"order" => order_params}) do
    with {:ok, %Order{} = order} <- Orders.create_order(order_params) do
      conn
      |> put_status(:created)
      |> put_resp_header("location", Routes.api_order_path(conn, :show, order))
      |> render("show.json", order: order)
    end
  end

  @doc """
  Update an order
  """
  def update(conn, %{"id" => id, "order" => order_params}) do
    order = Orders.get_order!(id)

    with {:ok, %Order{} = order} <- Orders.update_order(order, order_params) do
      render(conn, "show.json", order: order)
    end
  end

  @doc """
  Cancel an order
  """
  def cancel(conn, %{"id" => id} = params) do
    order = Orders.get_order!(id)
    reason = params["reason"]

    with {:ok, %Order{} = order} <- Orders.cancel_order(order, reason) do
      render(conn, "show.json", order: order)
    end
  end

  @doc """
  Get active orders
  """
  def active(conn, params) do
    filters = build_filters(params)
    orders = Orders.get_active_orders(filters)
    
    render(conn, "index.json", orders: orders)
  end

  @doc """
  Get order book snapshot
  """
  def orderbook(conn, %{"instrument_id" => instrument_id} = params) do
    levels = String.to_integer(params["levels"] || "10")
    
    with {:ok, snapshot} <- Orders.get_orderbook_snapshot(instrument_id, levels) do
      render(conn, "orderbook.json", snapshot: snapshot)
    end
  end

  @doc """
  Sync orders with exchange
  """
  def sync(conn, %{"exchange" => exchange}) do
    case Orders.sync_orders_with_exchange(exchange) do
      {:ok, _} ->
        conn
        |> put_status(:ok)
        |> json(%{status: "success", message: "Orders synced successfully"})
      
      {:error, reason} ->
        conn
        |> put_status(:service_unavailable)
        |> json(%{status: "error", message: reason})
    end
  end

  # Private functions

  defp build_filters(params) do
    []
    |> add_filter(:status, params["status"])
    |> add_filter(:instrument_id, params["instrument_id"])
    |> add_filter(:exchange, params["exchange"])
    |> add_filter(:side, params["side"])
    |> add_filter(:from_date, parse_date(params["from_date"]))
    |> add_filter(:to_date, parse_date(params["to_date"]))
    |> add_filter(:tags, parse_tags(params["tags"]))
    |> add_filter(:limit, parse_integer(params["limit"]))
    |> add_filter(:order_by, parse_order_by(params["order_by"]))
  end

  defp add_filter(filters, _key, nil), do: filters
  defp add_filter(filters, key, value), do: [{key, value} | filters]

  defp parse_date(nil), do: nil
  defp parse_date(date_string) do
    case DateTime.from_iso8601(date_string) do
      {:ok, date, _} -> date
      _ -> nil
    end
  end

  defp parse_tags(nil), do: nil
  defp parse_tags(tags) when is_list(tags), do: tags
  defp parse_tags(tags) when is_binary(tags), do: String.split(tags, ",")

  defp parse_integer(nil), do: nil
  defp parse_integer(string) when is_binary(string), do: String.to_integer(string)
  defp parse_integer(int) when is_integer(int), do: int

  defp parse_order_by(nil), do: nil
  defp parse_order_by("created_asc"), do: [asc: :inserted_at]
  defp parse_order_by("created_desc"), do: [desc: :inserted_at]
  defp parse_order_by("updated_asc"), do: [asc: :updated_at]
  defp parse_order_by("updated_desc"), do: [desc: :updated_at]
  defp parse_order_by(_), do: nil
end
