defmodule TtQuantWeb.NotificationChannel do
  @moduledoc """
  Channel for real-time notifications.
  
  Handles subscription to notification events and provides real-time
  updates for new notifications, alerts, and system messages.
  """

  use TtQuantWeb, :channel
  
  alias TtQuant.Events.EventDispatcher
  alias TtQuant.Notifications
  require Logger

  @impl true
  def join("notifications:" <> account_id, _payload, socket) do
    # Verify user can access this account's notifications
    if authorized?(socket, account_id) do
      # Subscribe to notification events for this account
      EventDispatcher.subscribe_to_notifications(account_id)
      
      # Send current unread notifications
      unread_notifications = Notifications.list_unread_notifications(account_id, %{limit: 50})
      
      {:ok, %{unread_notifications: unread_notifications}, assign(socket, :account_id, account_id)}
    else
      {:error, %{reason: "unauthorized"}}
    end
  end

  @impl true
  def handle_in("get_notifications", %{"limit" => limit}, socket) do
    account_id = socket.assigns.account_id
    notifications = Notifications.list_notifications(account_id, %{limit: limit})
    
    {:reply, {:ok, %{notifications: notifications}}, socket}
  end

  @impl true
  def handle_in("get_unread_notifications", _payload, socket) do
    account_id = socket.assigns.account_id
    notifications = Notifications.list_unread_notifications(account_id)
    
    {:reply, {:ok, %{notifications: notifications}}, socket}
  end

  @impl true
  def handle_in("mark_as_read", %{"notification_id" => notification_id}, socket) do
    case Notifications.mark_as_read(notification_id) do
      {:ok, notification} ->
        {:reply, {:ok, %{notification: notification}}, socket}
      
      {:error, reason} ->
        {:reply, {:error, %{reason: reason}}, socket}
    end
  end

  @impl true
  def handle_in("mark_all_as_read", _payload, socket) do
    account_id = socket.assigns.account_id
    
    case Notifications.mark_all_as_read(account_id) do
      {count, _} ->
        {:reply, {:ok, %{marked_count: count}}, socket}
    end
  end

  @impl true
  def handle_in("get_notification_stats", _payload, socket) do
    account_id = socket.assigns.account_id
    stats = Notifications.get_notification_stats(account_id)
    
    {:reply, {:ok, %{stats: stats}}, socket}
  end

  @impl true
  def handle_info(%{event_type: :new_notification, notification: notification}, socket) do
    # Forward new notification to the client
    push(socket, "new_notification", %{
      notification: notification,
      timestamp: DateTime.utc_now()
    })
    
    {:noreply, socket}
  end

  @impl true
  def handle_info(%{event_type: event_type, data: data} = message, socket) do
    # Forward other notification events to the client
    push(socket, "notification_event", %{
      event_type: event_type,
      data: data,
      timestamp: message.timestamp
    })
    
    {:noreply, socket}
  end

  @impl true
  def handle_info(message, socket) do
    Logger.debug("NotificationChannel received unknown message: #{inspect(message)}")
    {:noreply, socket}
  end

  defp authorized?(socket, account_id) do
    socket.assigns.account_id == account_id
  end
end
