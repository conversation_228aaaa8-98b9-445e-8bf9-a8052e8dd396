defmodule TtQuantWeb.UserSocket do
  @moduledoc """
  WebSocket handler for real-time communication.
  
  Handles user authentication and channel authorization for
  real-time data streams including orders, trades, and market data.
  """

  use Phoenix.Socket

  alias TtQuant.Accounts

  # Channels
  channel "orders:*", TtQuantWeb.OrderChannel
  channel "trades:*", TtQuantWeb.TradeChannel
  channel "portfolio:*", TtQuantWeb.PortfolioChannel
  channel "market_data:*", TtQuantWeb.MarketDataChannel
  channel "notifications:*", TtQuantWeb.NotificationChannel

  @impl true
  def connect(%{"token" => token}, socket, _connect_info) do
    case Accounts.validate_session_token(token) do
      {:ok, account} ->
        socket = assign(socket, :account_id, account.id)
        {:ok, socket}
      
      {:error, :invalid_session} ->
        :error
    end
  end

  def connect(_params, _socket, _connect_info) do
    :error
  end

  @impl true
  def id(socket), do: "user_socket:#{socket.assigns.account_id}"
end
