defmodule TtQuantWeb.OrderChannel do
  @moduledoc """
  Channel for real-time order updates.
  
  Handles subscription to order events and provides real-time
  updates for order status changes, fills, and cancellations.
  """

  use TtQuantWeb, :channel
  
  alias TtQuant.Events.EventDispatcher
  alias TtQuant.Trading
  require Logger

  @impl true
  def join("orders:" <> account_id, _payload, socket) do
    # Verify user can access this account's orders
    if authorized?(socket, account_id) do
      # Subscribe to order events for this account
      EventDispatcher.subscribe_to_orders(account_id)
      
      # Send current active orders
      active_orders = Trading.list_active_orders(account_id)
      
      {:ok, %{active_orders: active_orders}, assign(socket, :account_id, account_id)}
    else
      {:error, %{reason: "unauthorized"}}
    end
  end

  @impl true
  def handle_in("get_orders", _payload, socket) do
    account_id = socket.assigns.account_id
    orders = Trading.list_active_orders(account_id)
    
    {:reply, {:ok, %{orders: orders}}, socket}
  end

  @impl true
  def handle_in("get_order_history", %{"limit" => limit}, socket) do
    account_id = socket.assigns.account_id
    history = Trading.list_order_history(account_id, %{limit: limit})
    
    {:reply, {:ok, %{history: history}}, socket}
  end

  @impl true
  def handle_in("cancel_order", %{"client_order_id" => client_order_id}, socket) do
    account_id = socket.assigns.account_id
    
    case Trading.cancel_order(account_id, client_order_id) do
      {:ok, order} ->
        {:reply, {:ok, %{order: order}}, socket}
      
      {:error, reason} ->
        {:reply, {:error, %{reason: reason}}, socket}
    end
  end

  @impl true
  def handle_info(%{event_type: event_type, data: data} = message, socket) do
    # Forward order events to the client
    push(socket, "order_event", %{
      event_type: event_type,
      data: data,
      timestamp: message.timestamp
    })
    
    {:noreply, socket}
  end

  @impl true
  def handle_info(message, socket) do
    Logger.debug("OrderChannel received unknown message: #{inspect(message)}")
    {:noreply, socket}
  end

  defp authorized?(socket, account_id) do
    socket.assigns.account_id == account_id
  end
end
