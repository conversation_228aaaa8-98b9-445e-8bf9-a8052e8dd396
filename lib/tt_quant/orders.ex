defmodule TtQuant.Orders do
  @moduledoc """
  The Orders context - handles order management business logic
  """

  import Ecto.Query, warn: false
  alias TtQuant.Repo
  alias TtQuant.Orders.{Order, OrderFill}
  alias TtQuant.Native
  require Logger

  @doc """
  Returns the list of orders with optional filters
  """
  def list_orders(opts \\ []) do
    query = from(o in Order)

    query
    |> apply_filters(opts)
    |> Repo.all()
    |> Repo.preload(:fills)
  end

  @doc """
  Gets a single order.
  """
  def get_order!(id) do
    Repo.get!(Order, id)
    |> Repo.preload(:fills)
  end

  @doc """
  Gets an order by client_order_id
  """
  def get_order_by_client_id(client_order_id) do
    Repo.get_by(Order, client_order_id: client_order_id)
    |> Repo.preload(:fills)
  end

  @doc """
  Creates an order and submits it to the exchange
  """
  def create_order(attrs \\ %{}) do
    # Generate client_order_id if not provided
    attrs = Map.put_new(attrs, "client_order_id", generate_client_order_id())
    attrs = Map.put_new(attrs, "status", "initialized")
    attrs = Map.put(attrs, "remaining_quantity", attrs["quantity"])

    changeset = %Order{} |> Order.changeset(attrs)

    case Repo.insert(changeset) do
      {:ok, order} ->
        # Send to Rust for processing
        case submit_to_rust(order) do
          {:ok, _} ->
            # Update status to submitted
            update_order_status(order, "submitted")
            broadcast_order_event(order, :created)
            {:ok, order}
          {:error, reason} ->
            # Mark as rejected and update database
            {:ok, rejected} = update_order_status(order, "rejected")
            broadcast_order_event(rejected, :rejected)
            {:error, reason}
        end

      {:error, changeset} ->
        {:error, changeset}
    end
  end

  @doc """
  Updates an order
  """
  def update_order(%Order{} = order, attrs) do
    # Validate state transition if status is changing
    if new_status = attrs["status"] do
      unless Order.valid_transition?(order.status, new_status) do
        {:error, "Invalid state transition from #{order.status} to #{new_status}"}
      else
        do_update_order(order, attrs)
      end
    else
      do_update_order(order, attrs)
    end
  end

  defp do_update_order(order, attrs) do

    changeset = order |> Order.changeset(attrs)

    case Repo.update(changeset) do
      {:ok, updated} ->
        broadcast_order_event(updated, :updated)
        {:ok, updated}
      error ->
        error
    end
  end

  @doc """
  Updates order status with validation
  """
  def update_order_status(%Order{} = order, new_status) do
    if Order.valid_transition?(order.status, new_status) do
      update_order(order, %{"status" => new_status})
    else
      {:error, "Invalid state transition"}
    end
  end

  @doc """
  Cancels an order
  """
  def cancel_order(%Order{} = order, reason \\ nil) do
    if Order.active?(order) do
      # Send cancel request to Rust
      case cancel_in_rust(order) do
        {:ok, _} ->
          attrs = %{
            "status" => "cancelled",
            "metadata" => Map.put(order.metadata || %{}, "cancel_reason", reason || "User requested")
          }
          update_order(order, attrs)

        {:error, reason} ->
          {:error, reason}
      end
    else
      {:error, "Cannot cancel order in #{order.status} status"}
    end
  end

  @doc """
  Process a fill for an order
  """
  def process_fill(%Order{} = order, fill_attrs) do
    Repo.transaction(fn ->
      # Create fill record
      fill_changeset = %OrderFill{}
        |> OrderFill.changeset(Map.put(fill_attrs, "order_id", order.id))

      case Repo.insert(fill_changeset) do
        {:ok, fill} ->
          # Update order with fill
          updated = Order.apply_fill(
            order,
            fill.quantity,
            fill.price
          )

          changeset = order |> Order.changeset(%{
            "filled_quantity" => updated.filled_quantity,
            "remaining_quantity" => updated.remaining_quantity,
            "avg_fill_price" => updated.avg_fill_price,
            "status" => updated.status
          })

          case Repo.update(changeset) do
            {:ok, updated_order} ->
              broadcast_order_event(updated_order, :fill, fill)
              {updated_order, fill}

            {:error, changeset} ->
              Repo.rollback(changeset)
          end

        {:error, changeset} ->
          Repo.rollback(changeset)
      end
    end)
  end

  @doc """
  Get active orders
  """
  def get_active_orders(opts \\ []) do
    statuses = ~w(submitted accepted partially_filled pending_update pending_cancel)

    opts = Keyword.put(opts, :status, statuses)
    list_orders(opts)
  end

  @doc """
  Get orders by instrument
  """
  def get_orders_by_instrument(instrument_id, opts \\ []) do
    opts = Keyword.put(opts, :instrument_id, instrument_id)
    list_orders(opts)
  end

  @doc """
  Get order book snapshot from Rust
  """
  def get_orderbook_snapshot(instrument_id, levels \\ 10) do
    case Native.get_orderbook_snapshot(instrument_id, levels) do
      {:ok, snapshot} ->
        {:ok, decode_snapshot(snapshot)}
      error ->
        error
    end
  end

  @doc """
  Sync orders with exchange
  """
  def sync_orders_with_exchange(exchange) do
    # Get orders from exchange via Rust
    case Native.get_exchange_orders(exchange) do
      {:ok, exchange_orders} ->
        # Sync with database
        Enum.map(exchange_orders, fn ext_order ->
          case get_order_by_client_id(ext_order["client_order_id"]) do
            nil ->
              # New order from exchange
              create_order(ext_order)

            existing ->
              # Update existing order
              update_order(existing, ext_order)
          end
        end)

      error ->
        Logger.error("Failed to sync orders: #{inspect(error)}")
        error
    end
  end

  # Private functions

  defp apply_filters(query, opts) do
    Enum.reduce(opts, query, fn
      {:status, status}, q when is_list(status) ->
        where(q, [o], o.status in ^status)

      {:status, status}, q ->
        where(q, [o], o.status == ^status)

      {:instrument_id, id}, q ->
        where(q, [o], o.instrument_id == ^id)

      {:exchange, exchange}, q ->
        where(q, [o], o.exchange == ^exchange)

      {:side, side}, q ->
        where(q, [o], o.side == ^side)

      {:from_date, date}, q ->
        where(q, [o], o.inserted_at >= ^date)

      {:to_date, date}, q ->
        where(q, [o], o.inserted_at <= ^date)

      {:tags, tags}, q when is_list(tags) ->
        where(q, [o], fragment("? && ?", o.tags, ^tags))

      {:order_by, order_by}, q ->
        order_by(q, ^order_by)

      {:limit, limit}, q ->
        limit(q, ^limit)

      _, q ->
        q
    end)
  end

  defp generate_client_order_id do
    "TT-#{DateTime.utc_now() |> DateTime.to_unix(:microsecond)}-#{:rand.uniform(9999)}"
  end

  defp submit_to_rust(%Order{} = order) do
    if Mix.env() == :test do
      # In test environment, skip Rust NIF calls
      {:ok, :test_mode}
    else
      # Convert order to Rust format and submit
      rust_order = %{
        client_order_id: order.client_order_id,
        instrument_id: order.instrument_id,
        side: String.to_atom(order.side),
        order_type: String.to_atom(order.order_type),
        quantity: order.quantity,
        price: order.price,
        stop_price: order.stop_price,
        time_in_force: order.time_in_force && String.to_atom(order.time_in_force)
      }

      Native.process_order(rust_order)
    end
  end

  defp cancel_in_rust(%Order{} = order) do
    if Mix.env() == :test do
      # In test environment, skip Rust NIF calls
      {:ok, :test_mode}
    else
      Native.cancel_order(
        order.client_order_id,
        order.instrument_id,
        String.to_atom(order.side)
      )
    end
  end

  defp decode_snapshot(snapshot) do
    %{
      instrument_id: snapshot.instrument_id,
      bids: Enum.map(snapshot.bid_levels, &decode_level/1),
      asks: Enum.map(snapshot.ask_levels, &decode_level/1),
      last_update: snapshot.last_update,
      sequence: snapshot.sequence
    }
  end

  defp decode_level({price, quantity, count}) do
    %{
      price: price,
      quantity: quantity,
      order_count: count
    }
  end

  defp broadcast_order_event(order, event, fill \\ nil) do
    Phoenix.PubSub.broadcast(
      TtQuant.PubSub,
      "orders:#{order.client_order_id}",
      {event, order, fill}
    )

    Phoenix.PubSub.broadcast(
      TtQuant.PubSub,
      "orders:all",
      {event, order, fill}
    )
  end
end
