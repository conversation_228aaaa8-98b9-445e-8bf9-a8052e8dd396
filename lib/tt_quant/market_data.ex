defmodule TtQuant.MarketData do
  @moduledoc """
  The MarketData context.

  This module provides the main API for market data management,
  including ticks, quotes, trades, and real-time data feeds.
  """

  alias TtQuant.MarketData.MarketData
  alias TtQuant.Repo
  import Ecto.Query

  # Market Data Management

  @doc """
  Creates market data entry.
  """
  @spec create_market_data(map()) :: {:ok, MarketData.t()} | {:error, Ecto.Changeset.t()}
  def create_market_data(attrs) do
    %MarketData{}
    |> MarketData.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates a tick data entry.
  """
  @spec create_tick(String.t(), Decimal.t(), Decimal.t(), String.t(), DateTime.t()) :: {:ok, MarketData.t()} | {:error, Ecto.Changeset.t()}
  def create_tick(instrument_id, price, quantity, venue, timestamp \\ nil) do
    MarketData.tick(instrument_id, price, quantity, venue, timestamp)
    |> Repo.insert()
  end

  @doc """
  Creates a quote data entry.
  """
  @spec create_quote(String.t(), Decimal.t(), Decimal.t(), Decimal.t(), Decimal.t(), String.t(), DateTime.t()) :: {:ok, MarketData.t()} | {:error, Ecto.Changeset.t()}
  def create_quote(instrument_id, bid_price, bid_qty, ask_price, ask_qty, venue, timestamp \\ nil) do
    MarketData.quote(instrument_id, bid_price, bid_qty, ask_price, ask_qty, venue, timestamp)
    |> Repo.insert()
  end

  @doc """
  Creates a trade data entry.
  """
  @spec create_trade(String.t(), Decimal.t(), Decimal.t(), Decimal.t(), String.t(), DateTime.t()) :: {:ok, MarketData.t()} | {:error, Ecto.Changeset.t()}
  def create_trade(instrument_id, price, quantity, volume, venue, timestamp \\ nil) do
    MarketData.trade(instrument_id, price, quantity, volume, venue, timestamp)
    |> Repo.insert()
  end

  @doc """
  Gets market data by ID.
  """
  @spec get_market_data(binary()) :: MarketData.t() | nil
  def get_market_data(id) do
    Repo.get(MarketData, id)
  end

  @doc """
  Gets the latest market data for an instrument.
  """
  @spec get_latest_market_data(String.t(), String.t()) :: MarketData.t() | nil
  def get_latest_market_data(instrument_id, data_type \\ "quote") do
    MarketData
    |> where([m], m.instrument_id == ^instrument_id and m.data_type == ^data_type)
    |> order_by([m], desc: m.timestamp)
    |> limit(1)
    |> Repo.one()
  end

  @doc """
  Gets the latest quote for an instrument.
  """
  @spec get_latest_quote(String.t()) :: MarketData.t() | nil
  def get_latest_quote(instrument_id) do
    get_latest_market_data(instrument_id, "quote")
  end

  @doc """
  Gets the latest tick for an instrument.
  """
  @spec get_latest_tick(String.t()) :: MarketData.t() | nil
  def get_latest_tick(instrument_id) do
    get_latest_market_data(instrument_id, "tick")
  end

  @doc """
  Lists market data for an instrument.
  """
  @spec list_market_data(String.t(), map()) :: [MarketData.t()]
  def list_market_data(instrument_id, opts \\ %{}) do
    query = MarketData
    |> where([m], m.instrument_id == ^instrument_id)
    |> order_by([m], desc: m.timestamp)

    query = apply_market_data_filters(query, opts)
    
    Repo.all(query)
  end

  @doc """
  Lists quotes for an instrument.
  """
  @spec list_quotes(String.t(), map()) :: [MarketData.t()]
  def list_quotes(instrument_id, opts \\ %{}) do
    opts = Map.put(opts, :data_type, "quote")
    list_market_data(instrument_id, opts)
  end

  @doc """
  Lists ticks for an instrument.
  """
  @spec list_ticks(String.t(), map()) :: [MarketData.t()]
  def list_ticks(instrument_id, opts \\ %{}) do
    opts = Map.put(opts, :data_type, "tick")
    list_market_data(instrument_id, opts)
  end

  @doc """
  Lists trades for an instrument.
  """
  @spec list_trades(String.t(), map()) :: [MarketData.t()]
  def list_trades(instrument_id, opts \\ %{}) do
    opts = Map.put(opts, :data_type, "trade")
    list_market_data(instrument_id, opts)
  end

  @doc """
  Gets market data statistics for an instrument.
  """
  @spec get_market_data_stats(String.t(), map()) :: map()
  def get_market_data_stats(instrument_id, opts \\ %{}) do
    from_date = Map.get(opts, :from_date, DateTime.add(DateTime.utc_now(), -1, :day))
    to_date = Map.get(opts, :to_date, DateTime.utc_now())

    query = MarketData
    |> where([m], m.instrument_id == ^instrument_id)
    |> where([m], m.timestamp >= ^from_date and m.timestamp <= ^to_date)

    # Get data counts by type
    data_counts = query
    |> group_by([m], m.data_type)
    |> select([m], {m.data_type, count(m.id)})
    |> Repo.all()
    |> Enum.into(%{})

    # Get venue counts
    venue_counts = query
    |> group_by([m], m.venue)
    |> select([m], {m.venue, count(m.id)})
    |> Repo.all()
    |> Enum.into(%{})

    # Get price statistics for ticks/trades
    price_stats = query
    |> where([m], not is_nil(m.last_price))
    |> select([m], %{
      min_price: min(m.last_price),
      max_price: max(m.last_price),
      avg_price: avg(m.last_price)
    })
    |> Repo.one()

    # Get volume statistics
    volume_stats = query
    |> where([m], not is_nil(m.volume))
    |> select([m], %{
      total_volume: sum(m.volume),
      avg_volume: avg(m.volume)
    })
    |> Repo.one()

    %{
      data_counts: data_counts,
      venue_counts: venue_counts,
      price_stats: price_stats || %{},
      volume_stats: volume_stats || %{},
      period_start: from_date,
      period_end: to_date
    }
  end

  @doc """
  Subscribes to real-time market data updates for an instrument.
  """
  @spec subscribe_to_market_data(String.t()) :: :ok
  def subscribe_to_market_data(instrument_id) do
    Phoenix.PubSub.subscribe(TtQuant.PubSub, "market_data:#{instrument_id}")
  end

  @doc """
  Unsubscribes from real-time market data updates for an instrument.
  """
  @spec unsubscribe_from_market_data(String.t()) :: :ok
  def unsubscribe_from_market_data(instrument_id) do
    Phoenix.PubSub.unsubscribe(TtQuant.PubSub, "market_data:#{instrument_id}")
  end

  @doc """
  Broadcasts market data update.
  """
  @spec broadcast_market_data_update(MarketData.t()) :: :ok
  def broadcast_market_data_update(%MarketData{} = market_data) do
    message = %{
      type: :market_data_update,
      data: market_data,
      timestamp: DateTime.utc_now()
    }

    Phoenix.PubSub.broadcast(TtQuant.PubSub, "market_data:#{market_data.instrument_id}", message)
  end

  @doc """
  Deletes old market data.
  """
  @spec delete_old_market_data(integer()) :: {integer(), nil}
  def delete_old_market_data(days_old \\ 30) do
    cutoff_date = DateTime.add(DateTime.utc_now(), -days_old, :day)
    
    from(m in MarketData, where: m.inserted_at < ^cutoff_date)
    |> Repo.delete_all()
  end

  # Helper functions

  defp apply_market_data_filters(query, opts) do
    query
    |> filter_by_data_type(opts)
    |> filter_by_venue(opts)
    |> filter_by_date_range(opts)
    |> filter_by_limit(opts)
  end

  defp filter_by_data_type(query, %{data_type: data_type}) do
    where(query, [m], m.data_type == ^data_type)
  end
  defp filter_by_data_type(query, _), do: query

  defp filter_by_venue(query, %{venue: venue}) do
    where(query, [m], m.venue == ^venue)
  end
  defp filter_by_venue(query, _), do: query

  defp filter_by_date_range(query, %{from_date: from_date, to_date: to_date}) do
    where(query, [m], m.timestamp >= ^from_date and m.timestamp <= ^to_date)
  end
  defp filter_by_date_range(query, %{from_date: from_date}) do
    where(query, [m], m.timestamp >= ^from_date)
  end
  defp filter_by_date_range(query, %{to_date: to_date}) do
    where(query, [m], m.timestamp <= ^to_date)
  end
  defp filter_by_date_range(query, _), do: query

  defp filter_by_limit(query, %{limit: limit}) do
    limit(query, ^limit)
  end
  defp filter_by_limit(query, _), do: query
end
