defmodule TtQuant.Events.EventListener do
  @moduledoc """
  Event listener for handling system-wide events and triggering appropriate actions.
  
  This GenServer listens to various event channels and performs actions like
  creating notifications, updating statistics, and triggering workflows.
  """

  use GenServer
  require Logger
  
  alias TtQuant.Events.EventDispatcher
  alias TtQuant.Notifications
  alias TtQuant.Events

  @doc """
  Starts the event listener.
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @impl true
  def init(_opts) do
    # Subscribe to various event channels
    EventDispatcher.subscribe_to_global_events()
    EventDispatcher.subscribe_to_system_events()
    
    Logger.info("Event listener started and subscribed to global events")
    
    {:ok, %{
      event_count: 0,
      last_event_time: DateTime.utc_now(),
      event_stats: %{}
    }}
  end

  @impl true
  def handle_info(%{event_type: event_type, data: data} = message, state) do
    # Update statistics
    new_stats = update_event_stats(state.event_stats, event_type)
    new_state = %{
      state | 
      event_count: state.event_count + 1,
      last_event_time: DateTime.utc_now(),
      event_stats: new_stats
    }

    # Handle specific event types
    handle_event(event_type, data, message)

    {:noreply, new_state}
  end

  @impl true
  def handle_info(message, state) do
    Logger.debug("Event listener received unknown message: #{inspect(message)}")
    {:noreply, state}
  end

  @impl true
  def handle_call(:get_stats, _from, state) do
    stats = %{
      total_events: state.event_count,
      last_event_time: state.last_event_time,
      event_type_counts: state.event_stats,
      uptime: DateTime.diff(DateTime.utc_now(), state.last_event_time, :second)
    }
    
    {:reply, stats, state}
  end

  @impl true
  def handle_cast({:reset_stats}, state) do
    new_state = %{
      state |
      event_count: 0,
      event_stats: %{}
    }
    
    {:noreply, new_state}
  end

  # Public API

  @doc """
  Gets event listener statistics.
  """
  @spec get_stats() :: map()
  def get_stats do
    GenServer.call(__MODULE__, :get_stats)
  end

  @doc """
  Resets event statistics.
  """
  @spec reset_stats() :: :ok
  def reset_stats do
    GenServer.cast(__MODULE__, {:reset_stats})
  end

  # Private event handlers

  defp handle_event(:order_rejected, data, message) do
    account_id = Map.get(message, :account_id)
    
    if account_id do
      # Create notification for order rejection
      title = "Order Rejected"
      message_text = "Order #{Map.get(data, :client_order_id, "unknown")} was rejected"
      
      Notifications.create_order_update_notification(account_id, title, message_text, data)
      
      # Create order event record
      if order_id = Map.get(data, :order_id) do
        Events.create_order_event(%{
          order_id: order_id,
          event_type: "order_rejected",
          event_data: data,
          timestamp: DateTime.utc_now(),
          source: "system"
        })
      end
    end
  end

  defp handle_event(:order_filled, data, message) do
    account_id = Map.get(message, :account_id)
    
    if account_id do
      # Create notification for order fill
      title = "Order Filled"
      quantity = Map.get(data, :filled_quantity, "unknown")
      price = Map.get(data, :fill_price, "unknown")
      message_text = "Order filled: #{quantity} @ #{price}"
      
      Notifications.create_trade_execution_notification(account_id, title, message_text, data)
      
      # Create order event record
      if order_id = Map.get(data, :order_id) do
        Events.create_order_event(%{
          order_id: order_id,
          event_type: "order_filled",
          event_data: data,
          timestamp: DateTime.utc_now(),
          source: "system"
        })
      end
    end
  end

  defp handle_event(:margin_call, data, message) do
    account_id = Map.get(message, :account_id)
    
    if account_id do
      # Create urgent margin call notification
      title = "Margin Call Alert"
      margin_ratio = Map.get(data, :margin_ratio, "unknown")
      message_text = "Margin call triggered. Current margin ratio: #{margin_ratio}"
      
      Notifications.create_margin_call_notification(account_id, title, message_text, data)
      
      Logger.warning("Margin call event processed for account #{account_id}")
    end
  end

  defp handle_event(:risk_alert, data, message) do
    account_id = Map.get(message, :account_id)
    
    if account_id do
      # Create risk alert notification
      title = "Risk Alert"
      alert_type = Map.get(data, :alert_type, "general")
      message_text = "Risk alert triggered: #{alert_type}"
      
      Notifications.create_risk_alert_notification(account_id, title, message_text, data)
      
      Logger.warning("Risk alert event processed for account #{account_id}")
    end
  end

  defp handle_event(:position_closed, data, message) do
    account_id = Map.get(message, :account_id)
    
    if account_id do
      # Create notification for position closure
      title = "Position Closed"
      instrument_id = Map.get(data, :instrument_id, "unknown")
      pnl = Map.get(data, :realized_pnl, "unknown")
      message_text = "Position closed for #{instrument_id}. P&L: #{pnl}"
      
      Notifications.create_order_update_notification(account_id, title, message_text, data)
    end
  end

  defp handle_event(:system_maintenance, data, _message) do
    # Broadcast system maintenance notification to all active accounts
    maintenance_type = Map.get(data, :maintenance_type, "general")
    scheduled_time = Map.get(data, :scheduled_time, DateTime.utc_now())
    
    Logger.info("System maintenance event: #{maintenance_type} scheduled for #{scheduled_time}")
    
    # This would typically query for all active accounts and send notifications
    # For now, we'll just log it
  end

  defp handle_event(event_type, data, message) do
    # Default handler for unspecified events
    Logger.debug("Event listener processed event: #{event_type} with data: #{inspect(data)}")
    
    # Log high-priority events
    if event_type in [:order_cancelled, :trade_executed, :balance_updated] do
      account_id = Map.get(message, :account_id, "system")
      Logger.info("Important event processed: #{event_type} for #{account_id}")
    end
  end

  defp update_event_stats(stats, event_type) do
    Map.update(stats, event_type, 1, &(&1 + 1))
  end
end
