defmodule TtQuant.Events.OrderEvent do
  @moduledoc """
  Schema for order lifecycle events.
  
  Tracks all events that occur during an order's lifecycle for
  audit trails, debugging, and analytics.
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Trading.Order

  @type t :: %__MODULE__{
          id: binary(),
          order_id: binary(),
          event_type: String.t(),
          event_data: map(),
          timestamp: DateTime.t(),
          source: String.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @event_types [
    "order_created",
    "order_submitted", 
    "order_accepted",
    "order_rejected",
    "order_cancelled",
    "order_expired",
    "order_triggered",
    "order_filled",
    "order_partially_filled",
    "order_updated",
    "order_replaced"
  ]

  @sources ["system", "user", "venue", "risk_engine", "matching_engine"]

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "order_events" do
    field :event_type, :string
    field :event_data, :map, default: %{}
    field :timestamp, :utc_datetime
    field :source, :string, default: "system"

    belongs_to :order, Order

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for order event creation.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(order_event, attrs) do
    order_event
    |> cast(attrs, [:order_id, :event_type, :event_data, :timestamp, :source])
    |> validate_required([:order_id, :event_type, :timestamp, :source])
    |> validate_inclusion(:event_type, @event_types)
    |> validate_inclusion(:source, @sources)
    |> foreign_key_constraint(:order_id)
    |> put_timestamp()
  end

  @doc """
  Creates an order event from order and event details.
  """
  @spec from_order(Order.t(), String.t(), map(), String.t()) :: Ecto.Changeset.t()
  def from_order(%Order{} = order, event_type, event_data \\ %{}, source \\ "system") do
    attrs = %{
      order_id: order.id,
      event_type: event_type,
      event_data: event_data,
      timestamp: DateTime.utc_now(),
      source: source
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Gets all valid event types.
  """
  @spec event_types() :: [String.t()]
  def event_types, do: @event_types

  @doc """
  Gets all valid sources.
  """
  @spec sources() :: [String.t()]
  def sources, do: @sources

  defp put_timestamp(changeset) do
    case get_field(changeset, :timestamp) do
      nil -> put_change(changeset, :timestamp, DateTime.utc_now())
      _ -> changeset
    end
  end
end
