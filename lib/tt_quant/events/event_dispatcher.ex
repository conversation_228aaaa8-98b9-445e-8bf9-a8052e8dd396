defmodule TtQuant.Events.EventDispatcher do
  @moduledoc """
  Central event dispatcher for real-time event distribution.

  This module handles broadcasting events to subscribers across different
  channels including orders, trades, market data, and notifications.
  """

  alias Phoenix.PubSub
  require Logger

  @pubsub TtQuant.PubSub

  # Event Types
  @order_events [
    :order_created,
    :order_submitted,
    :order_accepted,
    :order_rejected,
    :order_cancelled,
    :order_expired,
    :order_filled,
    :order_partially_filled,
    :order_updated
  ]

  @trade_events [
    :trade_executed,
    :trade_settled,
    :trade_cancelled
  ]

  @market_data_events [
    :tick_update,
    :quote_update,
    :trade_update,
    :book_update
  ]

  @portfolio_events [
    :balance_updated,
    :position_opened,
    :position_updated,
    :position_closed,
    :margin_updated
  ]

  @notification_events [
    :new_notification,
    :notification_read,
    :urgent_alert
  ]

  @doc """
  Dispatches an order event to relevant subscribers.
  """
  @spec dispatch_order_event(atom(), binary(), map()) :: :ok
  def dispatch_order_event(event_type, account_id, event_data) when event_type in @order_events do
    message = build_event_message(event_type, event_data)

    # Broadcast to account-specific channel
    PubSub.broadcast(@pubsub, "orders:#{account_id}", message)

    # Broadcast to global orders channel for admin/monitoring
    PubSub.broadcast(@pubsub, "orders:global", Map.put(message, :account_id, account_id))

    # Log high-priority events
    if event_type in [:order_rejected, :order_cancelled] do
      Logger.info("Order event dispatched: #{event_type} for account #{account_id}")
    end

    :ok
  end

  @doc """
  Dispatches a trade event to relevant subscribers.
  """
  @spec dispatch_trade_event(atom(), binary(), map()) :: :ok
  def dispatch_trade_event(event_type, account_id, event_data) when event_type in @trade_events do
    message = build_event_message(event_type, event_data)

    # Broadcast to account-specific channel
    PubSub.broadcast(@pubsub, "trades:#{account_id}", message)

    # Broadcast to global trades channel
    PubSub.broadcast(@pubsub, "trades:global", Map.put(message, :account_id, account_id))

    # Broadcast to instrument-specific channel if instrument_id is present
    if instrument_id = Map.get(event_data, :instrument_id) do
      PubSub.broadcast(@pubsub, "trades:instrument:#{instrument_id}", message)
    end

    Logger.info("Trade event dispatched: #{event_type} for account #{account_id}")
    :ok
  end

  @doc """
  Dispatches a market data event to relevant subscribers.
  """
  @spec dispatch_market_data_event(atom(), String.t(), map()) :: :ok
  def dispatch_market_data_event(event_type, instrument_id, event_data) when event_type in @market_data_events do
    message = build_event_message(event_type, event_data)

    # Broadcast to instrument-specific channel
    PubSub.broadcast(@pubsub, "market_data:#{instrument_id}", message)

    # Broadcast to global market data channel
    PubSub.broadcast(@pubsub, "market_data:global", Map.put(message, :instrument_id, instrument_id))

    # Broadcast to venue-specific channel if venue is present
    if venue = Map.get(event_data, :venue) do
      PubSub.broadcast(@pubsub, "market_data:venue:#{venue}", message)
    end

    :ok
  end

  @doc """
  Dispatches a portfolio event to relevant subscribers.
  """
  @spec dispatch_portfolio_event(atom(), binary(), map()) :: :ok
  def dispatch_portfolio_event(event_type, account_id, event_data) when event_type in @portfolio_events do
    message = build_event_message(event_type, event_data)

    # Broadcast to account-specific channel
    PubSub.broadcast(@pubsub, "portfolio:#{account_id}", message)

    # Broadcast to global portfolio channel for monitoring
    PubSub.broadcast(@pubsub, "portfolio:global", Map.put(message, :account_id, account_id))

    # Log important portfolio events
    if event_type in [:margin_updated, :position_closed] do
      Logger.info("Portfolio event dispatched: #{event_type} for account #{account_id}")
    end

    :ok
  end

  @doc """
  Dispatches a notification event to relevant subscribers.
  """
  @spec dispatch_notification_event(atom(), binary(), map()) :: :ok
  def dispatch_notification_event(event_type, account_id, event_data) when event_type in @notification_events do
    message = build_event_message(event_type, event_data)

    # Broadcast to account-specific channel
    PubSub.broadcast(@pubsub, "notifications:#{account_id}", message)

    # Broadcast urgent alerts to global channel
    if event_type == :urgent_alert do
      PubSub.broadcast(@pubsub, "notifications:urgent", Map.put(message, :account_id, account_id))
      Logger.warning("Urgent alert dispatched for account #{account_id}")
    end

    :ok
  end

  @doc """
  Dispatches a system event to all subscribers.
  """
  @spec dispatch_system_event(atom(), map()) :: :ok
  def dispatch_system_event(event_type, event_data) do
    message = build_event_message(event_type, event_data)

    # Broadcast to system-wide channel
    PubSub.broadcast(@pubsub, "system:events", message)

    Logger.info("System event dispatched: #{event_type}")
    :ok
  end

  @doc """
  Subscribes to order events for a specific account.
  """
  @spec subscribe_to_orders(binary()) :: :ok
  def subscribe_to_orders(account_id) do
    PubSub.subscribe(@pubsub, "orders:#{account_id}")
  end

  @doc """
  Subscribes to trade events for a specific account.
  """
  @spec subscribe_to_trades(binary()) :: :ok
  def subscribe_to_trades(account_id) do
    PubSub.subscribe(@pubsub, "trades:#{account_id}")
  end

  @doc """
  Subscribes to market data events for a specific instrument.
  """
  @spec subscribe_to_market_data(String.t()) :: :ok
  def subscribe_to_market_data(instrument_id) do
    PubSub.subscribe(@pubsub, "market_data:#{instrument_id}")
  end

  @doc """
  Subscribes to portfolio events for a specific account.
  """
  @spec subscribe_to_portfolio(binary()) :: :ok
  def subscribe_to_portfolio(account_id) do
    PubSub.subscribe(@pubsub, "portfolio:#{account_id}")
  end

  @doc """
  Subscribes to notification events for a specific account.
  """
  @spec subscribe_to_notifications(binary()) :: :ok
  def subscribe_to_notifications(account_id) do
    PubSub.subscribe(@pubsub, "notifications:#{account_id}")
  end

  @doc """
  Subscribes to system events.
  """
  @spec subscribe_to_system_events() :: :ok
  def subscribe_to_system_events do
    PubSub.subscribe(@pubsub, "system:events")
  end

  @doc """
  Subscribes to global events for monitoring/admin purposes.
  """
  @spec subscribe_to_global_events() :: :ok
  def subscribe_to_global_events do
    channels = [
      "orders:global",
      "trades:global",
      "portfolio:global",
      "notifications:urgent",
      "system:events"
    ]

    Enum.each(channels, &PubSub.subscribe(@pubsub, &1))
  end

  @doc """
  Unsubscribes from all channels for a process.
  """
  @spec unsubscribe_all() :: :ok
  def unsubscribe_all do
    # Phoenix.PubSub doesn't have a direct unsubscribe_all function
    # This would need to be implemented by tracking subscriptions
    # For now, we'll just return :ok
    :ok
  end

  @doc """
  Gets the list of subscribers for a channel.
  Note: Phoenix.PubSub doesn't expose subscribers list for security reasons.
  """
  @spec get_subscribers(String.t()) :: :not_available
  def get_subscribers(_channel) do
    # Phoenix.PubSub doesn't expose subscriber lists for security reasons
    :not_available
  end

  @doc """
  Gets statistics about active subscriptions.
  """
  @spec get_subscription_stats() :: map()
  def get_subscription_stats do
    # This would require tracking subscription metrics
    # For now, return basic info
    %{
      pubsub_name: @pubsub,
      node: Node.self(),
      timestamp: DateTime.utc_now()
    }
  end

  # Private helper functions

  defp build_event_message(event_type, event_data) do
    %{
      event_type: event_type,
      data: event_data,
      timestamp: DateTime.utc_now(),
      node: Node.self()
    }
  end
end
