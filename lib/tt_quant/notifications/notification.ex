defmodule TtQuant.Notifications.Notification do
  @moduledoc """
  Schema for system notifications.
  
  Manages notifications sent to users about various system events,
  order updates, risk alerts, and other important information.
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Portfolio.Account

  @type t :: %__MODULE__{
          id: binary(),
          account_id: binary() | nil,
          notification_type: String.t(),
          title: String.t(),
          message: String.t(),
          data: map(),
          priority: String.t(),
          is_read: boolean(),
          read_at: DateTime.t() | nil,
          expires_at: DateTime.t() | nil,
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @notification_types [
    "order_update",
    "trade_execution", 
    "risk_alert",
    "margin_call",
    "system_maintenance",
    "account_update",
    "market_alert",
    "position_update",
    "balance_update",
    "general"
  ]

  @priorities ["low", "normal", "high", "urgent"]

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "notifications" do
    field :notification_type, :string
    field :title, :string
    field :message, :string
    field :data, :map, default: %{}
    field :priority, :string, default: "normal"
    field :is_read, :boolean, default: false
    field :read_at, :utc_datetime
    field :expires_at, :utc_datetime

    belongs_to :account, Account

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for notification creation.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(notification, attrs) do
    notification
    |> cast(attrs, [
      :account_id, :notification_type, :title, :message, :data,
      :priority, :is_read, :read_at, :expires_at
    ])
    |> validate_required([:notification_type, :title, :message, :priority])
    |> validate_inclusion(:notification_type, @notification_types)
    |> validate_inclusion(:priority, @priorities)
    |> validate_length(:title, max: 255)
    |> validate_length(:message, max: 5000)
    |> foreign_key_constraint(:account_id)
    |> validate_read_consistency()
  end

  @doc """
  Creates an order update notification.
  """
  @spec order_update(binary(), String.t(), String.t(), map()) :: Ecto.Changeset.t()
  def order_update(account_id, title, message, data \\ %{}) do
    attrs = %{
      account_id: account_id,
      notification_type: "order_update",
      title: title,
      message: message,
      data: data,
      priority: "normal"
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Creates a trade execution notification.
  """
  @spec trade_execution(binary(), String.t(), String.t(), map()) :: Ecto.Changeset.t()
  def trade_execution(account_id, title, message, data \\ %{}) do
    attrs = %{
      account_id: account_id,
      notification_type: "trade_execution",
      title: title,
      message: message,
      data: data,
      priority: "high"
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Creates a risk alert notification.
  """
  @spec risk_alert(binary(), String.t(), String.t(), map()) :: Ecto.Changeset.t()
  def risk_alert(account_id, title, message, data \\ %{}) do
    attrs = %{
      account_id: account_id,
      notification_type: "risk_alert",
      title: title,
      message: message,
      data: data,
      priority: "urgent"
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Creates a margin call notification.
  """
  @spec margin_call(binary(), String.t(), String.t(), map()) :: Ecto.Changeset.t()
  def margin_call(account_id, title, message, data \\ %{}) do
    attrs = %{
      account_id: account_id,
      notification_type: "margin_call",
      title: title,
      message: message,
      data: data,
      priority: "urgent",
      expires_at: DateTime.add(DateTime.utc_now(), 24 * 60 * 60, :second) # 24 hours
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Marks notification as read.
  """
  @spec mark_as_read(t()) :: Ecto.Changeset.t()
  def mark_as_read(%__MODULE__{} = notification) do
    changeset(notification, %{
      is_read: true,
      read_at: DateTime.utc_now()
    })
  end

  @doc """
  Marks notification as unread.
  """
  @spec mark_as_unread(t()) :: Ecto.Changeset.t()
  def mark_as_unread(%__MODULE__{} = notification) do
    changeset(notification, %{
      is_read: false,
      read_at: nil
    })
  end

  @doc """
  Checks if notification is expired.
  """
  @spec expired?(t()) :: boolean()
  def expired?(%__MODULE__{expires_at: nil}), do: false
  def expired?(%__MODULE__{expires_at: expires_at}) do
    DateTime.compare(DateTime.utc_now(), expires_at) == :gt
  end

  @doc """
  Checks if notification is urgent.
  """
  @spec urgent?(t()) :: boolean()
  def urgent?(%__MODULE__{priority: "urgent"}), do: true
  def urgent?(_), do: false

  @doc """
  Checks if notification is high priority.
  """
  @spec high_priority?(t()) :: boolean()
  def high_priority?(%__MODULE__{priority: priority}) when priority in ["high", "urgent"], do: true
  def high_priority?(_), do: false

  @doc """
  Gets all valid notification types.
  """
  @spec notification_types() :: [String.t()]
  def notification_types, do: @notification_types

  @doc """
  Gets all valid priorities.
  """
  @spec priorities() :: [String.t()]
  def priorities, do: @priorities

  defp validate_read_consistency(changeset) do
    is_read = get_field(changeset, :is_read)
    read_at = get_field(changeset, :read_at)

    cond do
      is_read && is_nil(read_at) ->
        add_error(changeset, :read_at, "must be set when notification is read")
      
      not is_read && not is_nil(read_at) ->
        add_error(changeset, :read_at, "must be nil when notification is unread")
      
      true ->
        changeset
    end
  end
end
