defmodule TtQuant.Instruments do
  @moduledoc """
  The Instruments context.

  This module provides the main API for instrument management,
  including registration, lookup, validation, and market rules.
  """

  alias TtQuant.Instruments.{Instrument, Registry, MarketRules}
  alias TtQuant.Trading.Order
  alias TtQuant.Repo
  import Ecto.Query

  # Instrument Management

  @doc """
  Creates and registers a new instrument.
  """
  @spec create_instrument(map()) :: {:ok, Instrument.t()} | {:error, Ecto.Changeset.t()}
  def create_instrument(attrs) do
    changeset = Instrument.changeset(%Instrument{}, attrs)

    case Repo.insert(changeset) do
      {:ok, instrument} ->
        # Update cache without inserting to database again
        Registry.refresh_cache()
        {:ok, instrument}

      error -> error
    end
  end

  @doc """
  Creates a spot trading pair.
  """
  @spec create_spot_pair(String.t(), String.t(), String.t(), map()) :: {:ok, Instrument.t()} | {:error, Ecto.Changeset.t()}
  def create_spot_pair(base_currency, quote_currency, venue \\ "default", opts \\ %{}) do
    symbol = "#{base_currency}#{quote_currency}"
    name = "#{base_currency}/#{quote_currency} Spot"

    changeset = Instrument.create_spot(symbol, name, base_currency, quote_currency, Map.put(opts, :venue, venue))

    case Repo.insert(changeset) do
      {:ok, instrument} ->
        Registry.refresh_cache()
        {:ok, instrument}

      error -> error
    end
  end

  @doc """
  Creates a futures contract.
  """
  @spec create_futures_contract(String.t(), String.t(), Date.t(), String.t(), map()) :: {:ok, Instrument.t()} | {:error, Ecto.Changeset.t()}
  def create_futures_contract(base_currency, quote_currency, expiry_date, venue \\ "default", opts \\ %{}) do
    expiry_str = Date.to_string(expiry_date) |> String.replace("-", "")
    symbol = "#{base_currency}#{quote_currency}#{expiry_str}"
    name = "#{base_currency}/#{quote_currency} Futures #{expiry_date}"

    changeset = Instrument.create_futures(symbol, name, base_currency, quote_currency, expiry_date, Map.put(opts, :venue, venue))

    case Repo.insert(changeset) do
      {:ok, instrument} ->
        Registry.refresh_cache()
        {:ok, instrument}

      error -> error
    end
  end

  @doc """
  Gets an instrument by symbol and venue.
  """
  @spec get_instrument(String.t(), String.t()) :: Instrument.t() | nil
  def get_instrument(symbol, venue \\ "default") do
    Registry.get_instrument(symbol, venue)
  end

  @doc """
  Gets an instrument by full symbol (symbol@venue).
  """
  @spec get_instrument_by_full_symbol(String.t()) :: Instrument.t() | nil
  def get_instrument_by_full_symbol(full_symbol) do
    Registry.get_instrument_by_full_symbol(full_symbol)
  end

  @doc """
  Gets an instrument by ID.
  """
  @spec get_instrument_by_id(binary()) :: Instrument.t() | nil
  def get_instrument_by_id(id) do
    Repo.get(Instrument, id)
  end

  @doc """
  Lists all instruments for a venue.
  """
  @spec list_instruments(String.t()) :: [Instrument.t()]
  def list_instruments(venue \\ "default") do
    Registry.list_instruments(venue)
  end

  @doc """
  Lists all active instruments.
  """
  @spec list_active_instruments() :: [Instrument.t()]
  def list_active_instruments do
    Registry.list_active_instruments()
  end

  @doc """
  Searches instruments by criteria.
  """
  @spec search_instruments(map()) :: [Instrument.t()]
  def search_instruments(criteria) do
    Registry.search_instruments(criteria)
  end

  @doc """
  Updates an instrument.
  """
  @spec update_instrument(String.t(), String.t(), map()) :: {:ok, Instrument.t()} | {:error, any()}
  def update_instrument(symbol, venue, updates) do
    Registry.update_instrument(symbol, venue, updates)
  end

  @doc """
  Updates an instrument by ID.
  """
  @spec update_instrument_by_id(binary(), map()) :: {:ok, Instrument.t()} | {:error, Ecto.Changeset.t()}
  def update_instrument_by_id(id, updates) do
    case get_instrument_by_id(id) do
      nil -> {:error, :not_found}
      instrument ->
        changeset = Instrument.changeset(instrument, updates)

        case Repo.update(changeset) do
          {:ok, updated_instrument} ->
            # Update cache
            Registry.refresh_cache()
            {:ok, updated_instrument}

          error -> error
        end
    end
  end

  @doc """
  Deactivates an instrument.
  """
  @spec deactivate_instrument(String.t(), String.t()) :: {:ok, Instrument.t()} | {:error, any()}
  def deactivate_instrument(symbol, venue \\ "default") do
    Registry.deactivate_instrument(symbol, venue)
  end

  # Market Rules and Validation

  @doc """
  Validates an order against instrument rules.
  """
  @spec validate_order(Order.t(), String.t(), String.t()) :: :ok | {:error, String.t()}
  def validate_order(%Order{} = order, symbol, venue \\ "default") do
    case get_instrument(symbol, venue) do
      nil -> {:error, "Instrument not found: #{symbol}@#{venue}"}
      instrument -> MarketRules.validate_order(order, instrument)
    end
  end

  @doc """
  Validates an order using instrument ID.
  """
  @spec validate_order_by_instrument_id(Order.t()) :: :ok | {:error, String.t()}
  def validate_order_by_instrument_id(%Order{instrument_id: instrument_id} = order) do
    case get_instrument_by_full_symbol(instrument_id) do
      nil -> {:error, "Instrument not found: #{instrument_id}"}
      instrument -> MarketRules.validate_order(order, instrument)
    end
  end

  @doc """
  Suggests corrected price for an instrument.
  """
  @spec suggest_price(Decimal.t(), String.t(), String.t()) :: Decimal.t() | nil
  def suggest_price(price, symbol, venue \\ "default") do
    case get_instrument(symbol, venue) do
      nil -> nil
      instrument -> MarketRules.suggest_price(price, instrument)
    end
  end

  @doc """
  Suggests corrected quantity for an instrument.
  """
  @spec suggest_quantity(Decimal.t(), String.t(), String.t()) :: Decimal.t() | nil
  def suggest_quantity(quantity, symbol, venue \\ "default") do
    case get_instrument(symbol, venue) do
      nil -> nil
      instrument -> MarketRules.suggest_quantity(quantity, instrument)
    end
  end

  @doc """
  Calculates trading fees for an order.
  """
  @spec calculate_trading_fees(Order.t(), String.t(), String.t(), :maker | :taker) :: map() | nil
  def calculate_trading_fees(%Order{} = order, symbol, venue \\ "default", liquidity_side) do
    case get_instrument(symbol, venue) do
      nil -> nil
      instrument -> MarketRules.calculate_trading_fees(order, instrument, liquidity_side)
    end
  end

  @doc """
  Gets trading session information.
  """
  @spec get_trading_session(String.t(), String.t(), DateTime.t()) :: map() | nil
  def get_trading_session(symbol, venue \\ "default", datetime \\ DateTime.utc_now()) do
    case get_instrument(symbol, venue) do
      nil -> nil
      instrument -> MarketRules.get_trading_session(instrument, datetime)
    end
  end

  @doc """
  Checks if an instrument is currently tradeable.
  """
  @spec tradeable?(String.t(), String.t()) :: boolean()
  def tradeable?(symbol, venue \\ "default") do
    case get_instrument(symbol, venue) do
      nil -> false
      instrument -> Instrument.tradeable?(instrument)
    end
  end

  @doc """
  Gets risk parameters for an instrument.
  """
  @spec get_risk_parameters(String.t(), String.t()) :: map() | nil
  def get_risk_parameters(symbol, venue \\ "default") do
    case get_instrument(symbol, venue) do
      nil -> nil
      instrument -> MarketRules.get_risk_parameters(instrument)
    end
  end

  # Bulk Operations

  @doc """
  Imports instruments from a list of definitions.
  """
  @spec import_instruments([map()]) :: {:ok, [Instrument.t()]} | {:error, any()}
  def import_instruments(instrument_definitions) do
    results = Enum.map(instrument_definitions, fn attrs ->
      create_instrument(attrs)
    end)

    errors = Enum.filter(results, fn
      {:error, _} -> true
      _ -> false
    end)

    if Enum.empty?(errors) do
      instruments = Enum.map(results, fn {:ok, instrument} -> instrument end)
      {:ok, instruments}
    else
      {:error, errors}
    end
  end

  @doc """
  Exports instruments to a list of maps.
  """
  @spec export_instruments(String.t()) :: [map()]
  def export_instruments(venue \\ "default") do
    list_instruments(venue)
    |> Enum.map(fn instrument ->
      %{
        symbol: instrument.symbol,
        name: instrument.name,
        instrument_type: instrument.instrument_type,
        base_currency: instrument.base_currency,
        quote_currency: instrument.quote_currency,
        tick_size: instrument.tick_size,
        lot_size: instrument.lot_size,
        min_quantity: instrument.min_quantity,
        max_quantity: instrument.max_quantity,
        price_precision: instrument.price_precision,
        quantity_precision: instrument.quantity_precision,
        maker_fee: instrument.maker_fee,
        taker_fee: instrument.taker_fee,
        venue: instrument.venue,
        trading_hours: instrument.trading_hours,
        contract_size: instrument.contract_size,
        expiry_date: instrument.expiry_date,
        strike_price: instrument.strike_price,
        option_type: instrument.option_type
      }
    end)
  end

  # Registry Management

  @doc """
  Refreshes the instrument cache.
  """
  @spec refresh_cache() :: :ok
  def refresh_cache do
    Registry.refresh_cache()
  end

  @doc """
  Gets registry statistics.
  """
  @spec get_registry_stats() :: map()
  def get_registry_stats do
    Registry.get_stats()
  end

  @doc """
  Subscribes to instrument updates.
  """
  @spec subscribe_to_updates() :: :ok
  def subscribe_to_updates do
    Registry.subscribe()
  end

  @doc """
  Unsubscribes from instrument updates.
  """
  @spec unsubscribe_from_updates() :: :ok
  def unsubscribe_from_updates do
    Registry.unsubscribe()
  end

  # Common Instrument Presets

  @doc """
  Creates common cryptocurrency pairs for a venue.
  """
  @spec create_crypto_pairs(String.t()) :: {:ok, [Instrument.t()]} | {:error, any()}
  def create_crypto_pairs(venue \\ "default") do
    pairs = [
      {"BTC", "USDT"},
      {"ETH", "USDT"},
      {"BTC", "USD"},
      {"ETH", "USD"},
      {"BTC", "EUR"},
      {"ETH", "EUR"}
    ]

    definitions = Enum.map(pairs, fn {base, quote} ->
      %{
        symbol: "#{base}#{quote}",
        name: "#{base}/#{quote} Spot",
        instrument_type: :spot,
        base_currency: base,
        quote_currency: quote,
        tick_size: if(quote in ["USD", "EUR", "USDT"], do: Decimal.new("0.01"), else: Decimal.new("0.00000001")),
        lot_size: Decimal.new("0.00001"),
        min_quantity: Decimal.new("0.00001"),
        max_quantity: Decimal.new("1000000"),
        price_precision: if(quote in ["USD", "EUR", "USDT"], do: 2, else: 8),
        quantity_precision: 8,
        maker_fee: Decimal.new("0.001"),
        taker_fee: Decimal.new("0.001"),
        venue: venue
      }
    end)

    import_instruments(definitions)
  end

  @doc """
  Creates common forex pairs for a venue.
  """
  @spec create_forex_pairs(String.t()) :: {:ok, [Instrument.t()]} | {:error, any()}
  def create_forex_pairs(venue \\ "default") do
    pairs = [
      {"EUR", "USD"},
      {"GBP", "USD"},
      {"USD", "JPY"},
      {"USD", "CNY"},
      {"EUR", "CNY"},
      {"GBP", "CNY"}
    ]

    definitions = Enum.map(pairs, fn {base, quote} ->
      %{
        symbol: "#{base}#{quote}",
        name: "#{base}/#{quote} Spot",
        instrument_type: :spot,
        base_currency: base,
        quote_currency: quote,
        tick_size: if(quote == "JPY", do: Decimal.new("0.001"), else: Decimal.new("0.00001")),
        lot_size: Decimal.new("1000"),
        min_quantity: Decimal.new("1000"),
        max_quantity: Decimal.new("10000000"),
        price_precision: if(quote == "JPY", do: 3, else: 5),
        quantity_precision: 0,
        maker_fee: Decimal.new("0.0002"),
        taker_fee: Decimal.new("0.0002"),
        venue: venue
      }
    end)

    import_instruments(definitions)
  end
end
