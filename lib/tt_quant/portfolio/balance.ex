defmodule TtQuant.Portfolio.Balance do
  @moduledoc """
  Balance management for individual currency holdings within an account.

  Tracks total, available, and locked amounts for each currency.
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Portfolio.Account

  @type t :: %__MODULE__{
          id: binary(),
          account_id: binary(),
          currency: String.t(),
          total: Decimal.t(),
          available: Decimal.t(),
          locked: Decimal.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "balances" do
    field :currency, :string
    field :total, :decimal
    field :available, :decimal
    field :locked, :decimal

    belongs_to :account, Account

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for balance creation and updates.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(balance, attrs) do
    balance
    |> cast(attrs, [:currency, :total, :available, :locked, :account_id])
    |> validate_required([:currency, :total, :available, :locked, :account_id])
    |> validate_inclusion(:currency, Account.supported_currencies())
    |> validate_number(:total, greater_than_or_equal_to: 0)
    |> validate_number(:available, greater_than_or_equal_to: 0)
    |> validate_number(:locked, greater_than_or_equal_to: 0)
    |> validate_balance_consistency()
    |> unique_constraint([:account_id, :currency])
  end

  @doc """
  Creates a new balance with zero amounts.
  """
  @spec new_zero_balance(binary(), String.t()) :: t()
  def new_zero_balance(account_id, currency) do
    %__MODULE__{
      account_id: account_id,
      currency: currency,
      total: Decimal.new(0),
      available: Decimal.new(0),
      locked: Decimal.new(0)
    }
  end

  @doc """
  Adds amount to available balance.
  """
  @spec add_available(t(), Decimal.t()) :: t()
  def add_available(%__MODULE__{} = balance, amount) do
    new_available = Decimal.add(balance.available, amount)
    new_total = Decimal.add(balance.total, amount)

    %{balance | available: new_available, total: new_total}
  end

  @doc """
  Subtracts amount from available balance.
  """
  @spec subtract_available(t(), Decimal.t()) :: {:ok, t()} | {:error, :insufficient_balance}
  def subtract_available(%__MODULE__{} = balance, amount) do
    if Decimal.compare(balance.available, amount) == :lt do
      {:error, :insufficient_balance}
    else
      new_available = Decimal.sub(balance.available, amount)
      new_total = Decimal.sub(balance.total, amount)

      {:ok, %{balance | available: new_available, total: new_total}}
    end
  end

  @doc """
  Locks amount from available balance.
  """
  @spec lock_amount(t(), Decimal.t()) :: {:ok, t()} | {:error, :insufficient_balance}
  def lock_amount(%__MODULE__{} = balance, amount) do
    if Decimal.compare(balance.available, amount) == :lt do
      {:error, :insufficient_balance}
    else
      new_available = Decimal.sub(balance.available, amount)
      new_locked = Decimal.add(balance.locked, amount)

      {:ok, %{balance | available: new_available, locked: new_locked}}
    end
  end

  @doc """
  Unlocks amount back to available balance.
  """
  @spec unlock_amount(t(), Decimal.t()) :: {:ok, t()} | {:error, :insufficient_locked}
  def unlock_amount(%__MODULE__{} = balance, amount) do
    if Decimal.compare(balance.locked, amount) == :lt do
      {:error, :insufficient_locked}
    else
      new_locked = Decimal.sub(balance.locked, amount)
      new_available = Decimal.add(balance.available, amount)

      {:ok, %{balance | available: new_available, locked: new_locked}}
    end
  end

  @doc """
  Releases locked amount (removes from total).
  """
  @spec release_locked(t(), Decimal.t()) :: {:ok, t()} | {:error, :insufficient_locked}
  def release_locked(%__MODULE__{} = balance, amount) do
    if Decimal.compare(balance.locked, amount) == :lt do
      {:error, :insufficient_locked}
    else
      new_locked = Decimal.sub(balance.locked, amount)
      new_total = Decimal.sub(balance.total, amount)

      {:ok, %{balance | locked: new_locked, total: new_total}}
    end
  end

  @doc """
  Checks if balance has sufficient available amount.
  """
  @spec has_sufficient_available?(t(), Decimal.t()) :: boolean()
  def has_sufficient_available?(%__MODULE__{available: available}, amount) do
    Decimal.compare(available, amount) != :lt
  end

  @doc """
  Checks if balance has sufficient locked amount.
  """
  @spec has_sufficient_locked?(t(), Decimal.t()) :: boolean()
  def has_sufficient_locked?(%__MODULE__{locked: locked}, amount) do
    Decimal.compare(locked, amount) != :lt
  end

  @doc """
  Gets the utilization ratio (locked / total).
  """
  @spec utilization_ratio(t()) :: Decimal.t()
  def utilization_ratio(%__MODULE__{total: total, locked: locked}) do
    if Decimal.compare(total, Decimal.new(0)) == :eq do
      Decimal.new(0)
    else
      Decimal.div(locked, total)
    end
  end

  # Private validation function
  defp validate_balance_consistency(changeset) do
    total = get_field(changeset, :total)
    available = get_field(changeset, :available)
    locked = get_field(changeset, :locked)

    if total && available && locked do
      expected_total = Decimal.add(available, locked)
      if Decimal.compare(total, expected_total) != :eq do
        add_error(changeset, :total, "must equal available + locked")
      else
        changeset
      end
    else
      changeset
    end
  end
end
