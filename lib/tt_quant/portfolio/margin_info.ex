defmodule TtQuant.Portfolio.MarginInfo do
  @moduledoc """
  Margin information management for margin and futures accounts.

  Tracks margin requirements, utilization, and risk metrics.
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Portfolio.Account

  @type t :: %__MODULE__{
          id: binary(),
          account_id: binary(),
          initial_margin: Decimal.t(),
          maintenance_margin: Decimal.t(),
          used_margin: Decimal.t(),
          available_margin: Decimal.t(),
          margin_ratio: Decimal.t(),
          currency: String.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "margin_infos" do
    field :initial_margin, :decimal
    field :maintenance_margin, :decimal
    field :used_margin, :decimal
    field :available_margin, :decimal
    field :margin_ratio, :decimal
    field :currency, :string

    belongs_to :account, Account

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for margin info creation and updates.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(margin_info, attrs) do
    margin_info
    |> cast(attrs, [:initial_margin, :maintenance_margin, :used_margin, :available_margin, :margin_ratio, :currency, :account_id])
    |> validate_required([:initial_margin, :maintenance_margin, :used_margin, :available_margin, :currency, :account_id])
    |> validate_inclusion(:currency, Account.supported_currencies())
    |> validate_number(:initial_margin, greater_than_or_equal_to: 0)
    |> validate_number(:maintenance_margin, greater_than_or_equal_to: 0)
    |> validate_number(:used_margin, greater_than_or_equal_to: 0)
    |> validate_number(:available_margin, greater_than_or_equal_to: 0)
    |> validate_number(:margin_ratio, greater_than_or_equal_to: 0, less_than_or_equal_to: 1)
    |> validate_margin_consistency()
    |> unique_constraint(:account_id)
  end

  @doc """
  Creates new margin info with zero values.
  """
  @spec new_zero_margin(binary(), String.t()) :: t()
  def new_zero_margin(account_id, currency) do
    %__MODULE__{
      account_id: account_id,
      initial_margin: Decimal.new(0),
      maintenance_margin: Decimal.new(0),
      used_margin: Decimal.new(0),
      available_margin: Decimal.new(0),
      margin_ratio: Decimal.new(0),
      currency: currency
    }
  end

  @doc """
  Updates margin usage after position changes.
  """
  @spec update_margin_usage(t(), Decimal.t()) :: t()
  def update_margin_usage(%__MODULE__{} = margin_info, new_used_margin) do
    new_available = Decimal.sub(margin_info.initial_margin, new_used_margin)
    new_ratio = calculate_margin_ratio(new_used_margin, margin_info.initial_margin)

    %{margin_info |
      used_margin: new_used_margin,
      available_margin: new_available,
      margin_ratio: new_ratio
    }
  end

  @doc """
  Checks if there's sufficient margin for a new position.
  """
  @spec has_sufficient_margin?(t(), Decimal.t()) :: boolean()
  def has_sufficient_margin?(%__MODULE__{available_margin: available}, required_margin) do
    Decimal.compare(available, required_margin) != :lt
  end

  @doc """
  Checks if margin ratio is above maintenance level.
  """
  @spec is_above_maintenance?(t()) :: boolean()
  def is_above_maintenance?(%__MODULE__{margin_ratio: ratio, maintenance_margin: maintenance, initial_margin: initial}) do
    if Decimal.compare(initial, Decimal.new(0)) == :eq do
      true  # No margin requirements
    else
      maintenance_ratio = Decimal.div(maintenance, initial)
      Decimal.compare(ratio, maintenance_ratio) == :gt
    end
  end

  @doc """
  Gets margin call threshold (typically 80% of maintenance margin).
  """
  @spec margin_call_threshold(t()) :: Decimal.t()
  def margin_call_threshold(%__MODULE__{maintenance_margin: maintenance, initial_margin: initial}) do
    if Decimal.compare(initial, Decimal.new(0)) == :eq do
      Decimal.new(0)
    else
      maintenance_ratio = Decimal.div(maintenance, initial)
      Decimal.mult(maintenance_ratio, Decimal.new("0.8"))
    end
  end

  @doc """
  Checks if account is in margin call.
  """
  @spec in_margin_call?(t()) :: boolean()
  def in_margin_call?(%__MODULE__{} = margin_info) do
    threshold = margin_call_threshold(margin_info)
    Decimal.compare(margin_info.margin_ratio, threshold) == :lt
  end

  @doc """
  Calculates liquidation risk level (0.0 = no risk, 1.0 = immediate liquidation).
  """
  @spec liquidation_risk(t()) :: Decimal.t()
  def liquidation_risk(%__MODULE__{} = margin_info) do
    if is_above_maintenance?(margin_info) do
      Decimal.new(0)
    else
      # Risk increases as we approach maintenance margin
      maintenance_threshold = margin_call_threshold(margin_info)
      if Decimal.compare(margin_info.margin_ratio, maintenance_threshold) == :lt do
        # Calculate risk based on how close we are to liquidation
        risk_factor = Decimal.div(margin_info.margin_ratio, maintenance_threshold)
        Decimal.sub(Decimal.new(1), risk_factor)
      else
        Decimal.new(0)
      end
    end
  end

  @doc """
  Gets available margin as percentage of initial margin.
  """
  @spec available_margin_percentage(t()) :: Decimal.t()
  def available_margin_percentage(%__MODULE__{available_margin: available, initial_margin: initial}) do
    if Decimal.compare(initial, Decimal.new(0)) == :eq do
      Decimal.new(0)
    else
      Decimal.div(available, initial)
    end
  end

  # Private helper to calculate margin ratio
  defp calculate_margin_ratio(used_margin, initial_margin) do
    if Decimal.compare(initial_margin, Decimal.new(0)) == :eq do
      Decimal.new(0)
    else
      Decimal.div(used_margin, initial_margin)
    end
  end

  # Private validation function
  defp validate_margin_consistency(changeset) do
    initial = get_field(changeset, :initial_margin)
    used = get_field(changeset, :used_margin)
    available = get_field(changeset, :available_margin)

    if initial && used && available do
      expected_available = Decimal.sub(initial, used)
      if Decimal.compare(available, expected_available) != :eq do
        add_error(changeset, :available_margin, "must equal initial_margin - used_margin")
      else
        changeset
      end
    else
      changeset
    end
  end
end
