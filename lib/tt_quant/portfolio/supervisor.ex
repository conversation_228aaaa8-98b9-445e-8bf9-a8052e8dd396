defmodule TtQuant.Portfolio.Supervisor do
  @moduledoc """
  Supervisor for portfolio servers and registry.
  
  Manages the lifecycle of portfolio servers for different accounts.
  """

  use Supervisor
  require Logger

  def start_link(init_arg) do
    Supervisor.start_link(__MODULE__, init_arg, name: __MODULE__)
  end

  @impl true
  def init(_init_arg) do
    children = [
      # Registry for portfolio servers
      {Registry, keys: :unique, name: TtQuant.Portfolio.Registry},
      
      # Dynamic supervisor for portfolio servers
      {DynamicSupervisor, strategy: :one_for_one, name: TtQuant.Portfolio.DynamicSupervisor}
    ]

    Supervisor.init(children, strategy: :one_for_one)
  end

  @doc """
  Starts a portfolio server for a specific account.
  """
  @spec start_portfolio_server(binary()) :: DynamicSupervisor.on_start_child()
  def start_portfolio_server(account_id) do
    child_spec = %{
      id: {TtQuant.Portfolio.Server, account_id},
      start: {TtQuant.Portfolio.Server, :start_link, [account_id]},
      restart: :transient
    }

    case DynamicSupervisor.start_child(TtQuant.Portfolio.DynamicSupervisor, child_spec) do
      {:ok, pid} ->
        Logger.info("Started portfolio server for account #{account_id}")
        {:ok, pid}
        
      {:error, {:already_started, pid}} ->
        Logger.debug("Portfolio server for account #{account_id} already running")
        {:ok, pid}
        
      error ->
        Logger.error("Failed to start portfolio server for account #{account_id}: #{inspect(error)}")
        error
    end
  end

  @doc """
  Stops a portfolio server for a specific account.
  """
  @spec stop_portfolio_server(binary()) :: :ok | {:error, :not_found}
  def stop_portfolio_server(account_id) do
    case Registry.lookup(TtQuant.Portfolio.Registry, account_id) do
      [{pid, _}] ->
        DynamicSupervisor.terminate_child(TtQuant.Portfolio.DynamicSupervisor, pid)
        Logger.info("Stopped portfolio server for account #{account_id}")
        :ok
        
      [] ->
        {:error, :not_found}
    end
  end

  @doc """
  Lists all running portfolio servers.
  """
  @spec list_portfolio_servers() :: [binary()]
  def list_portfolio_servers do
    Registry.select(TtQuant.Portfolio.Registry, [{{:"$1", :_, :_}, [], [:"$1"]}])
  end

  @doc """
  Checks if a portfolio server is running for an account.
  """
  @spec portfolio_server_running?(binary()) :: boolean()
  def portfolio_server_running?(account_id) do
    case Registry.lookup(TtQuant.Portfolio.Registry, account_id) do
      [{_pid, _}] -> true
      [] -> false
    end
  end

  @doc """
  Gets the PID of a portfolio server for an account.
  """
  @spec get_portfolio_server_pid(binary()) :: {:ok, pid()} | {:error, :not_found}
  def get_portfolio_server_pid(account_id) do
    case Registry.lookup(TtQuant.Portfolio.Registry, account_id) do
      [{pid, _}] -> {:ok, pid}
      [] -> {:error, :not_found}
    end
  end

  @doc """
  Ensures a portfolio server is running for an account.
  """
  @spec ensure_portfolio_server(binary()) :: {:ok, pid()} | {:error, any()}
  def ensure_portfolio_server(account_id) do
    case get_portfolio_server_pid(account_id) do
      {:ok, pid} -> {:ok, pid}
      {:error, :not_found} -> start_portfolio_server(account_id)
    end
  end
end
