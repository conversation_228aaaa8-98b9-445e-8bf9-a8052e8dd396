defmodule TtQuant.Portfolio.Account do
  @moduledoc """
  Account management module for handling account balances, margins, and available funds.
  
  This module provides functionality to:
  - Track account balances across multiple currencies
  - Calculate available and locked funds
  - Manage margin requirements
  - Handle currency conversions
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Portfolio.{Balance, MarginInfo}

  @type t :: %__MODULE__{
          id: binary(),
          name: String.t(),
          account_type: account_type(),
          base_currency: String.t(),
          balances: [Balance.t()],
          margin_info: MarginInfo.t() | nil,
          is_active: boolean(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @type account_type :: :cash | :margin | :futures

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "accounts" do
    field :name, :string
    field :account_type, Ecto.Enum, values: [:cash, :margin, :futures]
    field :base_currency, :string, default: "USD"
    field :is_active, :boolean, default: true

    has_many :balances, Balance, foreign_key: :account_id
    has_one :margin_info, MarginInfo, foreign_key: :account_id

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for account creation and updates.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(account, attrs) do
    account
    |> cast(attrs, [:name, :account_type, :base_currency, :is_active])
    |> validate_required([:name, :account_type, :base_currency])
    |> validate_length(:name, min: 1, max: 100)
    |> validate_inclusion(:base_currency, supported_currencies())
    |> unique_constraint(:name)
  end

  @doc """
  Returns list of supported currencies.
  """
  @spec supported_currencies() :: [String.t()]
  def supported_currencies do
    ["USD", "EUR", "GBP", "JPY", "CNY", "BTC", "ETH", "USDT", "USDC"]
  end

  @doc """
  Gets the total balance for a specific currency.
  """
  @spec get_balance(t(), String.t()) :: Decimal.t()
  def get_balance(%__MODULE__{balances: balances}, currency) do
    case Enum.find(balances, &(&1.currency == currency)) do
      nil -> Decimal.new(0)
      balance -> balance.total
    end
  end

  @doc """
  Gets the available balance for a specific currency.
  """
  @spec get_available_balance(t(), String.t()) :: Decimal.t()
  def get_available_balance(%__MODULE__{balances: balances}, currency) do
    case Enum.find(balances, &(&1.currency == currency)) do
      nil -> Decimal.new(0)
      balance -> balance.available
    end
  end

  @doc """
  Gets the locked balance for a specific currency.
  """
  @spec get_locked_balance(t(), String.t()) :: Decimal.t()
  def get_locked_balance(%__MODULE__{balances: balances}, currency) do
    case Enum.find(balances, &(&1.currency == currency)) do
      nil -> Decimal.new(0)
      balance -> balance.locked
    end
  end

  @doc """
  Checks if account has sufficient available balance for a given amount and currency.
  """
  @spec has_sufficient_balance?(t(), Decimal.t(), String.t()) :: boolean()
  def has_sufficient_balance?(account, amount, currency) do
    available = get_available_balance(account, currency)
    Decimal.compare(available, amount) != :lt
  end

  @doc """
  Gets all non-zero balances for the account.
  """
  @spec get_non_zero_balances(t()) :: [Balance.t()]
  def get_non_zero_balances(%__MODULE__{balances: balances}) do
    Enum.filter(balances, fn balance ->
      Decimal.compare(balance.total, Decimal.new(0)) == :gt
    end)
  end

  @doc """
  Calculates the total account value in base currency.
  This would require exchange rates for accurate conversion.
  """
  @spec calculate_total_value(t(), map()) :: Decimal.t()
  def calculate_total_value(%__MODULE__{balances: balances, base_currency: base_currency}, exchange_rates \\ %{}) do
    balances
    |> Enum.reduce(Decimal.new(0), fn balance, acc ->
      converted_value = convert_to_base_currency(balance, base_currency, exchange_rates)
      Decimal.add(acc, converted_value)
    end)
  end

  # Private helper function to convert balance to base currency
  defp convert_to_base_currency(%Balance{currency: currency, total: total}, base_currency, exchange_rates) do
    if currency == base_currency do
      total
    else
      rate_key = "#{currency}_#{base_currency}"
      case Map.get(exchange_rates, rate_key) do
        nil -> Decimal.new(0)  # No rate available, skip this balance
        rate -> Decimal.mult(total, Decimal.new(rate))
      end
    end
  end
end
