defmodule TtQuant.Portfolio.Position do
  @moduledoc """
  Position management for tracking open positions across instruments.

  Handles position sizing, P&L calculations, and risk metrics.
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Portfolio.Account

  @type t :: %__MODULE__{
          id: binary(),
          account_id: binary(),
          instrument_id: String.t(),
          side: position_side(),
          quantity: Decimal.t(),
          average_price: Decimal.t(),
          current_price: Decimal.t() | nil,
          unrealized_pnl: Decimal.t(),
          realized_pnl: Decimal.t(),
          currency: String.t(),
          opened_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @type position_side :: :long | :short | :flat

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "positions" do
    field :instrument_id, :string
    field :side, Ecto.Enum, values: [:long, :short, :flat]
    field :quantity, :decimal
    field :average_price, :decimal
    field :current_price, :decimal
    field :unrealized_pnl, :decimal, default: 0
    field :realized_pnl, :decimal, default: 0
    field :currency, :string
    field :opened_at, :utc_datetime

    belongs_to :account, Account

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for position creation and updates.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(position, attrs) do
    position
    |> cast(attrs, [:instrument_id, :side, :quantity, :average_price, :current_price,
                    :unrealized_pnl, :realized_pnl, :currency, :opened_at, :account_id])
    |> validate_required([:instrument_id, :side, :quantity, :average_price, :currency, :account_id])
    |> validate_inclusion(:currency, Account.supported_currencies())
    |> validate_number(:quantity, greater_than_or_equal_to: 0)
    |> validate_average_price()
    |> validate_position_consistency()
    |> unique_constraint([:account_id, :instrument_id])
  end

  @doc """
  Creates a new position from a trade.
  """
  @spec new_position(binary(), String.t(), :buy | :sell, Decimal.t(), Decimal.t(), String.t()) :: t()
  def new_position(account_id, instrument_id, trade_side, quantity, price, currency) do
    side = if trade_side == :buy, do: :long, else: :short

    %__MODULE__{
      account_id: account_id,
      instrument_id: instrument_id,
      side: side,
      quantity: quantity,
      average_price: price,
      current_price: price,
      unrealized_pnl: Decimal.new(0),
      realized_pnl: Decimal.new(0),
      currency: currency,
      opened_at: DateTime.utc_now()
    }
  end

  @doc """
  Updates position with a new trade (increases or reduces position).
  """
  @spec update_with_trade(t(), :buy | :sell, Decimal.t(), Decimal.t()) :: {:ok, t()} | {:ok, t(), Decimal.t()}
  def update_with_trade(%__MODULE__{} = position, trade_side, trade_quantity, trade_price) do
    position_side = if trade_side == :buy, do: :long, else: :short

    cond do
      position.side == :flat ->
        # Opening new position
        new_position = %{position |
          side: position_side,
          quantity: trade_quantity,
          average_price: trade_price,
          current_price: trade_price,
          opened_at: DateTime.utc_now()
        }
        {:ok, new_position}

      position.side == position_side ->
        # Adding to existing position
        new_quantity = Decimal.add(position.quantity, trade_quantity)
        new_average_price = calculate_new_average_price(
          position.quantity, position.average_price,
          trade_quantity, trade_price
        )

        new_position = %{position |
          quantity: new_quantity,
          average_price: new_average_price,
          current_price: trade_price
        }
        {:ok, new_position}

      true ->
        # Reducing or closing position (opposite side)
        if Decimal.compare(trade_quantity, position.quantity) == :lt do
          # Partial close
          new_quantity = Decimal.sub(position.quantity, trade_quantity)
          realized_pnl = calculate_realized_pnl(position, trade_quantity, trade_price)

          new_position = %{position |
            quantity: new_quantity,
            current_price: trade_price,
            realized_pnl: Decimal.add(position.realized_pnl, realized_pnl)
          }
          {:ok, new_position, realized_pnl}
        else
          # Full close or reverse
          realized_pnl = calculate_realized_pnl(position, position.quantity, trade_price)
          remaining_quantity = Decimal.sub(trade_quantity, position.quantity)

          if Decimal.compare(remaining_quantity, Decimal.new(0)) == :eq do
            # Exact close
            closed_position = %{position |
              side: :flat,
              quantity: Decimal.new(0),
              current_price: trade_price,
              unrealized_pnl: Decimal.new(0),
              realized_pnl: Decimal.add(position.realized_pnl, realized_pnl)
            }
            {:ok, closed_position, realized_pnl}
          else
            # Close and reverse
            new_position = %{position |
              side: position_side,
              quantity: remaining_quantity,
              average_price: trade_price,
              current_price: trade_price,
              unrealized_pnl: Decimal.new(0),
              realized_pnl: Decimal.add(position.realized_pnl, realized_pnl),
              opened_at: DateTime.utc_now()
            }
            {:ok, new_position, realized_pnl}
          end
        end
    end
  end

  @doc """
  Updates unrealized P&L based on current market price.
  """
  @spec update_unrealized_pnl(t(), Decimal.t()) :: t()
  def update_unrealized_pnl(%__MODULE__{side: :flat} = position, _current_price) do
    %{position | unrealized_pnl: Decimal.new(0)}
  end

  def update_unrealized_pnl(%__MODULE__{} = position, current_price) do
    unrealized_pnl = calculate_unrealized_pnl(position, current_price)
    %{position | current_price: current_price, unrealized_pnl: unrealized_pnl}
  end

  @doc """
  Checks if position is open (not flat).
  """
  @spec is_open?(t()) :: boolean()
  def is_open?(%__MODULE__{side: :flat}), do: false
  def is_open?(%__MODULE__{quantity: quantity}) do
    Decimal.compare(quantity, Decimal.new(0)) == :gt
  end

  @doc """
  Gets position value at current price.
  """
  @spec position_value(t()) :: Decimal.t()
  def position_value(%__MODULE__{quantity: quantity, current_price: current_price}) when not is_nil(current_price) do
    Decimal.mult(quantity, current_price)
  end
  def position_value(%__MODULE__{quantity: quantity, average_price: average_price}) do
    Decimal.mult(quantity, average_price)
  end

  @doc """
  Gets total P&L (realized + unrealized).
  """
  @spec total_pnl(t()) :: Decimal.t()
  def total_pnl(%__MODULE__{realized_pnl: realized, unrealized_pnl: unrealized}) do
    Decimal.add(realized, unrealized)
  end

  # Private helper functions
  defp calculate_new_average_price(old_quantity, old_price, new_quantity, new_price) do
    old_value = Decimal.mult(old_quantity, old_price)
    new_value = Decimal.mult(new_quantity, new_price)
    total_value = Decimal.add(old_value, new_value)
    total_quantity = Decimal.add(old_quantity, new_quantity)

    Decimal.div(total_value, total_quantity)
  end

  defp calculate_realized_pnl(%__MODULE__{side: :long, average_price: avg_price}, quantity, exit_price) do
    price_diff = Decimal.sub(exit_price, avg_price)
    Decimal.mult(quantity, price_diff)
  end

  defp calculate_realized_pnl(%__MODULE__{side: :short, average_price: avg_price}, quantity, exit_price) do
    price_diff = Decimal.sub(avg_price, exit_price)
    Decimal.mult(quantity, price_diff)
  end

  defp calculate_unrealized_pnl(%__MODULE__{side: :long, quantity: quantity, average_price: avg_price}, current_price) do
    price_diff = Decimal.sub(current_price, avg_price)
    Decimal.mult(quantity, price_diff)
  end

  defp calculate_unrealized_pnl(%__MODULE__{side: :short, quantity: quantity, average_price: avg_price}, current_price) do
    price_diff = Decimal.sub(avg_price, current_price)
    Decimal.mult(quantity, price_diff)
  end

  defp validate_average_price(changeset) do
    side = get_field(changeset, :side)
    average_price = get_field(changeset, :average_price)

    if side == :flat do
      # Flat positions can have zero average price
      changeset
    else
      # Active positions must have positive average price
      if average_price && Decimal.compare(average_price, Decimal.new(0)) == :gt do
        changeset
      else
        add_error(changeset, :average_price, "must be greater than 0 for active positions")
      end
    end
  end

  defp validate_position_consistency(changeset) do
    side = get_field(changeset, :side)
    quantity = get_field(changeset, :quantity)

    if side == :flat && quantity && Decimal.compare(quantity, Decimal.new(0)) != :eq do
      add_error(changeset, :quantity, "must be zero when position is flat")
    else
      changeset
    end
  end
end
