defmodule TtQuant.Portfolio.Server do
  @moduledoc """
  GenServer for managing portfolio state and real-time updates.

  Handles:
  - Real-time position updates
  - Balance changes
  - Risk monitoring
  - Portfolio event broadcasting
  """

  use GenServer
  require Logger
  alias TtQuant.Portfolio.{Portfolio, Account, Position}
  alias Phoenix.PubSub

  @type state :: %{
          account_id: binary(),
          account: Account.t() | nil,
          positions: %{String.t() => Position.t()},
          last_prices: %{String.t() => Decimal.t()},
          risk_alerts: [map()],
          subscribers: MapSet.t()
        }

  # Client API

  @doc """
  Starts the portfolio server for a specific account.
  """
  @spec start_link(binary()) :: GenServer.on_start()
  def start_link(account_id) do
    GenServer.start_link(__MODULE__, account_id, name: via_tuple(account_id))
  end

  @doc """
  Gets current portfolio state.
  """
  @spec get_state(binary()) :: state()
  def get_state(account_id) do
    GenServer.call(via_tuple(account_id), :get_state)
  end

  @doc """
  Updates position with a new trade.
  """
  @spec update_position(binary(), String.t(), :buy | :sell, Decimal.t(), Decimal.t(), String.t()) ::
    {:ok, Position.t()} | {:error, any()}
  def update_position(account_id, instrument_id, side, quantity, price, currency) do
    GenServer.call(via_tuple(account_id), {:update_position, instrument_id, side, quantity, price, currency})
  end

  @doc """
  Updates balance for a currency.
  """
  @spec update_balance(binary(), String.t(), Decimal.t()) :: {:ok, any()} | {:error, any()}
  def update_balance(account_id, currency, amount) do
    GenServer.call(via_tuple(account_id), {:update_balance, currency, amount})
  end

  @doc """
  Updates market prices for instruments.
  """
  @spec update_prices(binary(), %{String.t() => Decimal.t()}) :: :ok
  def update_prices(account_id, price_map) do
    GenServer.cast(via_tuple(account_id), {:update_prices, price_map})
  end

  @doc """
  Subscribes to portfolio updates.
  """
  @spec subscribe(binary()) :: :ok
  def subscribe(account_id) do
    GenServer.cast(via_tuple(account_id), {:subscribe, self()})
    PubSub.subscribe(TtQuant.PubSub, "portfolio:#{account_id}")
  end

  @doc """
  Unsubscribes from portfolio updates.
  """
  @spec unsubscribe(binary()) :: :ok
  def unsubscribe(account_id) do
    GenServer.cast(via_tuple(account_id), {:unsubscribe, self()})
    PubSub.unsubscribe(TtQuant.PubSub, "portfolio:#{account_id}")
  end

  @doc """
  Gets portfolio summary.
  """
  @spec get_summary(binary()) :: Portfolio.portfolio_summary()
  def get_summary(account_id) do
    GenServer.call(via_tuple(account_id), :get_summary)
  end

  # Server Callbacks

  @impl true
  def init(account_id) do
    Logger.info("Starting portfolio server for account #{account_id}")

    # Load initial state
    account = Portfolio.get_account(account_id)
    positions = load_positions(account_id)

    state = %{
      account_id: account_id,
      account: account,
      positions: positions,
      last_prices: %{},
      risk_alerts: [],
      subscribers: MapSet.new()
    }

    # Schedule periodic risk checks
    schedule_risk_check()

    {:ok, state}
  end

  @impl true
  def handle_call(:get_state, _from, state) do
    {:reply, state, state}
  end

  @impl true
  def handle_call({:update_position, instrument_id, side, quantity, price, currency}, _from, state) do
    case Portfolio.update_position(state.account_id, instrument_id, side, quantity, price, currency) do
      {:ok, position} ->
        new_positions = Map.put(state.positions, instrument_id, position)
        new_state = %{state | positions: new_positions}

        # Broadcast update
        broadcast_position_update(state.account_id, position)

        {:reply, {:ok, position}, new_state}

      {:ok, position, realized_pnl} ->
        new_positions = Map.put(state.positions, instrument_id, position)
        new_state = %{state | positions: new_positions}

        # Broadcast update with realized P&L
        broadcast_position_update(state.account_id, position, realized_pnl)

        {:reply, {:ok, position}, new_state}

      error ->
        {:reply, error, state}
    end
  end

  @impl true
  def handle_call({:update_balance, currency, amount}, _from, state) do
    case Portfolio.update_balance(state.account_id, currency, amount) do
      {:ok, balance} ->
        # Reload account to get updated balances
        updated_account = Portfolio.get_account(state.account_id)
        new_state = %{state | account: updated_account}

        # Broadcast balance update
        broadcast_balance_update(state.account_id, balance)

        {:reply, {:ok, balance}, new_state}

      error ->
        {:reply, error, state}
    end
  end

  @impl true
  def handle_call(:get_summary, _from, state) do
    summary = Portfolio.get_portfolio_summary(state.account_id, state.last_prices)
    {:reply, summary, state}
  end

  @impl true
  def handle_cast({:update_prices, price_map}, state) do
    # Update positions with new prices
    case Portfolio.update_positions_with_prices(state.account_id, price_map) do
      {:ok, updated_positions} ->
        new_positions = Enum.reduce(updated_positions, state.positions, fn pos, acc ->
          Map.put(acc, pos.instrument_id, pos)
        end)

        new_state = %{state |
          positions: new_positions,
          last_prices: Map.merge(state.last_prices, price_map)
        }

        # Broadcast price updates
        broadcast_price_update(state.account_id, price_map)

        # Check for risk alerts
        check_risk_alerts(new_state)

        {:noreply, new_state}

      {:error, reason} ->
        Logger.error("Failed to update positions with prices: #{inspect(reason)}")
        {:noreply, state}
    end
  end

  @impl true
  def handle_cast({:subscribe, pid}, state) do
    new_subscribers = MapSet.put(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  @impl true
  def handle_cast({:unsubscribe, pid}, state) do
    new_subscribers = MapSet.delete(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  @impl true
  def handle_info(:risk_check, state) do
    check_risk_alerts(state)
    schedule_risk_check()
    {:noreply, state}
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, pid, _reason}, state) do
    new_subscribers = MapSet.delete(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  # Private helper functions

  defp via_tuple(account_id) do
    {:via, Registry, {TtQuant.Portfolio.Registry, account_id}}
  end

  defp load_positions(account_id) do
    Portfolio.list_all_positions(account_id)
    |> Enum.reduce(%{}, fn position, acc ->
      Map.put(acc, position.instrument_id, position)
    end)
  end

  defp schedule_risk_check do
    Process.send_after(self(), :risk_check, 30_000)  # Check every 30 seconds
  end

  defp broadcast_position_update(account_id, position, realized_pnl \\ nil) do
    message = %{
      type: :position_update,
      account_id: account_id,
      position: position,
      realized_pnl: realized_pnl,
      timestamp: DateTime.utc_now()
    }

    PubSub.broadcast(TtQuant.PubSub, "portfolio:#{account_id}", message)
  end

  defp broadcast_balance_update(account_id, balance) do
    message = %{
      type: :balance_update,
      account_id: account_id,
      balance: balance,
      timestamp: DateTime.utc_now()
    }

    PubSub.broadcast(TtQuant.PubSub, "portfolio:#{account_id}", message)
  end

  defp broadcast_price_update(account_id, price_map) do
    message = %{
      type: :price_update,
      account_id: account_id,
      prices: price_map,
      timestamp: DateTime.utc_now()
    }

    PubSub.broadcast(TtQuant.PubSub, "portfolio:#{account_id}", message)
  end

  defp check_risk_alerts(state) do
    # Check margin levels if account has margin info
    case state.account && state.account.margin_info do
      nil -> :ok
      margin_info ->
        if TtQuant.Portfolio.MarginInfo.in_margin_call?(margin_info) do
          alert = %{
            type: :margin_call,
            account_id: state.account_id,
            margin_ratio: margin_info.margin_ratio,
            timestamp: DateTime.utc_now()
          }

          PubSub.broadcast(TtQuant.PubSub, "portfolio:#{state.account_id}", alert)
          Logger.warning("Margin call alert for account #{state.account_id}")
        end
    end

    # Check for large unrealized losses
    total_unrealized = state.positions
    |> Map.values()
    |> Enum.reduce(Decimal.new(0), fn pos, acc ->
      Decimal.add(acc, pos.unrealized_pnl)
    end)

    if Decimal.compare(total_unrealized, Decimal.new(-10000)) == :lt do
      alert = %{
        type: :large_loss,
        account_id: state.account_id,
        unrealized_pnl: total_unrealized,
        timestamp: DateTime.utc_now()
      }

      PubSub.broadcast(TtQuant.PubSub, "portfolio:#{state.account_id}", alert)
      Logger.warning("Large unrealized loss alert for account #{state.account_id}: #{total_unrealized}")
    end
  end
end
