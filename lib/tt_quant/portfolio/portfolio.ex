defmodule TtQuant.Portfolio.Portfolio do
  @moduledoc """
  Main portfolio management module that coordinates accounts, positions, and balances.

  Provides high-level operations for portfolio management and risk assessment.
  """

  alias TtQuant.Portfolio.{Account, Balance, Position, MarginInfo}
  alias TtQuant.Repo
  import Ecto.Query

  @type portfolio_summary :: %{
          total_value: Decimal.t(),
          total_pnl: Decimal.t(),
          unrealized_pnl: Decimal.t(),
          realized_pnl: Decimal.t(),
          margin_usage: Decimal.t() | nil,
          open_positions: non_neg_integer(),
          currencies: [String.t()]
        }

  @doc """
  Creates a new account with initial balance.
  """
  @spec create_account(map()) :: {:ok, Account.t()} | {:error, Ecto.Changeset.t()}
  def create_account(attrs) do
    %Account{}
    |> Account.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets account by ID with all related data preloaded.
  """
  @spec get_account(binary()) :: Account.t() | nil
  def get_account(account_id) do
    Account
    |> where([a], a.id == ^account_id)
    |> preload([:balances, :margin_info])
    |> Repo.one()
  end

  @doc """
  Gets all active accounts.
  """
  @spec list_active_accounts() :: [Account.t()]
  def list_active_accounts do
    Account
    |> where([a], a.is_active == true)
    |> preload([:balances, :margin_info])
    |> Repo.all()
  end

  @doc """
  Updates account balance for a specific currency.
  """
  @spec update_balance(binary(), String.t(), Decimal.t()) :: {:ok, Balance.t()} | {:error, any()}
  def update_balance(account_id, currency, amount) do
    case get_or_create_balance(account_id, currency) do
      {:ok, balance} ->
        updated_balance = Balance.add_available(balance, amount)

        balance
        |> Balance.changeset(%{
          total: updated_balance.total,
          available: updated_balance.available,
          locked: updated_balance.locked
        })
        |> Repo.update()

      error -> error
    end
  end

  @doc """
  Locks amount for trading (e.g., when placing an order).
  """
  @spec lock_balance(binary(), String.t(), Decimal.t()) :: {:ok, Balance.t()} | {:error, any()}
  def lock_balance(account_id, currency, amount) do
    case get_or_create_balance(account_id, currency) do
      {:ok, balance} ->
        case Balance.lock_amount(balance, amount) do
          {:ok, updated_balance} ->
            balance
            |> Balance.changeset(%{
              available: updated_balance.available,
              locked: updated_balance.locked
            })
            |> Repo.update()

          error -> error
        end

      error -> error
    end
  end

  @doc """
  Unlocks amount back to available balance.
  """
  @spec unlock_balance(binary(), String.t(), Decimal.t()) :: {:ok, Balance.t()} | {:error, any()}
  def unlock_balance(account_id, currency, amount) do
    case get_or_create_balance(account_id, currency) do
      {:ok, balance} ->
        case Balance.unlock_amount(balance, amount) do
          {:ok, updated_balance} ->
            balance
            |> Balance.changeset(%{
              available: updated_balance.available,
              locked: updated_balance.locked
            })
            |> Repo.update()

          error -> error
        end

      error -> error
    end
  end

  @doc """
  Creates or updates a position based on a trade.
  """
  @spec update_position(binary(), String.t(), :buy | :sell, Decimal.t(), Decimal.t(), String.t()) ::
    {:ok, Position.t()} | {:ok, Position.t(), Decimal.t()} | {:error, any()}
  def update_position(account_id, instrument_id, side, quantity, price, currency) do
    case get_or_create_position(account_id, instrument_id, currency) do
      {:ok, position} ->
        case Position.update_with_trade(position, side, quantity, price) do
          {:ok, updated_position} ->
            result = position
            |> Position.changeset(%{
              side: updated_position.side,
              quantity: updated_position.quantity,
              average_price: updated_position.average_price,
              current_price: updated_position.current_price,
              unrealized_pnl: updated_position.unrealized_pnl,
              realized_pnl: updated_position.realized_pnl,
              opened_at: updated_position.opened_at
            })
            |> Repo.update()

            case result do
              {:ok, pos} -> {:ok, pos}
              error -> error
            end

          {:ok, updated_position, realized_pnl} ->
            result = position
            |> Position.changeset(%{
              side: updated_position.side,
              quantity: updated_position.quantity,
              average_price: updated_position.average_price,
              current_price: updated_position.current_price,
              unrealized_pnl: updated_position.unrealized_pnl,
              realized_pnl: updated_position.realized_pnl,
              opened_at: updated_position.opened_at
            })
            |> Repo.update()

            case result do
              {:ok, pos} -> {:ok, pos, realized_pnl}
              error -> error
            end
        end

      error -> error
    end
  end

  @doc """
  Updates all positions with current market prices.
  """
  @spec update_positions_with_prices(binary(), %{String.t() => Decimal.t()}) :: {:ok, [Position.t()]} | {:error, any()}
  def update_positions_with_prices(account_id, price_map) do
    positions = list_open_positions(account_id)

    updated_positions = Enum.map(positions, fn position ->
      case Map.get(price_map, position.instrument_id) do
        nil -> position
        current_price ->
          updated = Position.update_unrealized_pnl(position, current_price)

          updated
          |> Position.changeset(%{
            current_price: updated.current_price,
            unrealized_pnl: updated.unrealized_pnl
          })
          |> Repo.update!()
      end
    end)

    {:ok, updated_positions}
  end

  @doc """
  Gets portfolio summary for an account.
  """
  @spec get_portfolio_summary(binary(), %{String.t() => Decimal.t()}) :: portfolio_summary()
  def get_portfolio_summary(account_id, exchange_rates \\ %{}) do
    account = get_account(account_id)
    positions = list_all_positions(account_id)

    total_value = Account.calculate_total_value(account, exchange_rates)

    {total_pnl, unrealized_pnl, realized_pnl} = calculate_total_pnl(positions)

    margin_usage = case account.margin_info do
      nil -> nil
      margin_info -> margin_info.margin_ratio
    end

    open_positions = Enum.count(positions, &Position.is_open?/1)
    currencies = Enum.map(account.balances, & &1.currency) |> Enum.uniq()

    %{
      total_value: total_value,
      total_pnl: total_pnl,
      unrealized_pnl: unrealized_pnl,
      realized_pnl: realized_pnl,
      margin_usage: margin_usage,
      open_positions: open_positions,
      currencies: currencies
    }
  end

  @doc """
  Lists all open positions for an account.
  """
  @spec list_open_positions(binary()) :: [Position.t()]
  def list_open_positions(account_id) do
    Position
    |> where([p], p.account_id == ^account_id and p.side != :flat)
    |> Repo.all()
  end

  @doc """
  Lists all positions (including closed) for an account.
  """
  @spec list_all_positions(binary()) :: [Position.t()]
  def list_all_positions(account_id) do
    Position
    |> where([p], p.account_id == ^account_id)
    |> Repo.all()
  end

  # Private helper functions
  defp get_or_create_balance(account_id, currency) do
    case Repo.get_by(Balance, account_id: account_id, currency: currency) do
      nil ->
        %Balance{}
        |> Balance.changeset(%{
          account_id: account_id,
          currency: currency,
          total: Decimal.new(0),
          available: Decimal.new(0),
          locked: Decimal.new(0)
        })
        |> Repo.insert()

      balance -> {:ok, balance}
    end
  end

  defp get_or_create_position(account_id, instrument_id, currency) do
    case Repo.get_by(Position, account_id: account_id, instrument_id: instrument_id) do
      nil ->
        %Position{}
        |> Position.changeset(%{
          account_id: account_id,
          instrument_id: instrument_id,
          side: :flat,
          quantity: Decimal.new(0),
          average_price: Decimal.new(0),
          currency: currency,
          unrealized_pnl: Decimal.new(0),
          realized_pnl: Decimal.new(0)
        })
        |> Repo.insert()

      position -> {:ok, position}
    end
  end

  defp calculate_total_pnl(positions) do
    Enum.reduce(positions, {Decimal.new(0), Decimal.new(0), Decimal.new(0)},
      fn position, {total_acc, unrealized_acc, realized_acc} ->
        total_pnl = Position.total_pnl(position)
        {
          Decimal.add(total_acc, total_pnl),
          Decimal.add(unrealized_acc, position.unrealized_pnl),
          Decimal.add(realized_acc, position.realized_pnl)
        }
      end)
  end
end
