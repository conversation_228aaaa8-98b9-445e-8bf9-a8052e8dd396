defmodule TtQuant.Core do
  @moduledoc """
  NIF module for TtQuant core functionality implemented in Rust.

  This module provides high-performance computational functions
  for quantitative trading operations.
  """

  use <PERSON><PERSON><PERSON>, otp_app: :tt_quant, crate: "tt_quant_core"

  @doc """
  Adds two integers together.

  This is a sample NIF function to verify the Rust integration is working.

  ## Examples

      iex> TtQuant.Core.add(1, 2)
      3

      iex> TtQuant.Core.add(-5, 10)
      5
  """
  # When your NIF is loaded, it will override this function.
  def add(_a, _b), do: :erlang.nif_error(:nif_not_loaded)

  @doc """
  Creates a price with the given value and precision.
  """
  def create_price(_value, _precision), do: :erlang.nif_error(:nif_not_loaded)

  @doc """
  Adds two prices together.
  """
  def add_prices(_price1_binary, _price2_binary), do: :erlang.nif_error(:nif_not_loaded)

  @doc """
  Creates a quantity with the given value and precision.
  """
  def create_quantity(_value, _precision), do: :erlang.nif_error(:nif_not_loaded)

  @doc """
  Creates a market order.
  """
  def create_market_order(_symbol, _venue, _side, _quantity_binary), do: :erlang.nif_error(:nif_not_loaded)

  @doc """
  Creates a limit order.
  """
  def create_limit_order(_symbol, _venue, _side, _quantity_binary, _price_binary), do: :erlang.nif_error(:nif_not_loaded)

  @doc """
  Creates a money object with the given amount and currency.
  """
  def create_money(_amount, _currency_code), do: :erlang.nif_error(:nif_not_loaded)
end
