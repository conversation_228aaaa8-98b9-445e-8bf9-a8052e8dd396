defmodule TtQuant.Accounts.Session do
  @moduledoc """
  Schema for user sessions and WebSocket connections.
  
  Tracks active sessions for authentication and connection management.
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Portfolio.Account

  @type t :: %__MODULE__{
          id: binary(),
          account_id: binary() | nil,
          session_token: String.t(),
          session_type: String.t(),
          ip_address: String.t() | nil,
          user_agent: String.t() | nil,
          is_active: boolean(),
          expires_at: DateTime.t(),
          last_activity_at: DateTime.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @session_types ["web", "api", "websocket", "mobile"]

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "sessions" do
    field :session_token, :string
    field :session_type, :string, default: "web"
    field :ip_address, :string
    field :user_agent, :string
    field :is_active, :boolean, default: true
    field :expires_at, :utc_datetime
    field :last_activity_at, :utc_datetime

    belongs_to :account, Account

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for session creation.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(session, attrs) do
    session
    |> cast(attrs, [
      :account_id, :session_token, :session_type, :ip_address, 
      :user_agent, :is_active, :expires_at, :last_activity_at
    ])
    |> validate_required([:session_token, :session_type, :expires_at, :last_activity_at])
    |> validate_inclusion(:session_type, @session_types)
    |> validate_length(:session_token, min: 32)
    |> unique_constraint(:session_token)
    |> foreign_key_constraint(:account_id)
    |> validate_expiry_date()
  end

  @doc """
  Creates a new session.
  """
  @spec create_session(binary() | nil, String.t(), map()) :: Ecto.Changeset.t()
  def create_session(account_id, session_type, opts \\ %{}) do
    now = DateTime.utc_now()
    expires_at = DateTime.add(now, session_duration(session_type), :second)
    
    attrs = %{
      account_id: account_id,
      session_token: generate_session_token(),
      session_type: session_type,
      ip_address: Map.get(opts, :ip_address),
      user_agent: Map.get(opts, :user_agent),
      is_active: true,
      expires_at: expires_at,
      last_activity_at: now
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Updates session activity.
  """
  @spec update_activity(t()) :: Ecto.Changeset.t()
  def update_activity(%__MODULE__{} = session) do
    changeset(session, %{last_activity_at: DateTime.utc_now()})
  end

  @doc """
  Deactivates a session.
  """
  @spec deactivate(t()) :: Ecto.Changeset.t()
  def deactivate(%__MODULE__{} = session) do
    changeset(session, %{is_active: false})
  end

  @doc """
  Checks if session is expired.
  """
  @spec expired?(t()) :: boolean()
  def expired?(%__MODULE__{expires_at: expires_at}) do
    DateTime.compare(DateTime.utc_now(), expires_at) == :gt
  end

  @doc """
  Checks if session is active and not expired.
  """
  @spec active?(t()) :: boolean()
  def active?(%__MODULE__{is_active: true} = session) do
    not expired?(session)
  end
  def active?(_), do: false

  @doc """
  Gets all valid session types.
  """
  @spec session_types() :: [String.t()]
  def session_types, do: @session_types

  defp generate_session_token do
    :crypto.strong_rand_bytes(32) |> Base.url_encode64(padding: false)
  end

  defp session_duration("web"), do: 24 * 60 * 60  # 24 hours
  defp session_duration("api"), do: 7 * 24 * 60 * 60  # 7 days
  defp session_duration("websocket"), do: 12 * 60 * 60  # 12 hours
  defp session_duration("mobile"), do: 30 * 24 * 60 * 60  # 30 days
  defp session_duration(_), do: 24 * 60 * 60  # default 24 hours

  defp validate_expiry_date(changeset) do
    expires_at = get_field(changeset, :expires_at)
    
    if expires_at && DateTime.compare(expires_at, DateTime.utc_now()) != :gt do
      add_error(changeset, :expires_at, "must be in the future")
    else
      changeset
    end
  end
end
