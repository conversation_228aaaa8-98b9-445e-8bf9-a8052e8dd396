defmodule TtQuant.Portfolio do
  @moduledoc """
  The Portfolio context.

  This module provides the main API for portfolio management operations,
  including account management, position tracking, and balance updates.
  """

  alias TtQuant.Portfolio.{Account, Balance, Position, MarginInfo, Portfolio, Server, Supervisor}
  alias TtQuant.Repo
  import Ecto.Query

  # Account Management

  @doc """
  Creates a new trading account.
  """
  @spec create_account(map()) :: {:ok, Account.t()} | {:error, Ecto.Changeset.t()}
  def create_account(attrs) do
    case Portfolio.create_account(attrs) do
      {:ok, account} ->
        # Start portfolio server for the new account
        Supervisor.start_portfolio_server(account.id)
        # Return account with preloaded associations
        {:ok, get_account(account.id)}

      error -> error
    end
  end

  @doc """
  Gets an account by ID.
  """
  @spec get_account(binary()) :: Account.t() | nil
  def get_account(account_id) do
    Portfolio.get_account(account_id)
  end

  @doc """
  Gets an account by ID, raising if not found.
  """
  @spec get_account!(binary()) :: Account.t()
  def get_account!(account_id) do
    case get_account(account_id) do
      nil -> raise Ecto.NoResultsError, queryable: Account
      account -> account
    end
  end

  @doc """
  Lists all active accounts.
  """
  @spec list_accounts() :: [Account.t()]
  def list_accounts do
    Portfolio.list_active_accounts()
  end

  @doc """
  Updates an account.
  """
  @spec update_account(Account.t(), map()) :: {:ok, Account.t()} | {:error, Ecto.Changeset.t()}
  def update_account(account, attrs) do
    account
    |> Account.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deactivates an account.
  """
  @spec deactivate_account(binary()) :: {:ok, Account.t()} | {:error, Ecto.Changeset.t()}
  def deactivate_account(account_id) do
    account = get_account!(account_id)

    # Stop portfolio server
    Supervisor.stop_portfolio_server(account_id)

    # Deactivate account
    update_account(account, %{is_active: false})
  end

  # Balance Management

  @doc """
  Gets balance for a specific currency in an account.
  """
  @spec get_balance(binary(), String.t()) :: Decimal.t()
  def get_balance(account_id, currency) do
    account = get_account(account_id)
    if account, do: Account.get_balance(account, currency), else: Decimal.new(0)
  end

  @doc """
  Gets available balance for a specific currency in an account.
  """
  @spec get_available_balance(binary(), String.t()) :: Decimal.t()
  def get_available_balance(account_id, currency) do
    account = get_account(account_id)
    if account, do: Account.get_available_balance(account, currency), else: Decimal.new(0)
  end

  @doc """
  Updates balance for an account and currency.
  """
  @spec update_balance(binary(), String.t(), Decimal.t()) :: {:ok, Balance.t()} | {:error, any()}
  def update_balance(account_id, currency, amount) do
    # Try to use server first, fallback to direct database update
    case Supervisor.ensure_portfolio_server(account_id) do
      {:ok, _pid} ->
        Server.update_balance(account_id, currency, amount)
      {:error, _} ->
        # Fallback to direct database update
        Portfolio.update_balance(account_id, currency, amount)
    end
  end

  @doc """
  Locks balance for trading.
  """
  @spec lock_balance(binary(), String.t(), Decimal.t()) :: {:ok, Balance.t()} | {:error, any()}
  def lock_balance(account_id, currency, amount) do
    Portfolio.lock_balance(account_id, currency, amount)
  end

  @doc """
  Unlocks balance back to available.
  """
  @spec unlock_balance(binary(), String.t(), Decimal.t()) :: {:ok, Balance.t()} | {:error, any()}
  def unlock_balance(account_id, currency, amount) do
    Portfolio.unlock_balance(account_id, currency, amount)
  end

  @doc """
  Checks if account has sufficient balance for an amount.
  """
  @spec has_sufficient_balance?(binary(), String.t(), Decimal.t()) :: boolean()
  def has_sufficient_balance?(account_id, currency, amount) do
    account = get_account(account_id)
    if account, do: Account.has_sufficient_balance?(account, amount, currency), else: false
  end

  @doc """
  Gets all balances for an account.
  """
  @spec get_balances(binary()) :: [Balance.t()]
  def get_balances(account_id) do
    account = get_account(account_id)
    if account, do: account.balances, else: []
  end

  @doc """
  Gets balance struct for a specific currency in an account.
  """
  @spec get_balance_struct(binary(), String.t()) :: Balance.t() | nil
  def get_balance_struct(account_id, currency) do
    account = get_account(account_id)
    if account do
      Enum.find(account.balances, &(&1.currency == currency))
    else
      nil
    end
  end

  # Position Management

  @doc """
  Updates position with a new trade.
  """
  @spec update_position(binary(), String.t(), :buy | :sell, Decimal.t(), Decimal.t(), String.t()) ::
    {:ok, Position.t()} | {:error, any()}
  def update_position(account_id, instrument_id, side, quantity, price, currency) do
    # Try to use server first, fallback to direct database update
    case Supervisor.ensure_portfolio_server(account_id) do
      {:ok, _pid} ->
        Server.update_position(account_id, instrument_id, side, quantity, price, currency)
      {:error, _} ->
        # Fallback to direct database update
        Portfolio.update_position(account_id, instrument_id, side, quantity, price, currency)
    end
  end

  @doc """
  Gets all open positions for an account.
  """
  @spec list_open_positions(binary()) :: [Position.t()]
  def list_open_positions(account_id) do
    Portfolio.list_open_positions(account_id)
  end

  @doc """
  Gets all positions (including closed) for an account.
  """
  @spec list_all_positions(binary()) :: [Position.t()]
  def list_all_positions(account_id) do
    Portfolio.list_all_positions(account_id)
  end

  @doc """
  Gets a specific position by account and instrument.
  """
  @spec get_position(binary(), String.t()) :: Position.t() | nil
  def get_position(account_id, instrument_id) do
    Position
    |> where([p], p.account_id == ^account_id and p.instrument_id == ^instrument_id)
    |> Repo.one()
  end

  # Market Data Updates

  @doc """
  Updates positions with current market prices.
  """
  @spec update_market_prices(binary(), %{String.t() => Decimal.t()}) :: :ok
  def update_market_prices(account_id, price_map) do
    # Ensure portfolio server is running
    Supervisor.ensure_portfolio_server(account_id)

    # Update prices through server
    Server.update_prices(account_id, price_map)
  end

  # Portfolio Summary and Analytics

  @doc """
  Gets portfolio summary for an account.
  """
  @spec get_portfolio_summary(binary()) :: Portfolio.portfolio_summary()
  def get_portfolio_summary(account_id) do
    # Try to use server first, fallback to direct calculation
    case Supervisor.ensure_portfolio_server(account_id) do
      {:ok, _pid} ->
        Server.get_summary(account_id)
      {:error, _} ->
        # Fallback to direct calculation
        Portfolio.get_portfolio_summary(account_id)
    end
  end

  @doc """
  Gets portfolio performance metrics.
  """
  @spec get_performance_metrics(binary(), DateTime.t(), DateTime.t()) :: map()
  def get_performance_metrics(account_id, start_date, end_date) do
    positions = Position
    |> where([p], p.account_id == ^account_id)
    |> where([p], p.updated_at >= ^start_date and p.updated_at <= ^end_date)
    |> Repo.all()

    total_realized_pnl = positions
    |> Enum.reduce(Decimal.new(0), fn pos, acc ->
      Decimal.add(acc, pos.realized_pnl)
    end)

    total_trades = positions
    |> Enum.count(fn pos -> Decimal.compare(pos.realized_pnl, Decimal.new(0)) != :eq end)

    winning_trades = positions
    |> Enum.count(fn pos -> Decimal.compare(pos.realized_pnl, Decimal.new(0)) == :gt end)

    win_rate = if total_trades > 0 do
      Decimal.div(Decimal.new(winning_trades), Decimal.new(total_trades))
    else
      Decimal.new(0)
    end

    %{
      total_realized_pnl: total_realized_pnl,
      total_trades: total_trades,
      winning_trades: winning_trades,
      win_rate: win_rate,
      period_start: start_date,
      period_end: end_date
    }
  end

  # Subscription Management

  @doc """
  Subscribes to portfolio updates for an account.
  """
  @spec subscribe_to_updates(binary()) :: :ok
  def subscribe_to_updates(account_id) do
    # Ensure portfolio server is running
    Supervisor.ensure_portfolio_server(account_id)

    Server.subscribe(account_id)
  end

  @doc """
  Unsubscribes from portfolio updates for an account.
  """
  @spec unsubscribe_from_updates(binary()) :: :ok
  def unsubscribe_from_updates(account_id) do
    Server.unsubscribe(account_id)
  end

  # Server Management

  @doc """
  Starts portfolio server for an account if not already running.
  """
  @spec ensure_portfolio_server(binary()) :: {:ok, pid()} | {:error, any()}
  def ensure_portfolio_server(account_id) do
    Supervisor.ensure_portfolio_server(account_id)
  end

  @doc """
  Lists all running portfolio servers.
  """
  @spec list_running_servers() :: [binary()]
  def list_running_servers do
    Supervisor.list_portfolio_servers()
  end

  @doc """
  Checks if portfolio server is running for an account.
  """
  @spec server_running?(binary()) :: boolean()
  def server_running?(account_id) do
    Supervisor.portfolio_server_running?(account_id)
  end
end
