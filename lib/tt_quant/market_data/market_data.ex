defmodule TtQuant.MarketData.MarketData do
  @moduledoc """
  Schema for market data storage.
  
  Stores various types of market data including ticks, quotes, and bars
  for different instruments and venues.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @type t :: %__MODULE__{
          id: binary(),
          instrument_id: String.t(),
          data_type: String.t(),
          bid_price: Decimal.t() | nil,
          ask_price: Decimal.t() | nil,
          bid_quantity: Decimal.t() | nil,
          ask_quantity: Decimal.t() | nil,
          last_price: Decimal.t() | nil,
          last_quantity: Decimal.t() | nil,
          volume: Decimal.t() | nil,
          timestamp: DateTime.t(),
          venue: String.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @data_types ["tick", "quote", "bar", "trade", "book_update"]

  @primary_key {:id, :binary_id, autogenerate: true}

  schema "market_data" do
    field :instrument_id, :string
    field :data_type, :string
    field :bid_price, :decimal
    field :ask_price, :decimal
    field :bid_quantity, :decimal
    field :ask_quantity, :decimal
    field :last_price, :decimal
    field :last_quantity, :decimal
    field :volume, :decimal
    field :timestamp, :utc_datetime
    field :venue, :string

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for market data creation.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(market_data, attrs) do
    market_data
    |> cast(attrs, [
      :instrument_id, :data_type, :bid_price, :ask_price, 
      :bid_quantity, :ask_quantity, :last_price, :last_quantity,
      :volume, :timestamp, :venue
    ])
    |> validate_required([:instrument_id, :data_type, :timestamp, :venue])
    |> validate_inclusion(:data_type, @data_types)
    |> validate_prices()
    |> validate_quantities()
  end

  @doc """
  Creates a tick data entry.
  """
  @spec tick(String.t(), Decimal.t(), Decimal.t(), String.t(), DateTime.t()) :: Ecto.Changeset.t()
  def tick(instrument_id, price, quantity, venue, timestamp \\ nil) do
    timestamp = timestamp || DateTime.utc_now()
    
    attrs = %{
      instrument_id: instrument_id,
      data_type: "tick",
      last_price: price,
      last_quantity: quantity,
      venue: venue,
      timestamp: timestamp
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Creates a quote data entry.
  """
  @spec quote(String.t(), Decimal.t(), Decimal.t(), Decimal.t(), Decimal.t(), String.t(), DateTime.t()) :: Ecto.Changeset.t()
  def quote(instrument_id, bid_price, bid_qty, ask_price, ask_qty, venue, timestamp \\ nil) do
    timestamp = timestamp || DateTime.utc_now()
    
    attrs = %{
      instrument_id: instrument_id,
      data_type: "quote",
      bid_price: bid_price,
      bid_quantity: bid_qty,
      ask_price: ask_price,
      ask_quantity: ask_qty,
      venue: venue,
      timestamp: timestamp
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Creates a trade data entry.
  """
  @spec trade(String.t(), Decimal.t(), Decimal.t(), Decimal.t(), String.t(), DateTime.t()) :: Ecto.Changeset.t()
  def trade(instrument_id, price, quantity, volume, venue, timestamp \\ nil) do
    timestamp = timestamp || DateTime.utc_now()
    
    attrs = %{
      instrument_id: instrument_id,
      data_type: "trade",
      last_price: price,
      last_quantity: quantity,
      volume: volume,
      venue: venue,
      timestamp: timestamp
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Gets all valid data types.
  """
  @spec data_types() :: [String.t()]
  def data_types, do: @data_types

  @doc """
  Checks if market data is a quote.
  """
  @spec quote?(t()) :: boolean()
  def quote?(%__MODULE__{data_type: "quote"}), do: true
  def quote?(_), do: false

  @doc """
  Checks if market data is a tick.
  """
  @spec tick?(t()) :: boolean()
  def tick?(%__MODULE__{data_type: "tick"}), do: true
  def tick?(_), do: false

  @doc """
  Checks if market data is a trade.
  """
  @spec trade?(t()) :: boolean()
  def trade?(%__MODULE__{data_type: "trade"}), do: true
  def trade?(_), do: false

  defp validate_prices(changeset) do
    changeset
    |> validate_number(:bid_price, greater_than: 0)
    |> validate_number(:ask_price, greater_than: 0)
    |> validate_number(:last_price, greater_than: 0)
    |> validate_bid_ask_spread()
  end

  defp validate_quantities(changeset) do
    changeset
    |> validate_number(:bid_quantity, greater_than: 0)
    |> validate_number(:ask_quantity, greater_than: 0)
    |> validate_number(:last_quantity, greater_than: 0)
    |> validate_number(:volume, greater_than_or_equal_to: 0)
  end

  defp validate_bid_ask_spread(changeset) do
    bid_price = get_field(changeset, :bid_price)
    ask_price = get_field(changeset, :ask_price)

    if bid_price && ask_price && Decimal.compare(bid_price, ask_price) != :lt do
      add_error(changeset, :ask_price, "must be greater than bid price")
    else
      changeset
    end
  end
end
