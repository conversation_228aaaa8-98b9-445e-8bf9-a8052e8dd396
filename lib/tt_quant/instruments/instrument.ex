defmodule TtQuant.Instruments.Instrument do
  @moduledoc """
  Instrument definition and management for trading instruments.

  Represents financial instruments with their trading rules, specifications,
  and market data requirements.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @type t :: %__MODULE__{
          id: binary(),
          symbol: String.t(),
          name: String.t(),
          instrument_type: instrument_type(),
          base_currency: String.t(),
          quote_currency: String.t(),
          tick_size: Decimal.t(),
          lot_size: Decimal.t(),
          min_quantity: Decimal.t(),
          max_quantity: Decimal.t(),
          price_precision: non_neg_integer(),
          quantity_precision: non_neg_integer(),
          maker_fee: Decimal.t(),
          taker_fee: Decimal.t(),
          venue: String.t(),
          is_active: boolean(),
          trading_hours: map(),
          contract_size: Decimal.t() | nil,
          expiry_date: Date.t() | nil,
          strike_price: Decimal.t() | nil,
          option_type: option_type() | nil,
          rust_instrument_data: binary() | nil,
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @type instrument_type :: :spot | :futures | :options | :swap | :cfd
  @type option_type :: :call | :put

  @primary_key {:id, :binary_id, autogenerate: true}

  schema "instruments" do
    field :symbol, :string
    field :name, :string
    field :instrument_type, Ecto.Enum, values: [:spot, :futures, :options, :swap, :cfd]
    field :base_currency, :string
    field :quote_currency, :string
    field :tick_size, :decimal
    field :lot_size, :decimal
    field :min_quantity, :decimal
    field :max_quantity, :decimal
    field :price_precision, :integer
    field :quantity_precision, :integer
    field :maker_fee, :decimal, default: 0
    field :taker_fee, :decimal, default: 0
    field :venue, :string
    field :is_active, :boolean, default: true
    field :trading_hours, :map, default: %{}
    field :contract_size, :decimal
    field :expiry_date, :date
    field :strike_price, :decimal
    field :option_type, Ecto.Enum, values: [:call, :put]
    field :rust_instrument_data, :binary

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for instrument creation and updates.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(instrument, attrs) do
    instrument
    |> cast(attrs, [:symbol, :name, :instrument_type, :base_currency, :quote_currency,
                    :tick_size, :lot_size, :min_quantity, :max_quantity, :price_precision,
                    :quantity_precision, :maker_fee, :taker_fee, :venue, :is_active,
                    :trading_hours, :contract_size, :expiry_date, :strike_price,
                    :option_type, :rust_instrument_data])
    |> validate_required([:symbol, :name, :instrument_type, :base_currency, :quote_currency,
                         :tick_size, :lot_size, :min_quantity, :max_quantity, :price_precision,
                         :quantity_precision, :venue])
    |> validate_length(:symbol, min: 1, max: 50)
    |> validate_length(:name, min: 1, max: 200)
    |> validate_number(:tick_size, greater_than: 0)
    |> validate_number(:lot_size, greater_than: 0)
    |> validate_number(:min_quantity, greater_than: 0)
    |> validate_number(:max_quantity, greater_than: 0)
    |> validate_number(:price_precision, greater_than_or_equal_to: 0, less_than_or_equal_to: 18)
    |> validate_number(:quantity_precision, greater_than_or_equal_to: 0, less_than_or_equal_to: 18)
    |> validate_number(:maker_fee, greater_than_or_equal_to: 0)
    |> validate_number(:taker_fee, greater_than_or_equal_to: 0)
    |> validate_quantity_constraints()
    |> validate_options_fields()
    |> validate_futures_fields()
    |> unique_constraint([:symbol, :venue])
  end

  @doc """
  Creates a spot instrument.
  """
  @spec create_spot(String.t(), String.t(), String.t(), String.t(), map()) :: Ecto.Changeset.t()
  def create_spot(symbol, name, base_currency, quote_currency, opts \\ %{}) do
    default_attrs = %{
      symbol: symbol,
      name: name,
      instrument_type: :spot,
      base_currency: base_currency,
      quote_currency: quote_currency,
      tick_size: Decimal.new("0.01"),
      lot_size: Decimal.new("1.0"),
      min_quantity: Decimal.new("0.001"),
      max_quantity: Decimal.new("1000000"),
      price_precision: 2,
      quantity_precision: 8,
      venue: "default"
    }

    attrs = Map.merge(default_attrs, opts)
    changeset(%__MODULE__{}, attrs)
  end

  @doc """
  Creates a futures instrument.
  """
  @spec create_futures(String.t(), String.t(), String.t(), String.t(), Date.t(), map()) :: Ecto.Changeset.t()
  def create_futures(symbol, name, base_currency, quote_currency, expiry_date, opts \\ %{}) do
    default_attrs = %{
      symbol: symbol,
      name: name,
      instrument_type: :futures,
      base_currency: base_currency,
      quote_currency: quote_currency,
      expiry_date: expiry_date,
      tick_size: Decimal.new("0.01"),
      lot_size: Decimal.new("1.0"),
      min_quantity: Decimal.new("1.0"),
      max_quantity: Decimal.new("100000"),
      price_precision: 2,
      quantity_precision: 0,
      contract_size: Decimal.new("1.0"),
      venue: "default"
    }

    attrs = Map.merge(default_attrs, opts)
    changeset(%__MODULE__{}, attrs)
  end

  @doc """
  Validates if a price conforms to tick size.
  """
  @spec valid_price?(t(), Decimal.t()) :: boolean()
  def valid_price?(%__MODULE__{tick_size: tick_size}, price) do
    remainder = Decimal.rem(price, tick_size)
    Decimal.equal?(remainder, Decimal.new(0))
  end

  @doc """
  Validates if a quantity conforms to lot size.
  """
  @spec valid_quantity?(t(), Decimal.t()) :: boolean()
  def valid_quantity?(%__MODULE__{lot_size: lot_size, min_quantity: min_qty, max_quantity: max_qty}, quantity) do
    # Check lot size conformity
    remainder = Decimal.rem(quantity, lot_size)
    lot_valid = Decimal.equal?(remainder, Decimal.new(0))

    # Check min/max constraints
    min_valid = Decimal.compare(quantity, min_qty) != :lt
    max_valid = Decimal.compare(quantity, max_qty) != :gt

    lot_valid && min_valid && max_valid
  end

  @doc """
  Rounds price to nearest valid tick.
  """
  @spec round_price(t(), Decimal.t()) :: Decimal.t()
  def round_price(%__MODULE__{tick_size: tick_size}, price) do
    ticks = Decimal.div(price, tick_size)
    rounded_ticks = Decimal.round(ticks, 0)
    Decimal.mult(rounded_ticks, tick_size)
  end

  @doc """
  Rounds quantity to nearest valid lot.
  """
  @spec round_quantity(t(), Decimal.t()) :: Decimal.t()
  def round_quantity(%__MODULE__{lot_size: lot_size}, quantity) do
    lots = Decimal.div(quantity, lot_size)
    rounded_lots = Decimal.round(lots, 0)
    Decimal.mult(rounded_lots, lot_size)
  end

  @doc """
  Calculates trading fee for a given value and side.
  """
  @spec calculate_fee(t(), Decimal.t(), :maker | :taker) :: Decimal.t()
  def calculate_fee(%__MODULE__{maker_fee: maker_fee}, value, :maker) do
    Decimal.mult(value, maker_fee)
  end

  def calculate_fee(%__MODULE__{taker_fee: taker_fee}, value, :taker) do
    Decimal.mult(value, taker_fee)
  end

  @doc """
  Checks if instrument is currently tradeable.
  """
  @spec tradeable?(t()) :: boolean()
  def tradeable?(%__MODULE__{is_active: false}), do: false
  def tradeable?(%__MODULE__{expiry_date: expiry_date}) when not is_nil(expiry_date) do
    Date.compare(Date.utc_today(), expiry_date) == :lt
  end
  def tradeable?(%__MODULE__{}), do: true

  @doc """
  Gets the full instrument identifier (symbol@venue).
  """
  @spec full_symbol(t()) :: String.t()
  def full_symbol(%__MODULE__{symbol: symbol, venue: venue}) do
    "#{symbol}@#{venue}"
  end

  @doc """
  Checks if instrument is within trading hours.
  """
  @spec in_trading_hours?(t(), DateTime.t()) :: boolean()
  def in_trading_hours?(%__MODULE__{trading_hours: hours}, _datetime) when map_size(hours) == 0 do
    true  # No trading hours restriction
  end

  def in_trading_hours?(%__MODULE__{trading_hours: hours}, datetime) do
    day_of_week = Date.day_of_week(DateTime.to_date(datetime))
    day_name = day_name(day_of_week)

    case Map.get(hours, day_name) do
      nil -> false  # No trading on this day
      day_hours -> within_day_hours?(datetime, day_hours)
    end
  end

  # Private helper functions
  defp validate_quantity_constraints(changeset) do
    min_qty = get_field(changeset, :min_quantity)
    max_qty = get_field(changeset, :max_quantity)

    if min_qty && max_qty && Decimal.compare(min_qty, max_qty) == :gt do
      add_error(changeset, :min_quantity, "must be less than or equal to max_quantity")
    else
      changeset
    end
  end

  defp validate_options_fields(changeset) do
    instrument_type = get_field(changeset, :instrument_type)
    strike_price = get_field(changeset, :strike_price)
    option_type = get_field(changeset, :option_type)

    if instrument_type == :options do
      changeset = if is_nil(strike_price) do
        add_error(changeset, :strike_price, "is required for options")
      else
        changeset
      end

      if is_nil(option_type) do
        add_error(changeset, :option_type, "is required for options")
      else
        changeset
      end
    else
      changeset
    end
  end

  defp validate_futures_fields(changeset) do
    instrument_type = get_field(changeset, :instrument_type)
    expiry_date = get_field(changeset, :expiry_date)

    if instrument_type == :futures && is_nil(expiry_date) do
      add_error(changeset, :expiry_date, "is required for futures")
    else
      changeset
    end
  end

  defp day_name(1), do: "monday"
  defp day_name(2), do: "tuesday"
  defp day_name(3), do: "wednesday"
  defp day_name(4), do: "thursday"
  defp day_name(5), do: "friday"
  defp day_name(6), do: "saturday"
  defp day_name(7), do: "sunday"

  defp within_day_hours?(datetime, day_hours) do
    time = DateTime.to_time(datetime)

    case day_hours do
      %{"open" => open_str, "close" => close_str} ->
        {:ok, open_time} = Time.from_iso8601(open_str)
        {:ok, close_time} = Time.from_iso8601(close_str)

        Time.compare(time, open_time) != :lt && Time.compare(time, close_time) != :gt

      _ -> true  # Invalid format, assume always open
    end
  end
end
