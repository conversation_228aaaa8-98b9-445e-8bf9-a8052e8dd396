defmodule TtQuant.Instruments.Registry do
  @moduledoc """
  GenServer for managing instrument registry and caching.

  Provides fast lookup and caching of instrument definitions,
  with automatic refresh and validation capabilities.
  """

  use GenServer
  require Logger
  alias TtQuant.Instruments.Instrument
  alias TtQuant.Repo
  alias Phoenix.PubSub
  import Ecto.Query

  @type state :: %{
          instruments: %{String.t() => Instrument.t()},
          symbols_by_venue: %{String.t() => MapSet.t()},
          last_refresh: DateTime.t(),
          subscribers: MapSet.t()
        }

  # Client API

  @doc """
  Starts the instrument registry.
  """
  @spec start_link(any()) :: GenServer.on_start()
  def start_link(_opts) do
    GenServer.start_link(__MODULE__, [], name: __MODULE__)
  end

  @doc """
  Registers a new instrument.
  """
  @spec register_instrument(Instrument.t()) :: {:ok, Instrument.t()} | {:error, any()}
  def register_instrument(%Instrument{} = instrument) do
    GenServer.call(__MODULE__, {:register_instrument, instrument})
  end

  @doc """
  Gets an instrument by symbol and venue.
  """
  @spec get_instrument(String.t(), String.t()) :: Instrument.t() | nil
  def get_instrument(symbol, venue \\ "default") do
    GenServer.call(__MODULE__, {:get_instrument, symbol, venue})
  end

  @doc """
  Gets an instrument by full symbol (symbol@venue).
  """
  @spec get_instrument_by_full_symbol(String.t()) :: Instrument.t() | nil
  def get_instrument_by_full_symbol(full_symbol) do
    case String.split(full_symbol, "@") do
      [symbol, venue] -> get_instrument(symbol, venue)
      [symbol] -> get_instrument(symbol, "default")
      _ -> nil
    end
  end

  @doc """
  Lists all instruments for a venue.
  """
  @spec list_instruments(String.t()) :: [Instrument.t()]
  def list_instruments(venue \\ "default") do
    GenServer.call(__MODULE__, {:list_instruments, venue})
  end

  @doc """
  Lists all active instruments.
  """
  @spec list_active_instruments() :: [Instrument.t()]
  def list_active_instruments do
    GenServer.call(__MODULE__, :list_active_instruments)
  end

  @doc """
  Searches instruments by criteria.
  """
  @spec search_instruments(map()) :: [Instrument.t()]
  def search_instruments(criteria) do
    GenServer.call(__MODULE__, {:search_instruments, criteria})
  end

  @doc """
  Updates an instrument.
  """
  @spec update_instrument(String.t(), String.t(), map()) :: {:ok, Instrument.t()} | {:error, any()}
  def update_instrument(symbol, venue, updates) do
    GenServer.call(__MODULE__, {:update_instrument, symbol, venue, updates})
  end

  @doc """
  Deactivates an instrument.
  """
  @spec deactivate_instrument(String.t(), String.t()) :: {:ok, Instrument.t()} | {:error, any()}
  def deactivate_instrument(symbol, venue \\ "default") do
    GenServer.call(__MODULE__, {:deactivate_instrument, symbol, venue})
  end

  @doc """
  Refreshes the instrument cache from database.
  """
  @spec refresh_cache() :: :ok
  def refresh_cache do
    GenServer.cast(__MODULE__, :refresh_cache)
  end

  @doc """
  Subscribes to instrument updates.
  """
  @spec subscribe() :: :ok
  def subscribe do
    GenServer.cast(__MODULE__, {:subscribe, self()})
    PubSub.subscribe(TtQuant.PubSub, "instruments")
  end

  @doc """
  Unsubscribes from instrument updates.
  """
  @spec unsubscribe() :: :ok
  def unsubscribe do
    GenServer.cast(__MODULE__, {:unsubscribe, self()})
    PubSub.unsubscribe(TtQuant.PubSub, "instruments")
  end

  @doc """
  Gets registry statistics.
  """
  @spec get_stats() :: map()
  def get_stats do
    GenServer.call(__MODULE__, :get_stats)
  end

  # Server Callbacks

  @impl true
  def init(_opts) do
    Logger.info("Starting instrument registry")

    # Load instruments from database
    instruments = load_instruments_from_db()
    symbols_by_venue = build_venue_index(instruments)

    state = %{
      instruments: instruments,
      symbols_by_venue: symbols_by_venue,
      last_refresh: DateTime.utc_now(),
      subscribers: MapSet.new()
    }

    # Schedule periodic refresh
    schedule_refresh()

    {:ok, state}
  end

  @impl true
  def handle_call({:register_instrument, instrument}, _from, state) do
    # Just update cache, assume instrument is already in database
    key = instrument_key(instrument.symbol, instrument.venue)
    new_instruments = Map.put(state.instruments, key, instrument)
    new_symbols_by_venue = add_to_venue_index(state.symbols_by_venue, instrument)

    new_state = %{state |
      instruments: new_instruments,
      symbols_by_venue: new_symbols_by_venue
    }

    # Broadcast update
    broadcast_instrument_event(:instrument_registered, instrument)

    {:reply, {:ok, instrument}, new_state}
  end

  @impl true
  def handle_call({:get_instrument, symbol, venue}, _from, state) do
    key = instrument_key(symbol, venue)
    instrument = Map.get(state.instruments, key)
    {:reply, instrument, state}
  end

  @impl true
  def handle_call({:list_instruments, venue}, _from, state) do
    symbols = Map.get(state.symbols_by_venue, venue, MapSet.new())

    instruments = symbols
    |> Enum.map(fn symbol ->
      key = instrument_key(symbol, venue)
      Map.get(state.instruments, key)
    end)
    |> Enum.filter(& &1)

    {:reply, instruments, state}
  end

  @impl true
  def handle_call(:list_active_instruments, _from, state) do
    active_instruments = state.instruments
    |> Map.values()
    |> Enum.filter(&Instrument.tradeable?/1)

    {:reply, active_instruments, state}
  end

  @impl true
  def handle_call({:search_instruments, criteria}, _from, state) do
    results = state.instruments
    |> Map.values()
    |> filter_by_criteria(criteria)

    {:reply, results, state}
  end

  @impl true
  def handle_call({:update_instrument, symbol, venue, updates}, _from, state) do
    key = instrument_key(symbol, venue)

    case Map.get(state.instruments, key) do
      nil ->
        {:reply, {:error, :not_found}, state}

      instrument ->
        changeset = Instrument.changeset(instrument, updates)

        case Repo.update(changeset) do
          {:ok, updated_instrument} ->
            # Update cache
            new_instruments = Map.put(state.instruments, key, updated_instrument)
            new_state = %{state | instruments: new_instruments}

            # Broadcast update
            broadcast_instrument_event(:instrument_updated, updated_instrument)

            {:reply, {:ok, updated_instrument}, new_state}

          {:error, changeset} ->
            {:reply, {:error, changeset}, state}
        end
    end
  end

  @impl true
  def handle_call({:deactivate_instrument, symbol, venue}, _from, state) do
    update_instrument(symbol, venue, %{is_active: false})
    {:reply, :ok, state}
  end

  @impl true
  def handle_call(:get_stats, _from, state) do
    stats = %{
      total_instruments: map_size(state.instruments),
      active_instruments: state.instruments |> Map.values() |> Enum.count(&Instrument.tradeable?/1),
      venues: Map.keys(state.symbols_by_venue),
      last_refresh: state.last_refresh,
      subscribers: MapSet.size(state.subscribers)
    }

    {:reply, stats, state}
  end

  @impl true
  def handle_cast(:refresh_cache, state) do
    # Skip refresh in test environment to avoid database connection issues
    if Mix.env() == :test do
      Logger.debug("Skipping cache refresh in test environment")
      {:noreply, state}
    else
      Logger.info("Refreshing instrument cache")

      # Reload from database
      instruments = load_instruments_from_db()
      symbols_by_venue = build_venue_index(instruments)

      new_state = %{state |
        instruments: instruments,
        symbols_by_venue: symbols_by_venue,
        last_refresh: DateTime.utc_now()
      }

      # Broadcast refresh event
      broadcast_instrument_event(:cache_refreshed, %{count: map_size(instruments)})

      {:noreply, new_state}
    end
  end

  @impl true
  def handle_cast({:subscribe, pid}, state) do
    new_subscribers = MapSet.put(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  @impl true
  def handle_cast({:unsubscribe, pid}, state) do
    new_subscribers = MapSet.delete(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  @impl true
  def handle_info(:refresh_cache, state) do
    handle_cast(:refresh_cache, state)
    schedule_refresh()
    {:noreply, state}
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, pid, _reason}, state) do
    new_subscribers = MapSet.delete(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  # Private helper functions

  defp load_instruments_from_db do
    try do
      Instrument
      |> Repo.all()
      |> Enum.reduce(%{}, fn instrument, acc ->
        key = instrument_key(instrument.symbol, instrument.venue)
        Map.put(acc, key, instrument)
      end)
    rescue
      _ ->
        # Return empty map if database is not available (e.g., in tests)
        %{}
    end
  end

  defp build_venue_index(instruments) do
    instruments
    |> Map.values()
    |> Enum.reduce(%{}, fn instrument, acc ->
      venue = instrument.venue
      symbols = Map.get(acc, venue, MapSet.new())
      updated_symbols = MapSet.put(symbols, instrument.symbol)
      Map.put(acc, venue, updated_symbols)
    end)
  end

  defp add_to_venue_index(venue_index, %Instrument{symbol: symbol, venue: venue}) do
    symbols = Map.get(venue_index, venue, MapSet.new())
    updated_symbols = MapSet.put(symbols, symbol)
    Map.put(venue_index, venue, updated_symbols)
  end

  defp instrument_key(symbol, venue), do: "#{symbol}@#{venue}"

  defp filter_by_criteria(instruments, criteria) do
    Enum.filter(instruments, fn instrument ->
      Enum.all?(criteria, fn {key, value} ->
        case key do
          :instrument_type -> instrument.instrument_type == value
          :base_currency -> instrument.base_currency == value
          :quote_currency -> instrument.quote_currency == value
          :venue -> instrument.venue == value
          :is_active -> instrument.is_active == value
          :symbol_contains -> String.contains?(String.downcase(instrument.symbol), String.downcase(value))
          :name_contains -> String.contains?(String.downcase(instrument.name), String.downcase(value))
          _ -> true
        end
      end)
    end)
  end

  defp schedule_refresh do
    Process.send_after(self(), :refresh_cache, 300_000)  # Refresh every 5 minutes
  end

  defp broadcast_instrument_event(event_type, data) do
    message = %{
      type: event_type,
      data: data,
      timestamp: DateTime.utc_now()
    }

    PubSub.broadcast(TtQuant.PubSub, "instruments", message)
  end
end
