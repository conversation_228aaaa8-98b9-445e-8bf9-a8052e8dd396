defmodule TtQuant.Instruments.MarketRules do
  @moduledoc """
  Market rules and validation for trading instruments.

  Provides comprehensive validation and rule enforcement for
  trading operations based on instrument specifications.
  """

  alias TtQuant.Instruments.Instrument
  alias TtQuant.Trading.Order

  @type validation_result :: :ok | {:error, String.t()}
  @type price_validation :: :ok | {:error, :invalid_tick_size | :out_of_range}
  @type quantity_validation :: :ok | {:error, :invalid_lot_size | :below_minimum | :above_maximum}
  @type trading_validation :: :ok | {:error, :instrument_inactive | :expired | :outside_hours}

  @doc """
  Validates an order against instrument rules.
  """
  @spec validate_order(Order.t(), Instrument.t()) :: validation_result()
  def validate_order(%Order{} = order, %Instrument{} = instrument) do
    with :ok <- validate_trading_allowed(instrument),
         :ok <- validate_price(order.price, instrument),
         :ok <- validate_quantity(order.quantity, instrument),
         :ok <- validate_order_value(order, instrument) do
      :ok
    else
      error -> error
    end
  end

  @doc """
  Validates if trading is currently allowed for an instrument.
  """
  @spec validate_trading_allowed(Instrument.t()) :: trading_validation()
  def validate_trading_allowed(%Instrument{is_active: false}) do
    {:error, :instrument_inactive}
  end

  def validate_trading_allowed(%Instrument{expiry_date: expiry_date}) when not is_nil(expiry_date) do
    if Date.compare(Date.utc_today(), expiry_date) == :lt do
      :ok
    else
      {:error, :expired}
    end
  end

  def validate_trading_allowed(%Instrument{trading_hours: hours} = instrument) when map_size(hours) > 0 do
    if Instrument.in_trading_hours?(instrument, DateTime.utc_now()) do
      :ok
    else
      {:error, :outside_hours}
    end
  end

  def validate_trading_allowed(%Instrument{}) do
    :ok
  end

  @doc """
  Validates price against tick size rules.
  """
  @spec validate_price(Decimal.t() | nil, Instrument.t()) :: price_validation()
  def validate_price(nil, %Instrument{}), do: :ok  # Market orders don't have price

  def validate_price(price, %Instrument{} = instrument) do
    if Instrument.valid_price?(instrument, price) do
      :ok
    else
      {:error, :invalid_tick_size}
    end
  end

  @doc """
  Validates quantity against lot size and min/max rules.
  """
  @spec validate_quantity(Decimal.t(), Instrument.t()) :: quantity_validation()
  def validate_quantity(quantity, %Instrument{} = instrument) do
    cond do
      not Instrument.valid_quantity?(instrument, quantity) ->
        cond do
          Decimal.compare(quantity, instrument.min_quantity) == :lt ->
            {:error, :below_minimum}
          Decimal.compare(quantity, instrument.max_quantity) == :gt ->
            {:error, :above_maximum}
          true ->
            {:error, :invalid_lot_size}
        end

      true -> :ok
    end
  end

  @doc """
  Validates order value constraints.
  """
  @spec validate_order_value(Order.t(), Instrument.t()) :: validation_result()
  def validate_order_value(%Order{order_type: :market}, %Instrument{}) do
    # Market orders don't have predetermined value
    :ok
  end

  def validate_order_value(%Order{price: price, quantity: quantity}, %Instrument{} = instrument) when not is_nil(price) do
    order_value = Decimal.mult(price, quantity)

    # Check minimum order value (if configured)
    min_order_value = get_min_order_value(instrument)
    if min_order_value && Decimal.compare(order_value, min_order_value) == :lt do
      {:error, "Order value below minimum: #{min_order_value}"}
    else
      :ok
    end
  end

  def validate_order_value(%Order{}, %Instrument{}) do
    :ok
  end

  @doc """
  Suggests corrected price based on tick size.
  """
  @spec suggest_price(Decimal.t(), Instrument.t()) :: Decimal.t()
  def suggest_price(price, %Instrument{} = instrument) do
    Instrument.round_price(instrument, price)
  end

  @doc """
  Suggests corrected quantity based on lot size.
  """
  @spec suggest_quantity(Decimal.t(), Instrument.t()) :: Decimal.t()
  def suggest_quantity(quantity, %Instrument{} = instrument) do
    rounded = Instrument.round_quantity(instrument, quantity)

    # Ensure it's within min/max bounds
    cond do
      Decimal.compare(rounded, instrument.min_quantity) == :lt ->
        instrument.min_quantity
      Decimal.compare(rounded, instrument.max_quantity) == :gt ->
        instrument.max_quantity
      true ->
        rounded
    end
  end

  @doc """
  Calculates trading fees for an order.
  """
  @spec calculate_trading_fees(Order.t(), Instrument.t(), :maker | :taker) :: %{commission: Decimal.t(), total_cost: Decimal.t()}
  def calculate_trading_fees(%Order{price: price, quantity: quantity}, %Instrument{} = instrument, liquidity_side) when not is_nil(price) do
    order_value = Decimal.mult(price, quantity)
    commission = Instrument.calculate_fee(instrument, order_value, liquidity_side)
    total_cost = Decimal.add(order_value, commission)

    %{
      commission: commission,
      total_cost: total_cost
    }
  end

  def calculate_trading_fees(%Order{}, %Instrument{}, _liquidity_side) do
    # Cannot calculate fees for market orders without execution price
    %{
      commission: Decimal.new(0),
      total_cost: Decimal.new(0)
    }
  end

  @doc """
  Gets trading session information for an instrument.
  """
  @spec get_trading_session(Instrument.t(), DateTime.t()) :: %{status: :open | :closed | :pre_market | :post_market, next_open: DateTime.t() | nil}
  def get_trading_session(%Instrument{trading_hours: hours}, _datetime) when map_size(hours) == 0 do
    %{status: :open, next_open: nil}  # Always open if no hours specified
  end

  def get_trading_session(%Instrument{} = instrument, datetime) do
    if Instrument.in_trading_hours?(instrument, datetime) do
      %{status: :open, next_open: nil}
    else
      %{status: :closed, next_open: calculate_next_open(instrument, datetime)}
    end
  end

  @doc """
  Validates position size against instrument limits.
  """
  @spec validate_position_size(Decimal.t(), Instrument.t()) :: validation_result()
  def validate_position_size(position_size, %Instrument{max_quantity: max_qty}) do
    if Decimal.compare(Decimal.abs(position_size), max_qty) == :gt do
      {:error, "Position size exceeds maximum allowed: #{max_qty}"}
    else
      :ok
    end
  end

  @doc """
  Gets risk parameters for an instrument.
  """
  @spec get_risk_parameters(Instrument.t()) :: map()
  def get_risk_parameters(%Instrument{} = instrument) do
    %{
      tick_size: instrument.tick_size,
      lot_size: instrument.lot_size,
      min_quantity: instrument.min_quantity,
      max_quantity: instrument.max_quantity,
      maker_fee: instrument.maker_fee,
      taker_fee: instrument.taker_fee,
      contract_size: instrument.contract_size,
      price_precision: instrument.price_precision,
      quantity_precision: instrument.quantity_precision
    }
  end

  @doc """
  Formats validation error for display.
  """
  @spec format_validation_error(any()) :: String.t()
  def format_validation_error({:error, :instrument_inactive}) do
    "Instrument is not active for trading"
  end

  def format_validation_error({:error, :expired}) do
    "Instrument has expired"
  end

  def format_validation_error({:error, :outside_hours}) do
    "Trading is outside allowed hours"
  end

  def format_validation_error({:error, :invalid_tick_size}) do
    "Price does not conform to tick size requirements"
  end

  def format_validation_error({:error, :invalid_lot_size}) do
    "Quantity does not conform to lot size requirements"
  end

  def format_validation_error({:error, :below_minimum}) do
    "Quantity is below minimum allowed"
  end

  def format_validation_error({:error, :above_maximum}) do
    "Quantity is above maximum allowed"
  end

  def format_validation_error({:error, message}) when is_binary(message) do
    message
  end

  def format_validation_error(_) do
    "Unknown validation error"
  end

  # Private helper functions

  defp get_min_order_value(%Instrument{venue: venue}) do
    # This could be configured per venue
    case venue do
      "binance" -> Decimal.new("10.0")
      "coinbase" -> Decimal.new("5.0")
      _ -> nil
    end
  end

  defp calculate_next_open(%Instrument{trading_hours: hours}, datetime) do
    # This is a simplified implementation
    # In practice, you'd need more sophisticated logic to handle
    # weekends, holidays, and different time zones

    current_date = DateTime.to_date(datetime)
    next_date = Date.add(current_date, 1)

    # Find next trading day
    day_of_week = Date.day_of_week(next_date)
    day_name = day_name(day_of_week)

    case Map.get(hours, day_name) do
      %{"open" => open_str} ->
        {:ok, open_time} = Time.from_iso8601(open_str)
        {:ok, next_open} = DateTime.new(next_date, open_time)
        next_open

      _ ->
        # Try next day
        calculate_next_open_recursive(hours, next_date, 7)  # Max 7 days ahead
    end
  end

  defp calculate_next_open_recursive(_hours, _date, 0), do: nil

  defp calculate_next_open_recursive(hours, date, days_left) do
    day_of_week = Date.day_of_week(date)
    day_name = day_name(day_of_week)

    case Map.get(hours, day_name) do
      %{"open" => open_str} ->
        {:ok, open_time} = Time.from_iso8601(open_str)
        {:ok, next_open} = DateTime.new(date, open_time)
        next_open

      _ ->
        next_date = Date.add(date, 1)
        calculate_next_open_recursive(hours, next_date, days_left - 1)
    end
  end

  defp day_name(1), do: "monday"
  defp day_name(2), do: "tuesday"
  defp day_name(3), do: "wednesday"
  defp day_name(4), do: "thursday"
  defp day_name(5), do: "friday"
  defp day_name(6), do: "saturday"
  defp day_name(7), do: "sunday"
end
