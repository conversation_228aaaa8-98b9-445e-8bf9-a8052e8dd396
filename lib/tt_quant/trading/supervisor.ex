defmodule TtQuant.Trading.Supervisor do
  @moduledoc """
  Supervisor for trading order managers and registry.
  
  Manages the lifecycle of order managers for different accounts.
  """

  use Supervisor
  require Logger

  def start_link(init_arg) do
    Supervisor.start_link(__MODULE__, init_arg, name: __MODULE__)
  end

  @impl true
  def init(_init_arg) do
    children = [
      # Registry for order managers
      {Registry, keys: :unique, name: TtQuant.Trading.Registry},
      
      # Dynamic supervisor for order managers
      {DynamicSupervisor, strategy: :one_for_one, name: TtQuant.Trading.DynamicSupervisor}
    ]

    Supervisor.init(children, strategy: :one_for_one)
  end

  @doc """
  Starts an order manager for a specific account.
  """
  @spec start_order_manager(binary()) :: DynamicSupervisor.on_start_child()
  def start_order_manager(account_id) do
    child_spec = %{
      id: {TtQuant.Trading.OrderManager, account_id},
      start: {TtQuant.Trading.OrderManager, :start_link, [account_id]},
      restart: :transient
    }

    case DynamicSupervisor.start_child(TtQuant.Trading.DynamicSupervisor, child_spec) do
      {:ok, pid} ->
        Logger.info("Started order manager for account #{account_id}")
        {:ok, pid}
        
      {:error, {:already_started, pid}} ->
        Logger.debug("Order manager for account #{account_id} already running")
        {:ok, pid}
        
      error ->
        Logger.error("Failed to start order manager for account #{account_id}: #{inspect(error)}")
        error
    end
  end

  @doc """
  Stops an order manager for a specific account.
  """
  @spec stop_order_manager(binary()) :: :ok | {:error, :not_found}
  def stop_order_manager(account_id) do
    case Registry.lookup(TtQuant.Trading.Registry, account_id) do
      [{pid, _}] ->
        DynamicSupervisor.terminate_child(TtQuant.Trading.DynamicSupervisor, pid)
        Logger.info("Stopped order manager for account #{account_id}")
        :ok
        
      [] ->
        {:error, :not_found}
    end
  end

  @doc """
  Lists all running order managers.
  """
  @spec list_order_managers() :: [binary()]
  def list_order_managers do
    Registry.select(TtQuant.Trading.Registry, [{{:"$1", :_, :_}, [], [:"$1"]}])
  end

  @doc """
  Checks if an order manager is running for an account.
  """
  @spec order_manager_running?(binary()) :: boolean()
  def order_manager_running?(account_id) do
    case Registry.lookup(TtQuant.Trading.Registry, account_id) do
      [{_pid, _}] -> true
      [] -> false
    end
  end

  @doc """
  Gets the PID of an order manager for an account.
  """
  @spec get_order_manager_pid(binary()) :: {:ok, pid()} | {:error, :not_found}
  def get_order_manager_pid(account_id) do
    case Registry.lookup(TtQuant.Trading.Registry, account_id) do
      [{pid, _}] -> {:ok, pid}
      [] -> {:error, :not_found}
    end
  end

  @doc """
  Ensures an order manager is running for an account.
  """
  @spec ensure_order_manager(binary()) :: {:ok, pid()} | {:error, any()}
  def ensure_order_manager(account_id) do
    case get_order_manager_pid(account_id) do
      {:ok, pid} -> {:ok, pid}
      {:error, :not_found} -> start_order_manager(account_id)
    end
  end
end
