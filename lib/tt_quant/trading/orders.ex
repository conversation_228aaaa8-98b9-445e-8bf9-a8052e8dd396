defmodule TtQuant.Trading.Orders do
  @moduledoc """
  订单管理 Context 模块，提供订单相关的业务逻辑
  """

  import Ecto.Query, warn: false
  alias TtQuant.Repo
  alias TtQuant.Trading.Order
  alias TtQuant.Core
  alias Phoenix.PubSub

  require Logger

  @pubsub TtQuant.PubSub
  @topic_prefix "orders:"

  # 订单状态枚举
  @order_statuses ~w(initialized submitted accepted rejected cancelled expired triggered 
                    pending_update pending_cancel partially_filled filled)a

  @doc """
  创建新订单
  """
  def create_order(attrs \\ %{}) do
    changeset = Order.changeset(%Order{}, attrs)

    with {:ok, order} <- Repo.insert(changeset),
         {:ok, _} <- submit_to_engine(order) do
      broadcast_event(:order_created, order)
      {:ok, order}
    else
      {:error, %Ecto.Changeset{} = changeset} ->
        {:error, changeset}
      {:error, reason} ->
        Logger.error("Failed to create order: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  提交订单到 Rust 引擎
  """
  def submit_to_engine(order) do
    with {:ok, order_binary} <- serialize_order(order),
         {:ok, result} <- Core.process_order(order_binary) do
      process_engine_result(result, order)
    end
  end

  @doc """
  取消订单
  """
  def cancel_order(order_id, reason \\ "User requested") do
    with {:ok, order} <- get_order(order_id),
         :ok <- validate_cancellable(order),
         {:ok, _} <- Core.cancel_order(order.client_order_id) do
      order
      |> Order.changeset(%{status: :cancelled, cancel_reason: reason})
      |> Repo.update()
      |> case do
        {:ok, updated_order} ->
          broadcast_event(:order_cancelled, updated_order)
          {:ok, updated_order}
        error ->
          error
      end
    end
  end

  @doc """
  更新订单
  """
  def update_order(order_id, attrs) do
    with {:ok, order} <- get_order(order_id),
         :ok <- validate_updatable(order),
         {:ok, updated} <- do_update_order(order, attrs) do
      broadcast_event(:order_updated, updated)
      {:ok, updated}
    end
  end

  @doc """
  获取订单
  """
  def get_order(id) do
    case Repo.get(Order, id) do
      nil -> {:error, :not_found}
      order -> {:ok, order}
    end
  end

  @doc """
  获取订单通过 client_order_id
  """
  def get_order_by_client_id(client_order_id) do
    case Repo.get_by(Order, client_order_id: client_order_id) do
      nil -> {:error, :not_found}
      order -> {:ok, order}
    end
  end

  @doc """
  列出用户的所有订单
  """
  def list_orders(user_id, opts \\ []) do
    status = Keyword.get(opts, :status)
    limit = Keyword.get(opts, :limit, 100)
    offset = Keyword.get(opts, :offset, 0)

    query = from o in Order,
      where: o.user_id == ^user_id,
      order_by: [desc: o.inserted_at],
      limit: ^limit,
      offset: ^offset

    query = if status do
      where(query, [o], o.status == ^status)
    else
      query
    end

    Repo.all(query)
  end

  @doc """
  列出活跃订单
  """
  def list_active_orders(user_id) do
    from(o in Order,
      where: o.user_id == ^user_id,
      where: o.status in [:accepted, :partially_filled],
      order_by: [desc: o.inserted_at]
    )
    |> Repo.all()
  end

  @doc """
  获取订单簿快照
  """
  def get_orderbook_snapshot(instrument_id, levels \\ 10) do
    with {:ok, snapshot_binary} <- Core.get_orderbook_snapshot(instrument_id, levels),
         {:ok, snapshot} <- deserialize_orderbook_snapshot(snapshot_binary) do
      {:ok, snapshot}
    end
  end

  @doc """
  处理订单成交
  """
  def handle_fill(order_id, fill_data) do
    with {:ok, order} <- get_order_by_client_id(order_id),
         {:ok, updated} <- process_fill(order, fill_data) do
      broadcast_event(:order_filled, updated, fill_data)
      {:ok, updated}
    end
  end

  @doc """
  同步 Rust 引擎事件
  """
  def sync_engine_events do
    case Core.get_sync_events() do
      {:ok, events} ->
        Enum.each(events, &process_sync_event/1)
        :ok
      {:error, reason} ->
        Logger.error("Failed to sync engine events: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # 私有函数

  defp serialize_order(order) do
    data = %{
      client_order_id: order.client_order_id,
      instrument_id: %{
        symbol: order.symbol,
        venue: order.venue
      },
      side: order.side,
      order_type: order.order_type,
      quantity: order.quantity,
      price: order.price,
      time_in_force: order.time_in_force || "GTC"
    }

    {:ok, :erlang.term_to_binary(data)}
  end

  defp deserialize_orderbook_snapshot(binary) do
    try do
      {:ok, :erlang.binary_to_term(binary)}
    rescue
      _ -> {:error, :deserialization_failed}
    end
  end

  defp validate_cancellable(order) do
    if order.status in [:accepted, :partially_filled, :pending_update] do
      :ok
    else
      {:error, :invalid_status_for_cancel}
    end
  end

  defp validate_updatable(order) do
    if order.status in [:accepted, :partially_filled] do
      :ok
    else
      {:error, :invalid_status_for_update}
    end
  end

  defp do_update_order(order, attrs) do
    order
    |> Order.update_changeset(attrs)
    |> Repo.update()
  end

  defp process_fill(order, fill_data) do
    new_filled_qty = Decimal.add(order.filled_quantity || Decimal.new(0), fill_data.quantity)
    new_status = if Decimal.equal?(new_filled_qty, order.quantity) do
      :filled
    else
      :partially_filled
    end

    # 计算平均成交价格
    new_avg_price = calculate_average_price(
      order.average_price,
      order.filled_quantity,
      fill_data.price,
      fill_data.quantity
    )

    order
    |> Order.changeset(%{
      status: new_status,
      filled_quantity: new_filled_qty,
      remaining_quantity: Decimal.sub(order.quantity, new_filled_qty),
      average_price: new_avg_price
    })
    |> Repo.update()
  end

  defp calculate_average_price(nil, _old_qty, new_price, _new_qty), do: new_price
  defp calculate_average_price(old_avg, old_qty, new_price, new_qty) do
    old_value = Decimal.mult(old_avg || Decimal.new(0), old_qty || Decimal.new(0))
    new_value = Decimal.mult(new_price, new_qty)
    total_value = Decimal.add(old_value, new_value)
    total_qty = Decimal.add(old_qty || Decimal.new(0), new_qty)
    
    if Decimal.equal?(total_qty, 0) do
      Decimal.new(0)
    else
      Decimal.div(total_value, total_qty)
    end
  end

  defp process_engine_result({:ok, fills}, order) when is_list(fills) do
    # 处理立即成交的情况
    Enum.each(fills, fn fill ->
      handle_fill(order.client_order_id, fill)
    end)
    {:ok, order}
  end

  defp process_engine_result({:ok, _}, order), do: {:ok, order}
  defp process_engine_result({:error, reason}, _order), do: {:error, reason}

  defp process_sync_event(%{type: "OrderCreated", data: data}) do
    Logger.info("Order created: #{inspect(data)}")
    # 更新本地订单状态
  end

  defp process_sync_event(%{type: "OrderUpdated", data: data}) do
    Logger.info("Order updated: #{inspect(data)}")
    # 更新本地订单状态
  end

  defp process_sync_event(%{type: "FillExecuted", data: data}) do
    Logger.info("Fill executed: #{inspect(data)}")
    handle_fill(data.order_id, data.fill)
  end

  defp process_sync_event(event) do
    Logger.debug("Received sync event: #{inspect(event)}")
  end

  defp broadcast_event(event, order, extra \\ nil) do
    topic = "#{@topic_prefix}#{order.user_id}"
    
    payload = %{
      event: event,
      order: serialize_for_broadcast(order),
      extra: extra,
      timestamp: DateTime.utc_now()
    }

    PubSub.broadcast(@pubsub, topic, payload)
    PubSub.broadcast(@pubsub, "#{@topic_prefix}all", payload)
  end

  defp serialize_for_broadcast(order) do
    Map.take(order, [
      :id,
      :client_order_id,
      :symbol,
      :venue,
      :side,
      :order_type,
      :status,
      :quantity,
      :price,
      :filled_quantity,
      :remaining_quantity,
      :average_price,
      :inserted_at,
      :updated_at
    ])
  end

  @doc """
  订阅订单事件
  """
  def subscribe_to_orders(user_id) do
    PubSub.subscribe(@pubsub, "#{@topic_prefix}#{user_id}")
  end

  @doc """
  订阅所有订单事件（管理员用）
  """
  def subscribe_to_all_orders do
    PubSub.subscribe(@pubsub, "#{@topic_prefix}all")
  end
end
