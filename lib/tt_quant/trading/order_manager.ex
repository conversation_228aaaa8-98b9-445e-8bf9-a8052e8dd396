defmodule TtQuant.Trading.OrderManager do
  @moduledoc """
  GenServer for managing order lifecycle and execution.

  Handles:
  - Order submission and routing
  - Order status updates
  - Fill processing
  - Risk checks before order placement
  """

  use GenServer
  require Logger
  alias TtQuant.Trading.{Order, Trade, Execution}
  alias TtQuant.Portfolio
  alias TtQuant.Repo
  alias TtQuant.Events.EventDispatcher
  alias Phoenix.PubSub
  import Ecto.Query

  @type state :: %{
          account_id: binary(),
          active_orders: %{String.t() => Order.t()},
          order_history: [Order.t()],
          risk_limits: map(),
          subscribers: MapSet.t()
        }

  # Client API

  @doc """
  Starts the order manager for a specific account.
  """
  @spec start_link(binary()) :: GenServer.on_start()
  def start_link(account_id) do
    GenServer.start_link(__MODULE__, account_id, name: via_tuple(account_id))
  end

  @doc """
  Submits a new order.
  """
  @spec submit_order(binary(), map()) :: {:ok, Order.t()} | {:error, any()}
  def submit_order(account_id, order_params) do
    GenServer.call(via_tuple(account_id), {:submit_order, order_params})
  end

  @doc """
  Cancels an order.
  """
  @spec cancel_order(binary(), String.t()) :: {:ok, Order.t()} | {:error, any()}
  def cancel_order(account_id, client_order_id) do
    GenServer.call(via_tuple(account_id), {:cancel_order, client_order_id})
  end

  @doc """
  Updates an order.
  """
  @spec update_order(binary(), String.t(), map()) :: {:ok, Order.t()} | {:error, any()}
  def update_order(account_id, client_order_id, updates) do
    GenServer.call(via_tuple(account_id), {:update_order, client_order_id, updates})
  end

  @doc """
  Processes an order fill.
  """
  @spec process_fill(binary(), String.t(), Decimal.t(), Decimal.t()) :: :ok
  def process_fill(account_id, client_order_id, fill_quantity, fill_price) do
    GenServer.cast(via_tuple(account_id), {:process_fill, client_order_id, fill_quantity, fill_price})
  end

  @doc """
  Updates order status.
  """
  @spec update_order_status(binary(), String.t(), Order.order_status()) :: :ok
  def update_order_status(account_id, client_order_id, new_status) do
    GenServer.cast(via_tuple(account_id), {:update_status, client_order_id, new_status})
  end

  @doc """
  Gets all active orders for the account.
  """
  @spec get_active_orders(binary()) :: [Order.t()]
  def get_active_orders(account_id) do
    GenServer.call(via_tuple(account_id), :get_active_orders)
  end

  @doc """
  Gets order by client order ID.
  """
  @spec get_order(binary(), String.t()) :: Order.t() | nil
  def get_order(account_id, client_order_id) do
    GenServer.call(via_tuple(account_id), {:get_order, client_order_id})
  end

  @doc """
  Subscribes to order updates.
  """
  @spec subscribe(binary()) :: :ok
  def subscribe(account_id) do
    GenServer.cast(via_tuple(account_id), {:subscribe, self()})
    PubSub.subscribe(TtQuant.PubSub, "orders:#{account_id}")
  end

  @doc """
  Unsubscribes from order updates.
  """
  @spec unsubscribe(binary()) :: :ok
  def unsubscribe(account_id) do
    GenServer.cast(via_tuple(account_id), {:unsubscribe, self()})
    PubSub.unsubscribe(TtQuant.PubSub, "orders:#{account_id}")
  end

  # Server Callbacks

  @impl true
  def init(account_id) do
    Logger.info("Starting order manager for account #{account_id}")

    # Load active orders from database
    active_orders = load_active_orders(account_id)

    state = %{
      account_id: account_id,
      active_orders: active_orders,
      order_history: [],
      risk_limits: load_risk_limits(account_id),
      subscribers: MapSet.new()
    }

    # Schedule periodic order checks
    schedule_order_check()

    {:ok, state}
  end

  @impl true
  def handle_call({:submit_order, order_params}, _from, state) do
    case create_and_validate_order(order_params, state) do
      {:ok, order} ->
        # Perform risk checks
        case perform_risk_checks(order, state) do
          :ok ->
            # Save order to database
            case Repo.insert(order) do
              {:ok, saved_order} ->
                # Add to active orders
                new_active_orders = Map.put(state.active_orders, saved_order.client_order_id, saved_order)
                new_state = %{state | active_orders: new_active_orders}

                # Submit to execution engine
                submit_to_execution(saved_order)

                # Dispatch order creation event
                EventDispatcher.dispatch_order_event(:order_created, state.account_id, %{
                  order_id: saved_order.id,
                  client_order_id: saved_order.client_order_id,
                  instrument_id: saved_order.instrument_id,
                  side: saved_order.side,
                  order_type: saved_order.order_type,
                  quantity: saved_order.quantity,
                  price: saved_order.price
                })

                {:reply, {:ok, saved_order}, new_state}

              {:error, changeset} ->
                {:reply, {:error, changeset}, state}
            end

          {:error, reason} ->
            {:reply, {:error, reason}, state}
        end

      {:error, changeset} ->
        {:reply, {:error, changeset}, state}
    end
  end

  @impl true
  def handle_call({:cancel_order, client_order_id}, _from, state) do
    case Map.get(state.active_orders, client_order_id) do
      nil ->
        {:reply, {:error, :order_not_found}, state}

      order ->
        if Order.terminal?(order) do
          {:reply, {:error, :order_already_terminal}, state}
        else
          # Update order status to pending cancel
          updated_order = Order.update_status(order, :pending_cancel)

          case Repo.update(updated_order) do
            {:ok, saved_order} ->
              # Update active orders
              new_active_orders = Map.put(state.active_orders, client_order_id, saved_order)
              new_state = %{state | active_orders: new_active_orders}

              # Send cancel request to execution engine
              cancel_at_execution(saved_order)

              # Broadcast cancel request
              broadcast_order_event(state.account_id, :cancel_requested, saved_order)

              {:reply, {:ok, saved_order}, new_state}

            {:error, changeset} ->
              {:reply, {:error, changeset}, state}
          end
        end
    end
  end

  @impl true
  def handle_call(:get_active_orders, _from, state) do
    orders = Map.values(state.active_orders)
    {:reply, orders, state}
  end

  @impl true
  def handle_call({:get_order, client_order_id}, _from, state) do
    order = Map.get(state.active_orders, client_order_id)
    {:reply, order, state}
  end

  @impl true
  def handle_cast({:process_fill, client_order_id, fill_quantity, fill_price}, state) do
    case Map.get(state.active_orders, client_order_id) do
      nil ->
        Logger.warning("Received fill for unknown order: #{client_order_id}")
        {:noreply, state}

      order ->
        # Add fill to order
        updated_order = Order.add_fill(order, fill_quantity, fill_price)

        case Repo.update(updated_order) do
          {:ok, saved_order} ->
            # Update portfolio position
            Portfolio.update_position(
              state.account_id,
              saved_order.instrument_id,
              saved_order.side,
              fill_quantity,
              fill_price,
              saved_order.currency
            )

            # Update active orders or remove if filled
            new_active_orders = if Order.terminal?(saved_order) do
              Map.delete(state.active_orders, client_order_id)
            else
              Map.put(state.active_orders, client_order_id, saved_order)
            end

            new_state = %{state | active_orders: new_active_orders}

            # Create trade record
            create_trade_record(saved_order, fill_quantity, fill_price)

            # Dispatch order fill event
            EventDispatcher.dispatch_order_event(:order_filled, state.account_id, %{
              order_id: saved_order.id,
              client_order_id: saved_order.client_order_id,
              instrument_id: saved_order.instrument_id,
              filled_quantity: fill_quantity,
              fill_price: fill_price,
              status: saved_order.status,
              remaining_quantity: saved_order.remaining_quantity
            })

            {:noreply, new_state}

          {:error, changeset} ->
            Logger.error("Failed to update order with fill: #{inspect(changeset)}")
            {:noreply, state}
        end
    end
  end

  @impl true
  def handle_cast({:update_status, client_order_id, new_status}, state) do
    case Map.get(state.active_orders, client_order_id) do
      nil ->
        Logger.warning("Received status update for unknown order: #{client_order_id}")
        {:noreply, state}

      order ->
        updated_order = Order.update_status(order, new_status)

        case Repo.update(updated_order) do
          {:ok, saved_order} ->
            # Update active orders or remove if terminal
            new_active_orders = if Order.terminal?(saved_order) do
              Map.delete(state.active_orders, client_order_id)
            else
              Map.put(state.active_orders, client_order_id, saved_order)
            end

            new_state = %{state | active_orders: new_active_orders}

            # Broadcast status update
            broadcast_order_event(state.account_id, :status_updated, saved_order)

            {:noreply, new_state}

          {:error, changeset} ->
            Logger.error("Failed to update order status: #{inspect(changeset)}")
            {:noreply, state}
        end
    end
  end

  @impl true
  def handle_cast({:subscribe, pid}, state) do
    new_subscribers = MapSet.put(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  @impl true
  def handle_cast({:unsubscribe, pid}, state) do
    new_subscribers = MapSet.delete(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  @impl true
  def handle_info(:order_check, state) do
    # Check for expired orders
    check_expired_orders(state)
    schedule_order_check()
    {:noreply, state}
  end

  @impl true
  def handle_info({:update_status, order_id, new_status}, state) do
    # Handle order status updates from external sources (e.g., venue)
    case Map.get(state.active_orders, order_id) do
      nil ->
        Logger.warning("Received status update for unknown order: #{order_id}")
        {:noreply, state}

      order ->
        case update_order_status_internal(order, new_status) do
          {:ok, updated_order} ->
            # Check if order is terminal and should be removed from active orders
            updated_active_orders = if Order.terminal?(updated_order) do
              Map.delete(state.active_orders, order_id)
            else
              Map.put(state.active_orders, order_id, updated_order)
            end

            new_state = %{state | active_orders: updated_active_orders}

            # Notify subscribers
            notify_subscribers(state, {:order_updated, updated_order})

            {:noreply, new_state}

          {:error, reason} ->
            Logger.error("Failed to update order status: #{inspect(reason)}")
            {:noreply, state}
        end
    end
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, pid, _reason}, state) do
    new_subscribers = MapSet.delete(state.subscribers, pid)
    {:noreply, %{state | subscribers: new_subscribers}}
  end

  # Private helper functions

  defp via_tuple(account_id) do
    {:via, Registry, {TtQuant.Trading.Registry, account_id}}
  end

  defp load_active_orders(account_id) do
    Order
    |> where([o], o.account_id == ^account_id)
    |> where([o], o.status not in [:rejected, :cancelled, :expired, :filled])
    |> Repo.all()
    |> Enum.reduce(%{}, fn order, acc ->
      Map.put(acc, order.client_order_id, order)
    end)
  end

  defp load_risk_limits(_account_id) do
    # Load risk limits from configuration or database
    %{
      max_order_value: Decimal.new("100000"),
      max_daily_loss: Decimal.new("10000"),
      max_position_size: Decimal.new("1000000")
    }
  end

  defp create_and_validate_order(params, state) do
    params_with_account = Map.put(params, :account_id, state.account_id)

    %Order{}
    |> Order.changeset(params_with_account)
    |> case do
      %{valid?: true} = changeset -> {:ok, Ecto.Changeset.apply_changes(changeset)}
      changeset -> {:error, changeset}
    end
  end

  defp perform_risk_checks(order, state) do
    # Check account balance
    required_balance = calculate_required_balance(order)

    if Portfolio.has_sufficient_balance?(state.account_id, order.currency, required_balance) do
      # Additional risk checks can be added here
      :ok
    else
      {:error, :insufficient_balance}
    end
  end

  defp calculate_required_balance(%Order{order_type: :market, quantity: quantity}) do
    # For market orders, we need to estimate based on current market price
    # This is a simplified calculation
    Decimal.mult(quantity, Decimal.new("50000"))  # Placeholder price
  end

  defp calculate_required_balance(%Order{price: price, quantity: quantity}) when not is_nil(price) do
    Decimal.mult(quantity, price)
  end

  defp calculate_required_balance(%Order{quantity: quantity}) do
    # Fallback calculation
    Decimal.mult(quantity, Decimal.new("1000"))
  end

  defp submit_to_execution(order) do
    # This would submit the order to the execution engine
    # For now, we'll just log it
    Logger.info("Submitting order to execution: #{order.client_order_id}")

    # Simulate immediate acceptance for market orders
    if order.order_type == :market do
      Process.send_after(self(), {:update_status, order.client_order_id, :accepted}, 100)
    end
  end

  defp cancel_at_execution(order) do
    # This would send cancel request to execution engine
    Logger.info("Cancelling order at execution: #{order.client_order_id}")
  end

  defp create_trade_record(order, fill_quantity, fill_price) do
    trade_attrs = %{
      account_id: order.account_id,
      order_id: order.id,
      instrument_id: order.instrument_id,
      side: order.side,
      quantity: fill_quantity,
      price: fill_price,
      currency: order.currency,
      executed_at: DateTime.utc_now()
    }

    # This would create a Trade record
    Logger.info("Creating trade record: #{inspect(trade_attrs)}")
  end

  defp schedule_order_check do
    Process.send_after(self(), :order_check, 60_000)  # Check every minute
  end

  defp check_expired_orders(state) do
    expired_orders = state.active_orders
    |> Map.values()
    |> Enum.filter(&Order.expired?/1)

    Enum.each(expired_orders, fn order ->
      GenServer.cast(self(), {:update_status, order.client_order_id, :expired})
    end)
  end

  defp notify_subscribers(state, message) do
    Enum.each(state.subscribers, fn pid ->
      send(pid, message)
    end)
  end



  defp update_order_status_internal(order, new_status) do
    # Reload order from database to get fresh version
    case Repo.get(Order, order.id) do
      nil ->
        {:error, :order_not_found}

      fresh_order ->
        # Update order status in database
        changeset = Order.changeset(fresh_order, %{status: new_status})

        case Repo.update(changeset) do
          {:ok, updated_order} ->
            {:ok, updated_order}
          {:error, %Ecto.Changeset{} = changeset} ->
            {:error, changeset}
          {:error, :stale} ->
            # Retry with refreshed order if stale
            update_order_status_internal(fresh_order, new_status)
        end
    end
  end

  defp broadcast_order_event(account_id, event_type, order, extra_data \\ %{}) do
    message = %{
      type: event_type,
      account_id: account_id,
      order: order,
      timestamp: DateTime.utc_now()
    }
    |> Map.merge(extra_data)

    PubSub.broadcast(TtQuant.PubSub, "orders:#{account_id}", message)
  end
end
