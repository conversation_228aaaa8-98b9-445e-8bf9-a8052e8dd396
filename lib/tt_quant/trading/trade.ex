defmodule TtQuant.Trading.Trade do
  @moduledoc """
  Trade execution record for tracking completed transactions.
  
  Records individual trade executions with full details for
  audit trail and performance analysis.
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Portfolio.Account
  alias TtQuant.Trading.Order

  @type t :: %__MODULE__{
          id: binary(),
          trade_id: String.t(),
          account_id: binary(),
          order_id: binary() | nil,
          instrument_id: String.t(),
          side: trade_side(),
          quantity: Decimal.t(),
          price: Decimal.t(),
          value: Decimal.t(),
          commission: Decimal.t(),
          currency: String.t(),
          liquidity_side: liquidity_side(),
          venue: String.t() | nil,
          executed_at: DateTime.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @type trade_side :: :buy | :sell
  @type liquidity_side :: :maker | :taker

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "trades" do
    field :trade_id, :string
    field :instrument_id, :string
    field :side, Ecto.Enum, values: [:buy, :sell]
    field :quantity, :decimal
    field :price, :decimal
    field :value, :decimal
    field :commission, :decimal, default: 0
    field :currency, :string
    field :liquidity_side, Ecto.Enum, values: [:maker, :taker], default: :taker
    field :venue, :string
    field :executed_at, :utc_datetime

    belongs_to :account, Account
    belongs_to :order, Order

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for trade creation.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(trade, attrs) do
    trade
    |> cast(attrs, [:trade_id, :account_id, :order_id, :instrument_id, :side, :quantity, 
                    :price, :value, :commission, :currency, :liquidity_side, :venue, :executed_at])
    |> validate_required([:trade_id, :account_id, :instrument_id, :side, :quantity, 
                         :price, :currency, :executed_at])
    |> validate_number(:quantity, greater_than: 0)
    |> validate_number(:price, greater_than: 0)
    |> validate_number(:commission, greater_than_or_equal_to: 0)
    |> calculate_value()
    |> unique_constraint(:trade_id)
  end

  @doc """
  Creates a trade from order fill.
  """
  @spec from_order_fill(Order.t(), Decimal.t(), Decimal.t(), map()) :: Ecto.Changeset.t()
  def from_order_fill(%Order{} = order, fill_quantity, fill_price, opts \\ %{}) do
    trade_id = generate_trade_id(order, fill_quantity, fill_price)
    
    attrs = %{
      trade_id: trade_id,
      account_id: order.account_id,
      order_id: order.id,
      instrument_id: order.instrument_id,
      side: order.side,
      quantity: fill_quantity,
      price: fill_price,
      currency: order.currency,
      liquidity_side: Map.get(opts, :liquidity_side, :taker),
      venue: Map.get(opts, :venue),
      executed_at: Map.get(opts, :executed_at, DateTime.utc_now())
    }
    
    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Calculates P&L for the trade relative to a reference price.
  """
  @spec calculate_pnl(t(), Decimal.t()) :: Decimal.t()
  def calculate_pnl(%__MODULE__{side: :buy, quantity: quantity, price: price}, reference_price) do
    price_diff = Decimal.sub(reference_price, price)
    Decimal.mult(quantity, price_diff)
  end
  
  def calculate_pnl(%__MODULE__{side: :sell, quantity: quantity, price: price}, reference_price) do
    price_diff = Decimal.sub(price, reference_price)
    Decimal.mult(quantity, price_diff)
  end

  @doc """
  Gets net proceeds from the trade (value - commission).
  """
  @spec net_proceeds(t()) :: Decimal.t()
  def net_proceeds(%__MODULE__{value: value, commission: commission}) do
    Decimal.sub(value, commission)
  end

  @doc """
  Checks if trade is a buy.
  """
  @spec buy?(t()) :: boolean()
  def buy?(%__MODULE__{side: :buy}), do: true
  def buy?(%__MODULE__{side: :sell}), do: false

  @doc """
  Checks if trade is a sell.
  """
  @spec sell?(t()) :: boolean()
  def sell?(%__MODULE__{side: :sell}), do: true
  def sell?(%__MODULE__{side: :buy}), do: false

  @doc """
  Gets trade direction as string.
  """
  @spec direction(t()) :: String.t()
  def direction(%__MODULE__{side: side}), do: Atom.to_string(side)

  @doc """
  Formats trade for display.
  """
  @spec format_summary(t()) :: String.t()
  def format_summary(%__MODULE__{} = trade) do
    "#{String.upcase(direction(trade))} #{trade.quantity} #{trade.instrument_id} @ #{trade.price} #{trade.currency}"
  end

  # Private helper functions

  defp calculate_value(changeset) do
    quantity = get_field(changeset, :quantity)
    price = get_field(changeset, :price)
    
    if quantity && price do
      value = Decimal.mult(quantity, price)
      put_change(changeset, :value, value)
    else
      changeset
    end
  end

  defp generate_trade_id(%Order{client_order_id: order_id}, quantity, price) do
    # Generate unique trade ID based on order, quantity, price and timestamp
    timestamp = DateTime.utc_now() |> DateTime.to_unix(:microsecond)
    hash_input = "#{order_id}_#{quantity}_#{price}_#{timestamp}"
    
    :crypto.hash(:sha256, hash_input)
    |> Base.encode16(case: :lower)
    |> String.slice(0, 16)
    |> then(&"trade_#{&1}")
  end
end
