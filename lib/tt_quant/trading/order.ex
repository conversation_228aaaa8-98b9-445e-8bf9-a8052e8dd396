defmodule TtQuant.Trading.Order do
  @moduledoc """
  Elixir wrapper for Rust order types with additional business logic.

  This module provides a high-level interface to the Rust order implementation
  while adding Elixir-specific functionality like database persistence and
  event handling.
  """

  use Ecto.Schema
  import Ecto.Changeset
  alias TtQuant.Portfolio.Account

  @type t :: %__MODULE__{
          id: binary(),
          client_order_id: String.t(),
          venue_order_id: String.t() | nil,
          account_id: binary(),
          instrument_id: String.t(),
          side: order_side(),
          order_type: order_type(),
          quantity: Decimal.t(),
          price: Decimal.t() | nil,
          stop_price: Decimal.t() | nil,
          time_in_force: time_in_force(),
          status: order_status(),
          filled_quantity: Decimal.t(),
          remaining_quantity: Decimal.t(),
          avg_fill_price: Decimal.t() | nil,
          currency: String.t(),
          expire_time: DateTime.t() | nil,
          tags: [String.t()],
          rust_order_data: binary() | nil,
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @type order_side :: :buy | :sell
  @type order_type :: :market | :limit | :stop | :stop_limit | :trailing_stop | :trailing_stop_limit
  @type order_status :: :initialized | :submitted | :accepted | :rejected | :cancelled |
                        :expired | :triggered | :pending_update | :pending_cancel |
                        :partially_filled | :filled
  @type time_in_force :: :gtc | :ioc | :fok | :gtd | :ato | :atc

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "orders" do
    field :client_order_id, :string
    field :venue_order_id, :string
    field :instrument_id, :string
    field :side, Ecto.Enum, values: [:buy, :sell]
    field :order_type, Ecto.Enum, values: [:market, :limit, :stop, :stop_limit, :trailing_stop, :trailing_stop_limit]
    field :quantity, :decimal
    field :price, :decimal
    field :stop_price, :decimal
    field :time_in_force, Ecto.Enum, values: [:gtc, :ioc, :fok, :gtd, :ato, :atc], default: :gtc
    field :status, Ecto.Enum, values: [:initialized, :submitted, :accepted, :rejected, :cancelled,
                                       :expired, :triggered, :pending_update, :pending_cancel,
                                       :partially_filled, :filled], default: :initialized
    field :filled_quantity, :decimal, default: 0
    field :remaining_quantity, :decimal
    field :avg_fill_price, :decimal
    field :currency, :string
    field :expire_time, :utc_datetime_usec
    field :tags, {:array, :string}, default: []
    field :rust_order_data, :binary

    belongs_to :account, Account

    timestamps(type: :utc_datetime)
  end

  @doc """
  Creates a changeset for order creation and updates.
  """
  @spec changeset(t(), map()) :: Ecto.Changeset.t()
  def changeset(order, attrs) do
    order
    |> cast(attrs, [:client_order_id, :venue_order_id, :account_id, :instrument_id, :side,
                    :order_type, :quantity, :price, :stop_price, :time_in_force, :status,
                    :filled_quantity, :remaining_quantity, :avg_fill_price, :currency,
                    :expire_time, :tags, :rust_order_data])
    |> validate_required([:client_order_id, :account_id, :instrument_id, :side, :order_type,
                         :quantity, :currency])
    |> validate_number(:quantity, greater_than: 0)
    |> validate_price_requirements()
    |> validate_time_constraints()
    |> calculate_remaining_quantity()
    |> unique_constraint(:client_order_id)
  end

  @doc """
  Creates an order from Rust order data.
  """
  @spec from_rust_order(binary(), binary(), binary()) :: {:ok, t()} | {:error, Ecto.Changeset.t()}
  def from_rust_order(rust_order_binary, account_id, currency) do
    # This would deserialize the Rust order and extract fields
    # For now, we'll create a placeholder implementation

    attrs = %{
      client_order_id: "order_#{System.unique_integer([:positive])}",
      account_id: account_id,
      instrument_id: "PLACEHOLDER",
      side: :buy,
      order_type: :market,
      quantity: Decimal.new("1.0"),
      currency: currency,
      rust_order_data: rust_order_binary
    }

    %__MODULE__{}
    |> changeset(attrs)
  end

  @doc """
  Converts order to Rust order format for NIF calls.
  """
  @spec to_rust_order(t()) :: {:ok, binary()} | {:error, String.t()}
  def to_rust_order(%__MODULE__{rust_order_data: data}) when not is_nil(data) do
    {:ok, data}
  end

  def to_rust_order(%__MODULE__{} = order) do
    # This would serialize the order to Rust format
    # For now, return a placeholder
    {:error, "Rust order serialization not implemented"}
  end

  @doc """
  Updates order status.
  """
  @spec update_status(t(), order_status()) :: Ecto.Changeset.t()
  def update_status(%__MODULE__{} = order, new_status) do
    changeset(order, %{status: new_status})
  end

  @doc """
  Adds a fill to the order.
  """
  @spec add_fill(t(), Decimal.t(), Decimal.t()) :: Ecto.Changeset.t()
  def add_fill(%__MODULE__{} = order, fill_quantity, fill_price) do
    new_filled = Decimal.add(order.filled_quantity, fill_quantity)
    new_remaining = Decimal.sub(order.quantity, new_filled)

    # Calculate new average fill price
    new_average_price = if Decimal.equal?(order.filled_quantity, Decimal.new(0)) do
      fill_price
    else
      current_value = Decimal.mult(order.filled_quantity, order.avg_fill_price || Decimal.new(0))
      fill_value = Decimal.mult(fill_quantity, fill_price)
      total_value = Decimal.add(current_value, fill_value)
      Decimal.div(total_value, new_filled)
    end

    # Determine new status
    new_status = cond do
      Decimal.equal?(new_remaining, Decimal.new(0)) -> :filled
      Decimal.gt?(new_filled, Decimal.new(0)) -> :partially_filled
      true -> order.status
    end

    changeset(order, %{
      filled_quantity: new_filled,
      remaining_quantity: new_remaining,
      average_fill_price: new_average_price,
      status: new_status
    })
  end

  @doc """
  Checks if order is in a terminal state.
  """
  @spec terminal?(t()) :: boolean()
  def terminal?(%__MODULE__{status: status}) do
    status in [:rejected, :cancelled, :expired, :filled]
  end

  @doc """
  Checks if order is active (can be filled).
  """
  @spec active?(t()) :: boolean()
  def active?(%__MODULE__{status: status}) do
    status in [:accepted, :triggered, :partially_filled]
  end

  @doc """
  Checks if order is pending.
  """
  @spec pending?(t()) :: boolean()
  def pending?(%__MODULE__{status: status}) do
    status in [:initialized, :submitted, :pending_update, :pending_cancel]
  end

  @doc """
  Gets fill percentage (0.0 to 1.0).
  """
  @spec fill_percentage(t()) :: Decimal.t()
  def fill_percentage(%__MODULE__{quantity: quantity, filled_quantity: filled}) do
    if Decimal.equal?(quantity, Decimal.new(0)) do
      Decimal.new(0)
    else
      Decimal.div(filled, quantity)
    end
  end

  @doc """
  Checks if order has expired.
  """
  @spec expired?(t()) :: boolean()
  def expired?(%__MODULE__{expire_time: nil}), do: false
  def expired?(%__MODULE__{expire_time: expire_time}) do
    DateTime.compare(DateTime.utc_now(), expire_time) == :gt
  end

  # Private validation functions
  defp validate_price_requirements(changeset) do
    order_type = get_field(changeset, :order_type)
    price = get_field(changeset, :price)
    stop_price = get_field(changeset, :stop_price)

    case order_type do
      :market ->
        changeset
        |> validate_exclusion(:price, [nil], message: "market orders cannot have a price")
        |> validate_exclusion(:stop_price, [nil], message: "market orders cannot have a stop price")

      :limit ->
        if is_nil(price) do
          add_error(changeset, :price, "is required for limit orders")
        else
          changeset
        end

      :stop ->
        if is_nil(stop_price) do
          add_error(changeset, :stop_price, "is required for stop orders")
        else
          changeset
        end

      :stop_limit ->
        changeset = if is_nil(price) do
          add_error(changeset, :price, "is required for stop limit orders")
        else
          changeset
        end

        if is_nil(stop_price) do
          add_error(changeset, :stop_price, "is required for stop limit orders")
        else
          changeset
        end

      _ ->
        changeset
    end
  end

  defp validate_time_constraints(changeset) do
    time_in_force = get_field(changeset, :time_in_force)
    expire_time = get_field(changeset, :expire_time)

    if time_in_force == :gtd && is_nil(expire_time) do
      add_error(changeset, :expire_time, "is required for GTD orders")
    else
      changeset
    end
  end

  defp calculate_remaining_quantity(changeset) do
    quantity = get_field(changeset, :quantity)
    filled_quantity = get_field(changeset, :filled_quantity) || Decimal.new(0)

    if quantity do
      remaining = Decimal.sub(quantity, filled_quantity)
      put_change(changeset, :remaining_quantity, remaining)
    else
      changeset
    end
  end
end
