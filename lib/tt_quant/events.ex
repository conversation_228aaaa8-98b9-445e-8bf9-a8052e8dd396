defmodule TtQuant.Events do
  @moduledoc """
  The Events context.

  This module provides the main API for event management,
  including order events, system events, and audit trails.
  """

  alias TtQuant.Events.OrderEvent
  alias TtQuant.Trading.Order
  alias TtQuant.Repo
  import Ecto.Query

  # Order Event Management

  @doc """
  Creates an order event.
  """
  @spec create_order_event(map()) :: {:ok, OrderEvent.t()} | {:error, Ecto.Changeset.t()}
  def create_order_event(attrs) do
    %OrderEvent{}
    |> OrderEvent.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates an order event from an order and event details.
  """
  @spec create_order_event_from_order(Order.t(), String.t(), map(), String.t()) :: {:ok, OrderEvent.t()} | {:error, Ecto.Changeset.t()}
  def create_order_event_from_order(%Order{} = order, event_type, event_data \\ %{}, source \\ "system") do
    OrderEvent.from_order(order, event_type, event_data, source)
    |> Repo.insert()
  end

  @doc """
  Gets an order event by ID.
  """
  @spec get_order_event(binary()) :: OrderEvent.t() | nil
  def get_order_event(id) do
    Repo.get(OrderEvent, id)
  end

  @doc """
  Gets an order event by ID, raising if not found.
  """
  @spec get_order_event!(binary()) :: OrderEvent.t()
  def get_order_event!(id) do
    Repo.get!(OrderEvent, id)
  end

  @doc """
  Lists order events for a specific order.
  """
  @spec list_order_events(binary()) :: [OrderEvent.t()]
  def list_order_events(order_id) do
    OrderEvent
    |> where([e], e.order_id == ^order_id)
    |> order_by([e], asc: e.timestamp)
    |> Repo.all()
  end

  @doc """
  Lists order events for an account.
  """
  @spec list_account_order_events(binary(), map()) :: [OrderEvent.t()]
  def list_account_order_events(account_id, opts \\ %{}) do
    query = from e in OrderEvent,
      join: o in Order, on: e.order_id == o.id,
      where: o.account_id == ^account_id,
      order_by: [desc: e.timestamp]

    query = apply_event_filters(query, opts)
    
    Repo.all(query)
  end

  @doc """
  Lists recent order events.
  """
  @spec list_recent_order_events(integer()) :: [OrderEvent.t()]
  def list_recent_order_events(limit \\ 100) do
    OrderEvent
    |> order_by([e], desc: e.timestamp)
    |> limit(^limit)
    |> Repo.all()
  end

  @doc """
  Gets order events by type.
  """
  @spec list_order_events_by_type(String.t(), map()) :: [OrderEvent.t()]
  def list_order_events_by_type(event_type, opts \\ %{}) do
    query = OrderEvent
    |> where([e], e.event_type == ^event_type)
    |> order_by([e], desc: e.timestamp)

    query = apply_event_filters(query, opts)
    
    Repo.all(query)
  end

  @doc """
  Gets order events by source.
  """
  @spec list_order_events_by_source(String.t(), map()) :: [OrderEvent.t()]
  def list_order_events_by_source(source, opts \\ %{}) do
    query = OrderEvent
    |> where([e], e.source == ^source)
    |> order_by([e], desc: e.timestamp)

    query = apply_event_filters(query, opts)
    
    Repo.all(query)
  end

  @doc """
  Gets order event statistics for an account.
  """
  @spec get_order_event_stats(binary(), map()) :: map()
  def get_order_event_stats(account_id, opts \\ %{}) do
    from_date = Map.get(opts, :from_date, DateTime.add(DateTime.utc_now(), -7, :day))
    to_date = Map.get(opts, :to_date, DateTime.utc_now())

    query = from e in OrderEvent,
      join: o in Order, on: e.order_id == o.id,
      where: o.account_id == ^account_id,
      where: e.timestamp >= ^from_date and e.timestamp <= ^to_date

    # Get event counts by type
    event_counts = query
    |> group_by([e], e.event_type)
    |> select([e], {e.event_type, count(e.id)})
    |> Repo.all()
    |> Enum.into(%{})

    # Get event counts by source
    source_counts = query
    |> group_by([e], e.source)
    |> select([e], {e.source, count(e.id)})
    |> Repo.all()
    |> Enum.into(%{})

    # Get total events
    total_events = query
    |> select([e], count(e.id))
    |> Repo.one()

    %{
      total_events: total_events,
      event_counts: event_counts,
      source_counts: source_counts,
      period_start: from_date,
      period_end: to_date
    }
  end

  @doc """
  Deletes old order events.
  """
  @spec delete_old_order_events(integer()) :: {integer(), nil}
  def delete_old_order_events(days_old \\ 90) do
    cutoff_date = DateTime.add(DateTime.utc_now(), -days_old, :day)
    
    from(e in OrderEvent, where: e.inserted_at < ^cutoff_date)
    |> Repo.delete_all()
  end

  # Helper functions

  defp apply_event_filters(query, opts) do
    query
    |> filter_by_date_range(opts)
    |> filter_by_limit(opts)
  end

  defp filter_by_date_range(query, %{from_date: from_date, to_date: to_date}) do
    where(query, [e], e.timestamp >= ^from_date and e.timestamp <= ^to_date)
  end
  defp filter_by_date_range(query, %{from_date: from_date}) do
    where(query, [e], e.timestamp >= ^from_date)
  end
  defp filter_by_date_range(query, %{to_date: to_date}) do
    where(query, [e], e.timestamp <= ^to_date)
  end
  defp filter_by_date_range(query, _), do: query

  defp filter_by_limit(query, %{limit: limit}) do
    limit(query, ^limit)
  end
  defp filter_by_limit(query, _), do: query
end
