defmodule TtQuant.Notifications do
  @moduledoc """
  The Notifications context.

  This module provides the main API for notification management,
  including creating, sending, and managing user notifications.
  """

  alias TtQuant.Notifications.Notification
  alias TtQuant.Repo
  import Ecto.Query

  # Notification Management

  @doc """
  Creates a notification.
  """
  @spec create_notification(map()) :: {:ok, Notification.t()} | {:error, Ecto.Changeset.t()}
  def create_notification(attrs) do
    %Notification{}
    |> Notification.changeset(attrs)
    |> Repo.insert()
    |> case do
      {:ok, notification} ->
        broadcast_notification(notification)
        {:ok, notification}
      error ->
        error
    end
  end

  @doc """
  Creates an order update notification.
  """
  @spec create_order_update_notification(binary(), String.t(), String.t(), map()) :: {:ok, Notification.t()} | {:error, Ecto.Changeset.t()}
  def create_order_update_notification(account_id, title, message, data \\ %{}) do
    Notification.order_update(account_id, title, message, data)
    |> Repo.insert()
    |> case do
      {:ok, notification} ->
        broadcast_notification(notification)
        {:ok, notification}
      error ->
        error
    end
  end

  @doc """
  Creates a trade execution notification.
  """
  @spec create_trade_execution_notification(binary(), String.t(), String.t(), map()) :: {:ok, Notification.t()} | {:error, Ecto.Changeset.t()}
  def create_trade_execution_notification(account_id, title, message, data \\ %{}) do
    Notification.trade_execution(account_id, title, message, data)
    |> Repo.insert()
    |> case do
      {:ok, notification} ->
        broadcast_notification(notification)
        {:ok, notification}
    end
  end

  @doc """
  Creates a risk alert notification.
  """
  @spec create_risk_alert_notification(binary(), String.t(), String.t(), map()) :: {:ok, Notification.t()} | {:error, Ecto.Changeset.t()}
  def create_risk_alert_notification(account_id, title, message, data \\ %{}) do
    Notification.risk_alert(account_id, title, message, data)
    |> Repo.insert()
    |> case do
      {:ok, notification} ->
        broadcast_notification(notification)
        {:ok, notification}
    end
  end

  @doc """
  Creates a margin call notification.
  """
  @spec create_margin_call_notification(binary(), String.t(), String.t(), map()) :: {:ok, Notification.t()} | {:error, Ecto.Changeset.t()}
  def create_margin_call_notification(account_id, title, message, data \\ %{}) do
    Notification.margin_call(account_id, title, message, data)
    |> Repo.insert()
    |> case do
      {:ok, notification} ->
        broadcast_notification(notification)
        {:ok, notification}
    end
  end

  @doc """
  Gets a notification by ID.
  """
  @spec get_notification(binary()) :: Notification.t() | nil
  def get_notification(id) do
    Repo.get(Notification, id)
  end

  @doc """
  Gets a notification by ID, raising if not found.
  """
  @spec get_notification!(binary()) :: Notification.t()
  def get_notification!(id) do
    Repo.get!(Notification, id)
  end

  @doc """
  Lists notifications for an account.
  """
  @spec list_notifications(binary(), map()) :: [Notification.t()]
  def list_notifications(account_id, opts \\ %{}) do
    query = Notification
    |> where([n], n.account_id == ^account_id)
    |> order_by([n], desc: n.inserted_at)

    query = apply_notification_filters(query, opts)
    
    Repo.all(query)
  end

  @doc """
  Lists unread notifications for an account.
  """
  @spec list_unread_notifications(binary(), map()) :: [Notification.t()]
  def list_unread_notifications(account_id, opts \\ %{}) do
    opts = Map.put(opts, :is_read, false)
    list_notifications(account_id, opts)
  end

  @doc """
  Lists urgent notifications for an account.
  """
  @spec list_urgent_notifications(binary(), map()) :: [Notification.t()]
  def list_urgent_notifications(account_id, opts \\ %{}) do
    opts = Map.put(opts, :priority, "urgent")
    list_notifications(account_id, opts)
  end

  @doc """
  Lists notifications by type.
  """
  @spec list_notifications_by_type(binary(), String.t(), map()) :: [Notification.t()]
  def list_notifications_by_type(account_id, notification_type, opts \\ %{}) do
    opts = Map.put(opts, :notification_type, notification_type)
    list_notifications(account_id, opts)
  end

  @doc """
  Marks a notification as read.
  """
  @spec mark_as_read(binary()) :: {:ok, Notification.t()} | {:error, Ecto.Changeset.t()}
  def mark_as_read(notification_id) do
    notification = get_notification!(notification_id)
    
    notification
    |> Notification.mark_as_read()
    |> Repo.update()
  end

  @doc """
  Marks a notification as unread.
  """
  @spec mark_as_unread(binary()) :: {:ok, Notification.t()} | {:error, Ecto.Changeset.t()}
  def mark_as_unread(notification_id) do
    notification = get_notification!(notification_id)
    
    notification
    |> Notification.mark_as_unread()
    |> Repo.update()
  end

  @doc """
  Marks all notifications as read for an account.
  """
  @spec mark_all_as_read(binary()) :: {integer(), nil}
  def mark_all_as_read(account_id) do
    now = DateTime.utc_now()
    
    from(n in Notification, 
      where: n.account_id == ^account_id and n.is_read == false)
    |> Repo.update_all(set: [is_read: true, read_at: now, updated_at: now])
  end

  @doc """
  Deletes a notification.
  """
  @spec delete_notification(binary()) :: {:ok, Notification.t()} | {:error, Ecto.Changeset.t()}
  def delete_notification(notification_id) do
    notification = get_notification!(notification_id)
    Repo.delete(notification)
  end

  @doc """
  Gets notification statistics for an account.
  """
  @spec get_notification_stats(binary(), map()) :: map()
  def get_notification_stats(account_id, opts \\ %{}) do
    from_date = Map.get(opts, :from_date, DateTime.add(DateTime.utc_now(), -7, :day))
    to_date = Map.get(opts, :to_date, DateTime.utc_now())

    query = Notification
    |> where([n], n.account_id == ^account_id)
    |> where([n], n.inserted_at >= ^from_date and n.inserted_at <= ^to_date)

    # Get counts by type
    type_counts = query
    |> group_by([n], n.notification_type)
    |> select([n], {n.notification_type, count(n.id)})
    |> Repo.all()
    |> Enum.into(%{})

    # Get counts by priority
    priority_counts = query
    |> group_by([n], n.priority)
    |> select([n], {n.priority, count(n.id)})
    |> Repo.all()
    |> Enum.into(%{})

    # Get read/unread counts
    read_stats = query
    |> group_by([n], n.is_read)
    |> select([n], {n.is_read, count(n.id)})
    |> Repo.all()
    |> Enum.into(%{})

    total_notifications = query
    |> select([n], count(n.id))
    |> Repo.one()

    unread_count = Map.get(read_stats, false, 0)
    read_count = Map.get(read_stats, true, 0)

    %{
      total_notifications: total_notifications,
      unread_count: unread_count,
      read_count: read_count,
      type_counts: type_counts,
      priority_counts: priority_counts,
      period_start: from_date,
      period_end: to_date
    }
  end

  @doc """
  Subscribes to notifications for an account.
  """
  @spec subscribe_to_notifications(binary()) :: :ok
  def subscribe_to_notifications(account_id) do
    Phoenix.PubSub.subscribe(TtQuant.PubSub, "notifications:#{account_id}")
  end

  @doc """
  Unsubscribes from notifications for an account.
  """
  @spec unsubscribe_from_notifications(binary()) :: :ok
  def unsubscribe_from_notifications(account_id) do
    Phoenix.PubSub.unsubscribe(TtQuant.PubSub, "notifications:#{account_id}")
  end

  @doc """
  Deletes old notifications.
  """
  @spec delete_old_notifications(integer()) :: {integer(), nil}
  def delete_old_notifications(days_old \\ 30) do
    cutoff_date = DateTime.add(DateTime.utc_now(), -days_old, :day)
    
    from(n in Notification, 
      where: n.inserted_at < ^cutoff_date and n.is_read == true)
    |> Repo.delete_all()
  end

  @doc """
  Deletes expired notifications.
  """
  @spec delete_expired_notifications() :: {integer(), nil}
  def delete_expired_notifications do
    now = DateTime.utc_now()
    
    from(n in Notification, 
      where: not is_nil(n.expires_at) and n.expires_at < ^now)
    |> Repo.delete_all()
  end

  # Helper functions

  defp broadcast_notification(%Notification{account_id: account_id} = notification) do
    message = %{
      type: :new_notification,
      notification: notification,
      timestamp: DateTime.utc_now()
    }

    Phoenix.PubSub.broadcast(TtQuant.PubSub, "notifications:#{account_id}", message)
  end

  defp apply_notification_filters(query, opts) do
    query
    |> filter_by_read_status(opts)
    |> filter_by_priority(opts)
    |> filter_by_type(opts)
    |> filter_by_date_range(opts)
    |> filter_by_limit(opts)
  end

  defp filter_by_read_status(query, %{is_read: is_read}) do
    where(query, [n], n.is_read == ^is_read)
  end
  defp filter_by_read_status(query, _), do: query

  defp filter_by_priority(query, %{priority: priority}) do
    where(query, [n], n.priority == ^priority)
  end
  defp filter_by_priority(query, _), do: query

  defp filter_by_type(query, %{notification_type: notification_type}) do
    where(query, [n], n.notification_type == ^notification_type)
  end
  defp filter_by_type(query, _), do: query

  defp filter_by_date_range(query, %{from_date: from_date, to_date: to_date}) do
    where(query, [n], n.inserted_at >= ^from_date and n.inserted_at <= ^to_date)
  end
  defp filter_by_date_range(query, %{from_date: from_date}) do
    where(query, [n], n.inserted_at >= ^from_date)
  end
  defp filter_by_date_range(query, %{to_date: to_date}) do
    where(query, [n], n.inserted_at <= ^to_date)
  end
  defp filter_by_date_range(query, _), do: query

  defp filter_by_limit(query, %{limit: limit}) do
    limit(query, ^limit)
  end
  defp filter_by_limit(query, _), do: query
end
