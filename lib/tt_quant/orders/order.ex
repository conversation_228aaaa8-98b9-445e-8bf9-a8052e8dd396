defmodule TtQuant.Orders.Order do
  @moduledoc """
  Order schema for database persistence
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :id, autogenerate: true}
  @foreign_key_type :id

  @sides ~w(buy sell)
  @order_types ~w(market limit stop stop_limit trailing_stop iceberg)
  @statuses ~w(initialized submitted accepted partially_filled filled cancelled expired rejected pending_update pending_cancel)
  @time_in_forces ~w(GTC IOC FOK GTD DAY)
  @liquidity_sides ~w(maker taker)

  @primary_key {:id, :binary_id, autogenerate: true}
  schema "orders" do
    field :client_order_id, :string
    field :venue_order_id, :string
    field :account_id, :binary_id
    field :instrument_id, :string
    field :exchange, :string
    field :side, :string
    field :order_type, :string
    field :status, :string

    field :quantity, :decimal
    field :filled_quantity, :decimal, default: Decimal.new(0)
    field :remaining_quantity, :decimal

    field :price, :decimal
    field :stop_price, :decimal
    field :avg_fill_price, :decimal

    field :time_in_force, :string
    field :expire_time, :utc_datetime_usec
    field :currency, :string

    field :metadata, :map, default: %{}
    field :tags, {:array, :string}, default: []
    field :rust_order_data, :binary

    has_many :fills, TtQuant.Orders.OrderFill

    timestamps(type: :utc_datetime_usec)
  end

  @doc false
  def changeset(order, attrs) do
    order
    |> cast(attrs, [
      :client_order_id, :venue_order_id, :account_id, :instrument_id, :exchange,
      :side, :order_type, :status, :quantity, :filled_quantity,
      :remaining_quantity, :price, :stop_price, :avg_fill_price,
      :time_in_force, :expire_time, :currency, :metadata, :tags, :rust_order_data
    ])
    |> validate_required([
      :client_order_id, :instrument_id, :exchange, :side,
      :order_type, :status, :quantity, :remaining_quantity, :currency
    ])

    |> validate_inclusion(:side, @sides)
    |> validate_inclusion(:order_type, @order_types)
    |> validate_inclusion(:status, @statuses)
    |> validate_inclusion(:time_in_force, @time_in_forces, allow_nil: true)
    |> validate_number(:quantity, greater_than: 0)
    |> validate_number(:filled_quantity, greater_than_or_equal_to: 0)
    |> validate_number(:remaining_quantity, greater_than_or_equal_to: 0)
    |> validate_price()
    |> validate_stop_price()
    |> unique_constraint(:client_order_id)
    |> validate_order_consistency()
  end

  defp validate_order_consistency(changeset) do
    case {get_field(changeset, :order_type), get_field(changeset, :price)} do
      {"limit", nil} ->
        add_error(changeset, :price, "is required for limit orders")
      {"stop", nil} ->
        add_error(changeset, :stop_price, "is required for stop orders")
      {"stop_limit", nil} ->
        changeset
        |> validate_required([:price, :stop_price])
      _ ->
        changeset
    end
    |> validate_quantity_consistency()
  end

  defp validate_quantity_consistency(changeset) do
    quantity = get_field(changeset, :quantity)
    filled = get_field(changeset, :filled_quantity)
    remaining = get_field(changeset, :remaining_quantity)

    if quantity && filled && remaining do
      expected_remaining = Decimal.sub(quantity, filled)

      if Decimal.equal?(remaining, expected_remaining) do
        changeset
      else
        add_error(changeset, :remaining_quantity,
          "must equal quantity minus filled_quantity")
      end
    else
      changeset
    end
  end

  @doc """
  Check if order is in an active state
  """
  def active?(%__MODULE__{status: status}) do
    status in ~w(submitted accepted partially_filled pending_update pending_cancel)
  end

  @doc """
  Check if order is in a terminal state
  """
  def terminal?(%__MODULE__{status: status}) do
    status in ~w(filled cancelled expired rejected)
  end

  @doc """
  Check if order is fully filled
  """
  def filled?(%__MODULE__{remaining_quantity: remaining}) do
    Decimal.equal?(remaining, Decimal.new(0))
  end

  @doc """
  Update order with fill
  """
  def apply_fill(%__MODULE__{} = order, fill_quantity, fill_price) do
    new_filled = Decimal.add(order.filled_quantity, fill_quantity)
    new_remaining = Decimal.sub(order.quantity, new_filled)

    # Calculate new average fill price
    total_value = Decimal.add(
      Decimal.mult(order.filled_quantity, order.avg_fill_price || Decimal.new(0)),
      Decimal.mult(fill_quantity, fill_price)
    )
    new_avg_price = Decimal.div(total_value, new_filled)

    new_status = if Decimal.equal?(new_remaining, Decimal.new(0)) do
      "filled"
    else
      "partially_filled"
    end

    %{order |
      filled_quantity: new_filled,
      remaining_quantity: new_remaining,
      avg_fill_price: new_avg_price,
      status: new_status
    }
  end

  defp validate_price(changeset) do
    case get_field(changeset, :price) do
      nil -> changeset
      price when is_struct(price, Decimal) ->
        if Decimal.positive?(price) do
          changeset
        else
          add_error(changeset, :price, "must be greater than 0")
        end
      _ -> add_error(changeset, :price, "must be a valid decimal")
    end
  end

  defp validate_stop_price(changeset) do
    case get_field(changeset, :stop_price) do
      nil -> changeset
      stop_price when is_struct(stop_price, Decimal) ->
        if Decimal.positive?(stop_price) do
          changeset
        else
          add_error(changeset, :stop_price, "must be greater than 0")
        end
      _ -> add_error(changeset, :stop_price, "must be a valid decimal")
    end
  end



  @doc """
  Validate state transition
  """
  def valid_transition?(from_status, to_status) do
    transitions = %{
      "initialized" => ~w(submitted rejected cancelled),
      "submitted" => ~w(accepted rejected cancelled),
      "accepted" => ~w(partially_filled filled cancelled expired pending_update pending_cancel),
      "partially_filled" => ~w(partially_filled filled cancelled expired pending_update pending_cancel),
      "pending_update" => ~w(accepted partially_filled rejected),
      "pending_cancel" => ~w(cancelled accepted partially_filled),
      "filled" => [],
      "cancelled" => [],
      "expired" => [],
      "rejected" => []
    }

    allowed = Map.get(transitions, from_status, [])
    to_status in allowed
  end
end
