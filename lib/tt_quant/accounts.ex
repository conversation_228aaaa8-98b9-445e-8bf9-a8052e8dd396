defmodule TtQuant.Accounts do
  @moduledoc """
  The Accounts context.

  This module provides the main API for account and session management,
  including authentication, session tracking, and user management.
  """

  alias TtQuant.Accounts.Session
  alias TtQuant.Portfolio.Account
  alias TtQuant.Repo
  import Ecto.Query

  # Session Management

  @doc """
  Creates a new session.
  """
  @spec create_session(binary() | nil, String.t(), map()) :: {:ok, Session.t()} | {:error, Ecto.Changeset.t()}
  def create_session(account_id, session_type, opts \\ %{}) do
    Session.create_session(account_id, session_type, opts)
    |> Repo.insert()
  end

  @doc """
  Gets a session by ID.
  """
  @spec get_session(binary()) :: Session.t() | nil
  def get_session(id) do
    Repo.get(Session, id)
  end

  @doc """
  Gets a session by token.
  """
  @spec get_session_by_token(String.t()) :: Session.t() | nil
  def get_session_by_token(token) do
    Session
    |> where([s], s.session_token == ^token)
    |> Repo.one()
  end

  @doc """
  Gets an active session by token.
  """
  @spec get_active_session_by_token(String.t()) :: Session.t() | nil
  def get_active_session_by_token(token) do
    now = DateTime.utc_now()
    
    Session
    |> where([s], s.session_token == ^token)
    |> where([s], s.is_active == true)
    |> where([s], s.expires_at > ^now)
    |> Repo.one()
  end

  @doc """
  Lists sessions for an account.
  """
  @spec list_sessions(binary(), map()) :: [Session.t()]
  def list_sessions(account_id, opts \\ %{}) do
    query = Session
    |> where([s], s.account_id == ^account_id)
    |> order_by([s], desc: s.last_activity_at)

    query = apply_session_filters(query, opts)
    
    Repo.all(query)
  end

  @doc """
  Lists active sessions for an account.
  """
  @spec list_active_sessions(binary()) :: [Session.t()]
  def list_active_sessions(account_id) do
    now = DateTime.utc_now()
    
    Session
    |> where([s], s.account_id == ^account_id)
    |> where([s], s.is_active == true)
    |> where([s], s.expires_at > ^now)
    |> order_by([s], desc: s.last_activity_at)
    |> Repo.all()
  end

  @doc """
  Updates session activity.
  """
  @spec update_session_activity(String.t()) :: {:ok, Session.t()} | {:error, :not_found}
  def update_session_activity(session_token) do
    case get_session_by_token(session_token) do
      nil ->
        {:error, :not_found}
      
      session ->
        session
        |> Session.update_activity()
        |> Repo.update()
    end
  end

  @doc """
  Deactivates a session.
  """
  @spec deactivate_session(String.t()) :: {:ok, Session.t()} | {:error, :not_found}
  def deactivate_session(session_token) do
    case get_session_by_token(session_token) do
      nil ->
        {:error, :not_found}
      
      session ->
        session
        |> Session.deactivate()
        |> Repo.update()
    end
  end

  @doc """
  Deactivates all sessions for an account.
  """
  @spec deactivate_all_sessions(binary()) :: {integer(), nil}
  def deactivate_all_sessions(account_id) do
    now = DateTime.utc_now()
    
    from(s in Session, where: s.account_id == ^account_id and s.is_active == true)
    |> Repo.update_all(set: [is_active: false, updated_at: now])
  end

  @doc """
  Validates a session token and returns the associated account.
  """
  @spec validate_session_token(String.t()) :: {:ok, Account.t()} | {:error, :invalid_session}
  def validate_session_token(token) do
    case get_active_session_by_token(token) do
      nil ->
        {:error, :invalid_session}
      
      %Session{account_id: nil} ->
        {:error, :invalid_session}
      
      %Session{account_id: account_id} ->
        case Repo.get(Account, account_id) do
          nil -> {:error, :invalid_session}
          account -> {:ok, account}
        end
    end
  end

  @doc """
  Cleans up expired sessions.
  """
  @spec cleanup_expired_sessions() :: {integer(), nil}
  def cleanup_expired_sessions do
    now = DateTime.utc_now()
    
    from(s in Session, where: s.expires_at <= ^now)
    |> Repo.delete_all()
  end

  @doc """
  Gets session statistics for an account.
  """
  @spec get_session_stats(binary(), map()) :: map()
  def get_session_stats(account_id, opts \\ %{}) do
    from_date = Map.get(opts, :from_date, DateTime.add(DateTime.utc_now(), -7, :day))
    to_date = Map.get(opts, :to_date, DateTime.utc_now())

    query = Session
    |> where([s], s.account_id == ^account_id)
    |> where([s], s.inserted_at >= ^from_date and s.inserted_at <= ^to_date)

    # Get session counts by type
    type_counts = query
    |> group_by([s], s.session_type)
    |> select([s], {s.session_type, count(s.id)})
    |> Repo.all()
    |> Enum.into(%{})

    # Get active session count
    active_sessions = Session
    |> where([s], s.account_id == ^account_id)
    |> where([s], s.is_active == true)
    |> where([s], s.expires_at > ^DateTime.utc_now())
    |> select([s], count(s.id))
    |> Repo.one()

    # Get total sessions
    total_sessions = query
    |> select([s], count(s.id))
    |> Repo.one()

    %{
      total_sessions: total_sessions,
      active_sessions: active_sessions,
      type_counts: type_counts,
      period_start: from_date,
      period_end: to_date
    }
  end

  @doc """
  Creates a web session for an account.
  """
  @spec create_web_session(binary(), map()) :: {:ok, Session.t()} | {:error, Ecto.Changeset.t()}
  def create_web_session(account_id, opts \\ %{}) do
    create_session(account_id, "web", opts)
  end

  @doc """
  Creates an API session for an account.
  """
  @spec create_api_session(binary(), map()) :: {:ok, Session.t()} | {:error, Ecto.Changeset.t()}
  def create_api_session(account_id, opts \\ %{}) do
    create_session(account_id, "api", opts)
  end

  @doc """
  Creates a WebSocket session for an account.
  """
  @spec create_websocket_session(binary(), map()) :: {:ok, Session.t()} | {:error, Ecto.Changeset.t()}
  def create_websocket_session(account_id, opts \\ %{}) do
    create_session(account_id, "websocket", opts)
  end

  @doc """
  Creates a mobile session for an account.
  """
  @spec create_mobile_session(binary(), map()) :: {:ok, Session.t()} | {:error, Ecto.Changeset.t()}
  def create_mobile_session(account_id, opts \\ %{}) do
    create_session(account_id, "mobile", opts)
  end

  # Helper functions

  defp apply_session_filters(query, opts) do
    query
    |> filter_by_session_type(opts)
    |> filter_by_active_status(opts)
    |> filter_by_date_range(opts)
    |> filter_by_limit(opts)
  end

  defp filter_by_session_type(query, %{session_type: session_type}) do
    where(query, [s], s.session_type == ^session_type)
  end
  defp filter_by_session_type(query, _), do: query

  defp filter_by_active_status(query, %{is_active: is_active}) do
    where(query, [s], s.is_active == ^is_active)
  end
  defp filter_by_active_status(query, _), do: query

  defp filter_by_date_range(query, %{from_date: from_date, to_date: to_date}) do
    where(query, [s], s.inserted_at >= ^from_date and s.inserted_at <= ^to_date)
  end
  defp filter_by_date_range(query, %{from_date: from_date}) do
    where(query, [s], s.inserted_at >= ^from_date)
  end
  defp filter_by_date_range(query, %{to_date: to_date}) do
    where(query, [s], s.inserted_at <= ^to_date)
  end
  defp filter_by_date_range(query, _), do: query

  defp filter_by_limit(query, %{limit: limit}) do
    limit(query, ^limit)
  end
  defp filter_by_limit(query, _), do: query
end
