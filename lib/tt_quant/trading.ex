defmodule TtQuant.Trading do
  @moduledoc """
  The Trading context.

  This module provides the main API for trading operations,
  including order management, trade execution, and order routing.
  """

  alias TtQuant.Trading.{Order, OrderManager, Trade, Supervisor}
  alias TtQuant.Repo
  import Ecto.Query

  # Order Management

  @doc """
  Creates and submits a new order.
  """
  @spec create_order(binary(), map()) :: {:ok, Order.t()} | {:error, any()}
  def create_order(account_id, order_params) do
    # Ensure order manager is running
    ensure_order_manager(account_id)

    # Submit order through manager
    OrderManager.submit_order(account_id, order_params)
  end

  @doc """
  Creates a market buy order.
  """
  @spec market_buy(binary(), String.t(), Decimal.t(), String.t()) :: {:ok, Order.t()} | {:error, any()}
  def market_buy(account_id, instrument_id, quantity, currency) do
    order_params = %{
      client_order_id: generate_client_order_id(),
      instrument_id: instrument_id,
      side: :buy,
      order_type: :market,
      quantity: quantity,
      currency: currency,
      time_in_force: :ioc
    }

    create_order(account_id, order_params)
  end

  @doc """
  Creates a market sell order.
  """
  @spec market_sell(binary(), String.t(), Decimal.t(), String.t()) :: {:ok, Order.t()} | {:error, any()}
  def market_sell(account_id, instrument_id, quantity, currency) do
    order_params = %{
      client_order_id: generate_client_order_id(),
      instrument_id: instrument_id,
      side: :sell,
      order_type: :market,
      quantity: quantity,
      currency: currency,
      time_in_force: :ioc
    }

    create_order(account_id, order_params)
  end

  @doc """
  Creates a limit buy order.
  """
  @spec limit_buy(binary(), String.t(), Decimal.t(), Decimal.t(), String.t()) :: {:ok, Order.t()} | {:error, any()}
  def limit_buy(account_id, instrument_id, quantity, price, currency) do
    order_params = %{
      client_order_id: generate_client_order_id(),
      instrument_id: instrument_id,
      side: :buy,
      order_type: :limit,
      quantity: quantity,
      price: price,
      currency: currency,
      time_in_force: :gtc
    }

    create_order(account_id, order_params)
  end

  @doc """
  Creates a limit sell order.
  """
  @spec limit_sell(binary(), String.t(), Decimal.t(), Decimal.t(), String.t()) :: {:ok, Order.t()} | {:error, any()}
  def limit_sell(account_id, instrument_id, quantity, price, currency) do
    order_params = %{
      client_order_id: generate_client_order_id(),
      instrument_id: instrument_id,
      side: :sell,
      order_type: :limit,
      quantity: quantity,
      price: price,
      currency: currency,
      time_in_force: :gtc
    }

    create_order(account_id, order_params)
  end

  @doc """
  Creates a stop loss order.
  """
  @spec stop_loss(binary(), String.t(), Decimal.t(), Decimal.t(), :buy | :sell, String.t()) :: {:ok, Order.t()} | {:error, any()}
  def stop_loss(account_id, instrument_id, quantity, stop_price, side, currency) do
    order_params = %{
      client_order_id: generate_client_order_id(),
      instrument_id: instrument_id,
      side: side,
      order_type: :stop,
      quantity: quantity,
      stop_price: stop_price,
      currency: currency,
      time_in_force: :gtc
    }

    create_order(account_id, order_params)
  end

  @doc """
  Cancels an order.
  """
  @spec cancel_order(binary(), String.t()) :: {:ok, Order.t()} | {:error, any()}
  def cancel_order(account_id, client_order_id) do
    ensure_order_manager(account_id)
    OrderManager.cancel_order(account_id, client_order_id)
  end

  @doc """
  Updates an order.
  """
  @spec update_order(binary(), String.t(), map()) :: {:ok, Order.t()} | {:error, any()}
  def update_order(account_id, client_order_id, updates) do
    ensure_order_manager(account_id)
    OrderManager.update_order(account_id, client_order_id, updates)
  end

  @doc """
  Gets an order by client order ID.
  """
  @spec get_order(binary(), String.t()) :: Order.t() | nil
  def get_order(account_id, client_order_id) do
    # Try to get from order manager first (for active orders)
    case ensure_order_manager(account_id) do
      {:ok, _pid} ->
        case OrderManager.get_order(account_id, client_order_id) do
          nil -> get_order_from_db(account_id, client_order_id)
          order -> order
        end

      {:error, _} ->
        get_order_from_db(account_id, client_order_id)
    end
  end

  @doc """
  Gets all active orders for an account.
  """
  @spec list_active_orders(binary()) :: [Order.t()]
  def list_active_orders(account_id) do
    case ensure_order_manager(account_id) do
      {:ok, _pid} -> OrderManager.get_active_orders(account_id)
      {:error, _} -> []
    end
  end

  @doc """
  Gets order history for an account.
  """
  @spec list_order_history(binary(), map()) :: [Order.t()]
  def list_order_history(account_id, opts \\ %{}) do
    query = Order
    |> where([o], o.account_id == ^account_id)
    |> order_by([o], desc: o.inserted_at)

    query = case Map.get(opts, :limit) do
      nil -> query
      limit -> limit(query, ^limit)
    end

    query = case Map.get(opts, :status) do
      nil -> query
      status -> where(query, [o], o.status == ^status)
    end

    query = case Map.get(opts, :instrument_id) do
      nil -> query
      instrument_id -> where(query, [o], o.instrument_id == ^instrument_id)
    end

    Repo.all(query)
  end

  # Trade Management

  @doc """
  Creates a trade record.
  """
  @spec create_trade(map()) :: {:ok, Trade.t()} | {:error, Ecto.Changeset.t()}
  def create_trade(attrs) do
    %Trade{}
    |> Trade.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Creates a trade from order fill.
  """
  @spec create_trade_from_fill(Order.t(), Decimal.t(), Decimal.t(), map()) :: {:ok, Trade.t()} | {:error, Ecto.Changeset.t()}
  def create_trade_from_fill(%Order{} = order, fill_quantity, fill_price, opts \\ %{}) do
    Trade.from_order_fill(order, fill_quantity, fill_price, opts)
    |> Repo.insert()
  end

  @doc """
  Gets trade history for an account.
  """
  @spec list_trade_history(binary(), map()) :: [Trade.t()]
  def list_trade_history(account_id, opts \\ %{}) do
    query = Trade
    |> where([t], t.account_id == ^account_id)
    |> order_by([t], desc: t.executed_at)

    query = case Map.get(opts, :limit) do
      nil -> query
      limit -> limit(query, ^limit)
    end

    query = case Map.get(opts, :instrument_id) do
      nil -> query
      instrument_id -> where(query, [t], t.instrument_id == ^instrument_id)
    end

    query = case Map.get(opts, :from_date) do
      nil -> query
      from_date -> where(query, [t], t.executed_at >= ^from_date)
    end

    query = case Map.get(opts, :to_date) do
      nil -> query
      to_date -> where(query, [t], t.executed_at <= ^to_date)
    end

    Repo.all(query)
  end

  @doc """
  Gets trades for a specific order.
  """
  @spec list_order_trades(binary()) :: [Trade.t()]
  def list_order_trades(order_id) do
    Trade
    |> where([t], t.order_id == ^order_id)
    |> order_by([t], asc: t.executed_at)
    |> Repo.all()
  end

  # Fill Processing

  @doc """
  Processes an order fill.
  """
  @spec process_fill(binary(), String.t(), Decimal.t(), Decimal.t(), map()) :: :ok
  def process_fill(account_id, client_order_id, fill_quantity, fill_price, opts \\ %{}) do
    ensure_order_manager(account_id)
    OrderManager.process_fill(account_id, client_order_id, fill_quantity, fill_price)

    # Create trade record if order exists
    case get_order(account_id, client_order_id) do
      nil -> :ok
      order ->
        create_trade_from_fill(order, fill_quantity, fill_price, opts)
        :ok
    end
  end

  @doc """
  Updates order status.
  """
  @spec update_order_status(binary(), String.t(), Order.order_status()) :: :ok
  def update_order_status(account_id, client_order_id, new_status) do
    ensure_order_manager(account_id)
    OrderManager.update_order_status(account_id, client_order_id, new_status)
  end

  # Analytics and Reporting

  @doc """
  Gets trading statistics for an account.
  """
  @spec get_trading_stats(binary(), DateTime.t(), DateTime.t()) :: map()
  def get_trading_stats(account_id, start_date, end_date) do
    trades = list_trade_history(account_id, %{from_date: start_date, to_date: end_date})

    total_trades = length(trades)
    total_volume = trades
    |> Enum.reduce(Decimal.new(0), fn trade, acc ->
      Decimal.add(acc, trade.value)
    end)

    total_commission = trades
    |> Enum.reduce(Decimal.new(0), fn trade, acc ->
      Decimal.add(acc, trade.commission)
    end)

    buy_trades = Enum.count(trades, &Trade.buy?/1)
    sell_trades = Enum.count(trades, &Trade.sell?/1)

    instruments_traded = trades
    |> Enum.map(& &1.instrument_id)
    |> Enum.uniq()
    |> length()

    %{
      total_trades: total_trades,
      total_volume: total_volume,
      total_commission: total_commission,
      buy_trades: buy_trades,
      sell_trades: sell_trades,
      instruments_traded: instruments_traded,
      period_start: start_date,
      period_end: end_date
    }
  end

  # Subscription Management

  @doc """
  Subscribes to order updates for an account.
  """
  @spec subscribe_to_orders(binary()) :: :ok
  def subscribe_to_orders(account_id) do
    ensure_order_manager(account_id)
    OrderManager.subscribe(account_id)
  end

  @doc """
  Unsubscribes from order updates for an account.
  """
  @spec unsubscribe_from_orders(binary()) :: :ok
  def unsubscribe_from_orders(account_id) do
    OrderManager.unsubscribe(account_id)
  end

  # Private helper functions

  defp ensure_order_manager(account_id) do
    Supervisor.ensure_order_manager(account_id)
  end

  defp get_order_from_db(account_id, client_order_id) do
    Order
    |> where([o], o.account_id == ^account_id and o.client_order_id == ^client_order_id)
    |> Repo.one()
  end

  defp generate_client_order_id do
    timestamp = DateTime.utc_now() |> DateTime.to_unix(:microsecond)
    random = :rand.uniform(9999)
    "order_#{timestamp}_#{random}"
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking order changes.
  """
  @spec change_order(Order.t() | map(), map()) :: Ecto.Changeset.t()
  def change_order(order_or_attrs, attrs \\ %{})
  def change_order(%Order{} = order, attrs) do
    Order.changeset(order, attrs)
  end
  def change_order(attrs, _) when is_map(attrs) do
    Order.changeset(%Order{}, attrs)
  end
end
